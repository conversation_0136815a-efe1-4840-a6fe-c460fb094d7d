# KeepAlive Module Migration Matrix

This document outlines the migration strategy from the legacy KeepAlive API to the modern Kotlin-based API using sealed classes, Flow, and DSL.

## Migration Strategy Overview

### 1. Enums → Sealed Classes

| Legacy Enum | New Sealed Class | Migration Path |
|-------------|------------------|----------------|
| `KeepAliveStatus.UNKNOWN` | `KeepAliveStatus.Unknown` | Direct replacement with backward compatibility |
| `KeepAliveStatus.ACTIVE` | `KeepAliveStatus.Active` | Direct replacement with backward compatibility |
| `KeepAliveStatus.INACTIVE` | `KeepAliveStatus.Inactive` | Direct replacement with backward compatibility |
| `KeepAliveStatus.ACTIVATING` | `KeepAliveStatus.Activating` | Direct replacement with backward compatibility |
| `KeepAliveStatus.FAILED` | `KeepAliveStatus.Failed` | Direct replacement with backward compatibility |
| `KeepAliveStatus.DISABLED` | `KeepAliveStatus.Disabled` | Direct replacement with backward compatibility |

| Legacy Enum | New Sealed Class | Migration Path |
|-------------|------------------|----------------|
| `KeepAliveEvent.STARTED` | `KeepAliveEvent.Started` | Direct replacement with backward compatibility |
| `KeepAliveEvent.STOPPED` | `KeepAliveEvent.Stopped` | Direct replacement with backward compatibility |
| `KeepAliveEvent.PACKAGE_ACTIVATED` | `KeepAliveEvent.PackageActivated` | Direct replacement with backward compatibility |
| `KeepAliveEvent.PACKAGE_STOPPED` | `KeepAliveEvent.PackageStopped` | Direct replacement with backward compatibility |
| `KeepAliveEvent.CHECK_COMPLETED` | `KeepAliveEvent.CheckCompleted` | Direct replacement with backward compatibility |
| `KeepAliveEvent.WHITELIST_ADDED` | `KeepAliveEvent.WhitelistAdded` | Direct replacement with backward compatibility |
| `KeepAliveEvent.WHITELIST_REMOVED` | `KeepAliveEvent.WhitelistRemoved` | Direct replacement with backward compatibility |
| `KeepAliveEvent.ERROR_OCCURRED` | `KeepAliveEvent.ErrorOccurred` | Direct replacement with backward compatibility |

| Legacy Enum | New Sealed Class | Migration Path |
|-------------|------------------|----------------|
| `KeepAliveStrategy.PACKAGE_ACTIVATION` | `KeepAliveStrategy.PackageActivation` | Direct replacement with backward compatibility |
| `KeepAliveStrategy.PERIODIC_CHECK` | `KeepAliveStrategy.PeriodicCheck` | Direct replacement with backward compatibility |
| `KeepAliveStrategy.BROADCAST_RECEIVER` | `KeepAliveStrategy.BroadcastReceiver` | Direct replacement with backward compatibility |
| `KeepAliveStrategy.DOZE_WHITELIST` | `KeepAliveStrategy.DozeWhitelist` | Direct replacement with backward compatibility |

### 2. Callbacks → Flow

| Legacy Pattern | New Pattern | Migration Path |
|----------------|-------------|----------------|
| `KeepAliveCallback` interface | `KeepAliveEventFlow` | Gradual migration with bridge support |
| `callback.onStatusChanged()` | `eventFlow.statusChanges.collect{}` | Flow-based reactive pattern |
| `callback.onEvent()` | `eventFlow.events.collect{}` | Flow-based reactive pattern |
| `callback.onError()` | `eventFlow.errors.collect{}` | Flow-based reactive pattern |
| `callback.onStatistics()` | `eventFlow.statistics.collect{}` | Flow-based reactive pattern |

### 3. Config Objects → DSL

| Legacy Pattern | New DSL Pattern | Migration Path |
|----------------|-----------------|----------------|
| Manual config creation | DSL builder pattern | Enhanced developer experience |
| `KeepAliveConfig()` constructor | `keepAliveConfig { }` | Type-safe DSL |
| Individual config classes | Nested DSL builders | Hierarchical configuration |

## Detailed Migration Guide

### Phase 1: Backward Compatibility (Current State)

All legacy APIs remain functional with deprecation warnings:

```kotlin
// Legacy code continues to work
val status = KeepAliveStatus.ACTIVE
val event = KeepAliveEvent.STARTED
val strategy = KeepAliveStrategy.PACKAGE_ACTIVATION

// Deprecation warnings guide developers to new API
```

### Phase 2: Modern API Introduction

#### 2.1 Sealed Classes Usage

```kotlin
// Old way
when (status) {
    KeepAliveStatus.ACTIVE -> handleActive()
    KeepAliveStatus.INACTIVE -> handleInactive()
    // ...
}

// New way
when (status) {
    is KeepAliveStatus.Active -> handleActive()
    is KeepAliveStatus.Inactive -> handleInactive()
    // Exhaustive when expressions
}
```

#### 2.2 Flow-based Events

```kotlin
// Old callback approach
val callback = object : KeepAliveCallback {
    override fun onStatusChanged(packageName: String, status: KeepAliveStatus, strategy: KeepAliveStrategy) {
        // Handle status change
    }
    override fun onEvent(event: KeepAliveEvent, packageName: String, message: String, extra: Map<String, Any>) {
        // Handle event
    }
}

// New Flow approach
launch {
    keepAliveManager.eventFlow.statusChanges.collect { event ->
        println("Package ${event.packageName} status changed to ${event.status}")
    }
}

launch {
    keepAliveManager.eventFlow.events
        .filter { it.event == KeepAliveEvent.PackageActivated }
        .collect { event ->
            println("Package activated: ${event.packageName}")
        }
}
```

#### 2.3 DSL Configuration

```kotlin
// Old configuration
val config = KeepAliveConfig(
    targetPackages = listOf("com.example.app1", "com.example.app2"),
    periodicCheck = PeriodicCheckConfig(
        enabled = true,
        checkInterval = 30000L,
        maxRetryCount = 3
    ),
    packageActivation = PackageActivationConfig(
        enabled = true,
        activationMethod = PackageActivationConfig.ActivationMethod.ACTIVITY
    )
)

// New DSL configuration
val config = keepAliveConfig {
    packages("com.example.app1", "com.example.app2")
    
    periodicCheck {
        enabled = true
        checkIntervalMs = 30000L
        maxRetryCount = 3
    }
    
    packageActivation {
        enabled = true
        useActivity()
    }
    
    statistics(enabled = true, intervalMs = 60000L)
}

// Predefined configurations
val lightweightConfig = KeepAliveConfig.lightweight("com.example.app")
val aggressiveConfig = KeepAliveConfig.aggressive("com.example.app")
```

### Phase 3: Migration Utilities

#### 3.1 Bridge Support

For gradual migration, a bridge class converts between Flow and callback patterns:

```kotlin
// Bridge legacy callbacks to Flow
val bridge = FlowToCallbackBridge(eventFlow, legacyCallback)
launch { bridge.startBridging() }
```

#### 3.2 Extension Functions

```kotlin
// Convenient filtering and collection
launch {
    eventFlow.statusChangesFor("com.example.app").collect { event ->
        // Handle status changes for specific package
    }
}

launch {
    eventFlow.eventsOfType(KeepAliveEvent.PackageActivated).collect { event ->
        // Handle only package activation events
    }
}
```

## Migration Timeline

### Immediate (Phase 1)
- ✅ Sealed classes implemented with backward compatibility
- ✅ Flow system implemented alongside callbacks
- ✅ DSL configuration system created
- ✅ Bridge utilities for smooth transition

### Short Term (1-2 months)
- Deprecation warnings added to legacy APIs
- Documentation updated with new patterns
- Example migration guides provided
- Training for development teams

### Medium Term (3-6 months)
- Legacy callback interfaces marked as deprecated
- New code required to use Flow-based APIs
- Migration tools and automated refactoring support

### Long Term (6+ months)
- Complete removal of legacy callback interfaces
- Full adoption of sealed classes and Flow patterns
- Performance optimizations for new architecture

## Benefits of Migration

### 1. Type Safety
- Sealed classes provide exhaustive when expressions
- Compile-time safety for event handling
- Better IDE support and autocomplete

### 2. Reactive Programming
- Flow-based events enable reactive patterns
- Better composition and transformation of event streams
- Built-in backpressure handling

### 3. Developer Experience
- DSL provides intuitive configuration
- Reduced boilerplate code
- Better readability and maintainability

### 4. Future Extensibility
- Sealed classes allow adding new types without breaking existing code
- Flow patterns enable complex event processing
- DSL can be extended with new configuration options

## Call Site Updates Required

### In KeepAliveService
- Update AIDL interfaces to support new enum values
- Add Flow-to-AIDL bridge for cross-process communication
- Maintain backward compatibility for existing clients

### In KeepAliveClient
- Update connection handling to support new event types
- Add Flow-based event subscription methods
- Provide migration utilities for existing implementations

### In Application Code
- Gradually migrate from callbacks to Flow collection
- Update configuration creation to use DSL
- Use new sealed class pattern matching

## Risk Mitigation

### Backward Compatibility
- All legacy APIs remain functional during transition
- Deprecation warnings provide clear migration paths
- Bridge classes ensure smooth integration

### Testing Strategy
- Comprehensive test suite for new APIs
- Migration tests to verify compatibility
- Performance tests to ensure no regression

### Documentation
- Detailed migration guides
- Code examples for each pattern
- Best practices documentation

## Conclusion

This migration strategy provides a smooth transition from legacy callback-based APIs to modern Kotlin patterns while maintaining full backward compatibility. The phased approach allows teams to migrate at their own pace while immediately benefiting from improved type safety and developer experience.
