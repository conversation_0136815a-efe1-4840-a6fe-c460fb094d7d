package com.aispeech.keepalive.dsl

import com.aispeech.keepalive.*

/**
 * Kotlin DSL for KeepAlive configuration
 * Provides a more intuitive and type-safe way to configure keep-alive settings
 */

@DslMarker
annotation class KeepAliveDsl

/**
 * DSL builder for KeepAlive configuration
 */
@KeepAliveDsl
class KeepAliveConfigBuilder {
    private var targetPackages: MutableList<String> = mutableListOf()
    private var periodicCheckConfig: PeriodicCheckConfig = PeriodicCheckConfig()
    private var packageActivationConfig: PackageActivationConfig = PackageActivationConfig()
    private var dozeWhitelistConfig: DozeWhitelistConfig = DozeWhitelistConfig()
    private var broadcastReceiverConfig: BroadcastReceiverConfig = BroadcastReceiverConfig()
    private var enableStatistics: Boolean = true
    private var statisticsInterval: Long = 60000L

    /**
     * Add target packages to keep alive
     */
    fun packages(vararg packages: String) {
        targetPackages.addAll(packages)
    }

    /**
     * Add target packages from a list
     */
    fun packages(packages: List<String>) {
        targetPackages.addAll(packages)
    }

    /**
     * Configure periodic check strategy
     */
    fun periodicCheck(builder: PeriodicCheckBuilder.() -> Unit) {
        periodicCheckConfig = PeriodicCheckBuilder().apply(builder).build()
    }

    /**
     * Configure package activation strategy
     */
    fun packageActivation(builder: PackageActivationBuilder.() -> Unit) {
        packageActivationConfig = PackageActivationBuilder().apply(builder).build()
    }

    /**
     * Configure doze whitelist strategy
     */
    fun dozeWhitelist(builder: DozeWhitelistBuilder.() -> Unit) {
        dozeWhitelistConfig = DozeWhitelistBuilder().apply(builder).build()
    }

    /**
     * Configure broadcast receiver strategy
     */
    fun broadcastReceiver(builder: BroadcastReceiverBuilder.() -> Unit) {
        broadcastReceiverConfig = BroadcastReceiverBuilder().apply(builder).build()
    }

    /**
     * Configure statistics
     */
    fun statistics(enabled: Boolean = true, intervalMs: Long = 60000L) {
        enableStatistics = enabled
        statisticsInterval = intervalMs
    }

    /**
     * Build the final configuration
     */
    internal fun build(): KeepAliveConfig {
        return KeepAliveConfig(
            targetPackages = targetPackages.toList(),
            periodicCheck = periodicCheckConfig,
            packageActivation = packageActivationConfig,
            dozeWhitelist = dozeWhitelistConfig,
            broadcastReceiver = broadcastReceiverConfig,
            enableStatistics = enableStatistics,
            statisticsInterval = statisticsInterval
        )
    }
}

/**
 * Periodic check strategy DSL builder
 */
@KeepAliveDsl
class PeriodicCheckBuilder {
    var enabled: Boolean = true
    var checkIntervalMs: Long = 30000L
    var maxRetryCount: Int = 3
    var retryDelayMs: Long = 5000L
    var checkOnScreenOn: Boolean = true
    var checkOnNetworkChange: Boolean = false

    internal fun build(): PeriodicCheckConfig {
        return PeriodicCheckConfig(
            enabled = enabled,
            checkInterval = checkIntervalMs,
            maxRetryCount = maxRetryCount,
            retryDelay = retryDelayMs,
            checkOnScreenOn = checkOnScreenOn,
            checkOnNetworkChange = checkOnNetworkChange
        )
    }
}

/**
 * Package activation strategy DSL builder
 */
@KeepAliveDsl
class PackageActivationBuilder {
    var enabled: Boolean = true
    var activationMethod: PackageActivationConfig.ActivationMethod = PackageActivationConfig.ActivationMethod.ACTIVITY
    var maxRetryCount: Int = 3
    var timeoutMs: Long = 10000L
    var retryDelayMs: Long = 2000L
    var verifyActivation: Boolean = true

    /**
     * Use activity launch for activation
     */
    fun useActivity() {
        activationMethod = PackageActivationConfig.ActivationMethod.ACTIVITY
    }

    /**
     * Use shell commands for activation
     */
    fun useShell() {
        activationMethod = PackageActivationConfig.ActivationMethod.SHELL
    }

    /**
     * Use comprehensive activation (try multiple methods)
     */
    fun useComprehensive() {
        activationMethod = PackageActivationConfig.ActivationMethod.COMPREHENSIVE
    }

    internal fun build(): PackageActivationConfig {
        return PackageActivationConfig(
            enabled = enabled,
            activationMethod = activationMethod,
            maxActivationRetry = maxRetryCount,
            activationTimeout = timeoutMs,
            retryDelay = retryDelayMs,
            verifyActivation = verifyActivation
        )
    }
}

/**
 * Doze whitelist strategy DSL builder
 */
@KeepAliveDsl
class DozeWhitelistBuilder {
    var enabled: Boolean = false
    var autoAddToWhitelist: Boolean = false
    var checkWhitelistStatus: Boolean = true

    internal fun build(): DozeWhitelistConfig {
        return DozeWhitelistConfig(
            enabled = enabled,
            autoAddToWhitelist = autoAddToWhitelist,
            checkWhitelistStatus = checkWhitelistStatus
        )
    }
}

/**
 * Broadcast receiver strategy DSL builder
 */
@KeepAliveDsl
class BroadcastReceiverBuilder {
    var enabled: Boolean = true
    var listenBootCompleted: Boolean = true
    var listenUserPresent: Boolean = true
    var listenPackageReplaced: Boolean = true
    var listenScreenOn: Boolean = false
    var listenNetworkChange: Boolean = false

    internal fun build(): BroadcastReceiverConfig {
        return BroadcastReceiverConfig(
            enabled = enabled,
            listenBootCompleted = listenBootCompleted,
            listenUserPresent = listenUserPresent,
            listenPackageReplaced = listenPackageReplaced,
            listenScreenOn = listenScreenOn,
            listenNetworkChange = listenNetworkChange
        )
    }
}

/**
 * Top-level DSL function to create KeepAlive configuration
 */
fun keepAliveConfig(builder: KeepAliveConfigBuilder.() -> Unit): KeepAliveConfig {
    return KeepAliveConfigBuilder().apply(builder).build()
}

/**
 * Extension function for creating default configuration for specific packages
 */
fun KeepAliveConfig.Companion.default(vararg packages: String): KeepAliveConfig {
    return keepAliveConfig {
        packages(*packages)
        periodicCheck {
            enabled = true
            checkIntervalMs = 30000L
        }
        packageActivation {
            enabled = true
            useActivity()
        }
        broadcastReceiver {
            enabled = true
        }
        dozeWhitelist {
            enabled = false
        }
        statistics(enabled = true, intervalMs = 60000L)
    }
}

/**
 * Extension function for creating lightweight configuration
 */
fun KeepAliveConfig.Companion.lightweight(vararg packages: String): KeepAliveConfig {
    return keepAliveConfig {
        packages(*packages)
        periodicCheck {
            enabled = true
            checkIntervalMs = 60000L // Less frequent checks
            maxRetryCount = 1
        }
        packageActivation {
            enabled = true
            useActivity()
            maxRetryCount = 1
        }
        broadcastReceiver {
            enabled = false
        }
        dozeWhitelist {
            enabled = false
        }
        statistics(enabled = false)
    }
}

/**
 * Extension function for creating aggressive configuration
 */
fun KeepAliveConfig.Companion.aggressive(vararg packages: String): KeepAliveConfig {
    return keepAliveConfig {
        packages(*packages)
        periodicCheck {
            enabled = true
            checkIntervalMs = 15000L // More frequent checks
            maxRetryCount = 5
            checkOnScreenOn = true
            checkOnNetworkChange = true
        }
        packageActivation {
            enabled = true
            useComprehensive()
            maxRetryCount = 5
        }
        broadcastReceiver {
            enabled = true
            listenBootCompleted = true
            listenUserPresent = true
            listenPackageReplaced = true
            listenScreenOn = true
            listenNetworkChange = true
        }
        dozeWhitelist {
            enabled = true
            autoAddToWhitelist = true
        }
        statistics(enabled = true, intervalMs = 30000L)
    }
}
