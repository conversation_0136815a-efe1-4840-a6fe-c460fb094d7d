package com.aispeech.keepalive

import android.content.Context
import com.aispeech.aibase.AILog
import kotlinx.coroutines.*

/**
 * 保活助手类
 * 提供简化的保活功能接口
 */
object KeepAliveHelper {
    
    private const val TAG = "KeepAliveHelper"
    
    private var manager: KeepAliveManager? = null
    private var isInitialized = false
    
    /**
     * 初始化保活功能
     * @param context 上下文
     * @param targetPackages 目标包名列表
     * @param callback 回调接口（可选）
     */
suspend fun initialize(
        context: Context,
        targetPackages: List<String>,
        callback: suspend (KeepAliveEvent) -> Unit
    ): Boolean {
        return try {
            if (isInitialized) {
                AILog.w(TAG, "KeepAlive already initialized")
                return true
            }
            
            manager = KeepAliveManager.getInstance()
            
            // 创建默认配置
            val config = createDefaultConfig(targetPackages)
            
            val result = manager?.initialize(context, config, callback) ?: false
            if (result) {
                isInitialized = true
                AILog.i(TAG, "KeepAlive initialized successfully")
            } else {
                AILog.e(TAG, "Failed to initialize KeepAlive")
            }
            
            result
        } catch (e: Exception) {
            AILog.e(TAG, "Error initializing KeepAlive", e)
            false
        }
    }
    
    /**
     * 启动保活功能
     */
    suspend fun start(): Boolean {
        return try {
            if (!isInitialized) {
                AILog.w(TAG, "KeepAlive not initialized")
                return false
            }
            
            manager?.start()
            AILog.i(TAG, "KeepAlive started")
            true
        } catch (e: Exception) {
            AILog.e(TAG, "Error starting KeepAlive", e)
            false
        }
    }
    
    /**
     * 停止保活功能
     */
    suspend fun stop(): Boolean {
        return try {
            manager?.stop()
            AILog.i(TAG, "KeepAlive stopped")
            true
        } catch (e: Exception) {
            AILog.e(TAG, "Error stopping KeepAlive", e)
            false
        }
    }
    
    /**
     * 检查包状态
     */
    suspend fun checkPackageStatus(packageName: String): KeepAliveStatus {
        return try {
            if (!isInitialized) {
                AILog.w(TAG, "KeepAlive not initialized")
                return KeepAliveStatus.UNKNOWN
            }
            
            manager?.getPackageStatus(packageName) ?: KeepAliveStatus.UNKNOWN
        } catch (e: Exception) {
            AILog.e(TAG, "Error checking package status", e)
            KeepAliveStatus.UNKNOWN
        }
    }
    
    /**
     * 激活包
     */
    suspend fun activatePackage(packageName: String): Boolean {
        return try {
            if (!isInitialized) {
                AILog.w(TAG, "KeepAlive not initialized")
                return false
            }
            
            manager?.activatePackage(packageName) ?: false
        } catch (e: Exception) {
            AILog.e(TAG, "Error activating package", e)
            false
        }
    }
    
    /**
     * 获取所有包状态
     */
    suspend fun getAllPackageStatuses(): Map<String, KeepAliveStatus> {
        return try {
            if (!isInitialized) {
                AILog.w(TAG, "KeepAlive not initialized")
                return emptyMap()
            }
            
            manager?.getAllPackageStatuses() ?: emptyMap()
        } catch (e: Exception) {
            AILog.e(TAG, "Error getting all package statuses", e)
            emptyMap()
        }
    }
    
    /**
     * 获取统计信息
     */
    fun getStatistics(): KeepAliveStatistics? {
        return try {
            if (!isInitialized) {
                AILog.w(TAG, "KeepAlive not initialized")
                return null
            }
            
            manager?.getStatistics()
        } catch (e: Exception) {
            AILog.e(TAG, "Error getting statistics", e)
            null
        }
    }
    
    /**
     * 释放资源
     */
    suspend fun release() {
        try {
            manager?.release()
            manager = null
            isInitialized = false
            AILog.i(TAG, "KeepAlive released")
        } catch (e: Exception) {
            AILog.e(TAG, "Error releasing KeepAlive", e)
        }
    }
    
    /**
     * 检查是否正在运行
     */
    fun isRunning(): Boolean {
        return manager?.isRunning() ?: false
    }
    
    /**
     * 创建默认配置
     */
    private fun createDefaultConfig(targetPackages: List<String>): KeepAliveConfig {
        return KeepAliveConfig(
            targetPackages = targetPackages,
            periodicCheck = PeriodicCheckConfig(
                enabled = true,
                checkInterval = 30000L, // 30秒检查一次
                maxRetryCount = 3,
                retryDelay = 5000L,
                checkOnScreenOn = true,
                checkOnNetworkChange = false
            ),
            packageActivation = PackageActivationConfig(
                enabled = true,
                activationMethod = PackageActivationConfig.ActivationMethod.ACTIVITY,
                maxActivationRetry = 3,
                activationTimeout = 10000L,
                retryDelay = 2000L,
                verifyActivation = true
            ),
            dozeWhitelist = DozeWhitelistConfig(
                enabled = false, // 默认关闭，需要系统权限
                autoAddToWhitelist = false,
                checkWhitelistStatus = true
            ),
            broadcastReceiver = BroadcastReceiverConfig(
                enabled = true,
                listenBootCompleted = true,
                listenUserPresent = true,
                listenPackageReplaced = true,
                listenScreenOn = false,
                listenNetworkChange = false
            ),
            enableStatistics = true,
            statisticsInterval = 60000L // 1分钟
        )
    }
    
    /**
     * 创建简单的日志回调
     */
    fun createSimpleCallback(): KeepAliveCallback {
        return object : KeepAliveCallback {
            override fun onStatusChanged(packageName: String, status: KeepAliveStatus, strategy: KeepAliveStrategy) {
                AILog.d(TAG, "Package $packageName status changed to $status by $strategy")
            }
            
            override fun onEvent(event: KeepAliveEvent, packageName: String, message: String, extra: Map<String, Any>) {
                AILog.d(TAG, "Event: $event for $packageName - $message")
            }
            
            override fun onError(packageName: String, strategy: KeepAliveStrategy, error: String, exception: Throwable?) {
                AILog.e(TAG, "Error in $strategy for $packageName: $error", exception)
            }
            
            override fun onStatistics(statistics: KeepAliveStatistics) {
                AILog.d(TAG, "Statistics: active=${statistics.activePackages}/${statistics.totalPackages}, " +
                        "checks=${statistics.totalChecks}, activations=${statistics.successfulActivations}")
            }
        }
    }
    
    /**
     * 快速启动保活功能
     * 使用默认配置和简单回调
     */
    suspend fun quickStart(context: Context, targetPackages: List<String>): Boolean {
        return try {
            val callback = createSimpleCallback()
            val initialized = initialize(context, targetPackages, callback)
            if (initialized) {
                start()
            } else {
                false
            }
        } catch (e: Exception) {
            AILog.e(TAG, "Error in quick start", e)
            false
        }
    }
    
    /**
     * 批量激活所有目标包
     */
    suspend fun activateAllPackages(): Map<String, Boolean> {
        return try {
            if (!isInitialized) {
                AILog.w(TAG, "KeepAlive not initialized")
                return emptyMap()
            }
            
            val allStatuses = getAllPackageStatuses()
            val results = mutableMapOf<String, Boolean>()
            
            allStatuses.forEach { (packageName, status) ->
                if (status == KeepAliveStatus.INACTIVE) {
                    results[packageName] = activatePackage(packageName)
                } else {
                    results[packageName] = true // 已经是活跃状态
                }
            }
            
            results
        } catch (e: Exception) {
            AILog.e(TAG, "Error activating all packages", e)
            emptyMap()
        }
    }
}
