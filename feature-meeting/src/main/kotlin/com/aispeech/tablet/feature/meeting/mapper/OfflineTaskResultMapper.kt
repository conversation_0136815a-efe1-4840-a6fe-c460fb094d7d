package com.aispeech.tablet.feature.meeting.mapper

import com.aispeech.aimeeting.api.entities.ConversationV2
import com.aispeech.aimeeting.api.entities.Utterance
import com.aispeech.aimeeting.api.extensions.removeAllEmphasisTags
import com.aispeech.tablet.core.meeting.entity.AsrDataType
import com.aispeech.tablet.core.meeting.entity.ConversationRow2
import com.aispeech.tablet.core.meeting.entity.RuntimeDataItemV2

object OfflineTaskResultMapper {

  private fun createRuntimeTextItemV2(u: Utterance) = ConversationRow2.ContentItem(
    begin = u.b ?: 0,
    end = u.e,
    text = u.t,
    spoken = u.spoken ?: false,
    updated = false
  )

  private fun createRuntimeDataItemV2(
    ts: Double,
    useSpeaker: Boolean = true,
    type: AsrDataType,
    language: String,
    speaker: String,
    text: List<ConversationRow2.ContentItem>
  ) = RuntimeDataItemV2(
    ts = ts,
    useSpeaker = useSpeaker,
    type = type,
    language = language,
    speaker = speaker,
    text = text
  )


  fun mapToRuntimeData(
    list: List<ConversationV2>,
    language: String,
    translateLanguage: String
  ): List<RuntimeDataItemV2> {

    return list.flatMap { conversation ->
      val t = conversation.content.map { t -> t.removeAllEmphasisTags() }.flatten()
      val sourceTextItems = t.map(::createRuntimeTextItemV2)

      val result = mutableListOf<RuntimeDataItemV2>()
      val source = createRuntimeDataItemV2(
        ts = conversation.begin.toDouble(),
        type = AsrDataType.SOURCE,
        language = language,
        speaker = "说话人${conversation.speaker}",
        text = sourceTextItems
      )
      result.add(source)

      conversation.translate?.takeIf { it.isNotBlank() }?.let {
        val translateTextItem = ConversationRow2.ContentItem(
          begin = conversation.begin,
          end = source.text.lastOrNull()?.end ?: 0,
          text = it,
          spoken = false,
          updated = false,
        )
        val translate = createRuntimeDataItemV2(
          ts = conversation.begin.toDouble(),
          type = AsrDataType.TRANSLATE,
          language = translateLanguage,
          speaker = "说话人${conversation.speaker}",
          text = listOf(translateTextItem)
        )
        result.add(translate)
      }

      result
    }
  }
}