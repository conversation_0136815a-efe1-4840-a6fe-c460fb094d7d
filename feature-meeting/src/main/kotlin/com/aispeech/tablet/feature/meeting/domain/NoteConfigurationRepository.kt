package com.aispeech.tablet.feature.meeting.domain

import com.aispeech.tablet.core.db.dao.NoteConfigurationDao
import com.aispeech.tablet.core.db.dao.NoteContentDao
import com.aispeech.tablet.core.db.dao.NoteDao
import com.aispeech.tablet.core.db.dao.NoteTranscriptionDao
import com.aispeech.tablet.core.db.use
import com.aispeech.tablet.core.model.entity.NoteConfigurationEntity
import com.aispeech.tablet.core.model.entity.NoteWithConfiguration
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.flatMapLatest
import kotlinx.coroutines.flow.flowOf
import kotlinx.coroutines.withContext
import javax.inject.Inject

class NoteConfigurationRepository @Inject constructor() {

  // 笔记
  private val noteDao by lazy {
    NoteDao.use()
  }

  // 笔记配置
  private val noteConfigurationDao by lazy {
    NoteConfigurationDao.use()
  }

  // 说话人
  private val noteTranscriptionDao by lazy {
    NoteTranscriptionDao.use()
  }

  // 记录
  private val noteContentDao by lazy {
    NoteContentDao.use()
  }

  /**
   * 获取置顶笔记和关联对象
   * @param noteId 笔记 id
   */
  fun getNoteWithConfiguration(noteId: String): Flow<NoteWithConfiguration?> {
    return noteDao.getNoteWithConfiguration(noteId)
  }

  /**
   * 获取指定的笔记配置流
   * @param noteId 笔记 id
   */
  fun getConfigurationForNoteFlow(noteId: String): Flow<NoteConfigurationEntity?> {
    return noteConfigurationDao.getConfigurationForNoteFlow(noteId)
  }

  /**
   * 获取指定的笔记配置
   * @param noteId 笔记 id
   */
  suspend fun getConfigurationForNote(noteId: String): NoteConfigurationEntity? {
    return noteConfigurationDao.getConfigurationForNote(noteId)
  }

  /**
   * 获取指定笔记中所有出现过的不重复的说话人名称。
   * @param noteOwnerId 笔记id。
   * @return Flow<List<String>> 不重复的说话人名称列表。
   */
  fun getAvailableSpeakersForNoteFlow(noteOwnerId: String): Flow<List<String>> {
    return noteContentDao.getOfflineCompletedVoiceContentIdsForNoteFlow(noteOwnerId)
      .flatMapLatest { contentIds ->
        if (contentIds.isEmpty()) {
          flowOf(emptyList())
        } else {
          noteTranscriptionDao.getDistinctSpeakersForContentIdsFlow(noteOwnerId, contentIds)
        }
      }.distinctUntilChanged()
  }

  /**
   * 更新指定笔记的说话人列表。
   * 如果笔记之前没有配置，会创建新的配置。
   * 同时会更新笔记的 hasSpeakerConfig 状态和 updated_at 时间戳。
   */
  suspend fun updateSpeakerSelectionAndIntent(
    noteId: String,
    newSelectedSpeakers: List<String>?,
    newIntentState: Boolean) {
    withContext(Dispatchers.IO) {
      noteConfigurationDao.saveSpeakerSelectionWithIntent(
        noteId,
        newSelectedSpeakers,
        newIntentState,
        noteDao
      )
    }
  }

}