package com.aispeech.tablet.feature.meeting.domain

import android.content.Context
import android.net.Uri
import android.util.Log
import androidx.annotation.OptIn
import androidx.media3.common.MediaItem
import androidx.media3.common.PlaybackException
import androidx.media3.common.Player
import androidx.media3.common.util.UnstableApi
import androidx.media3.datasource.DefaultDataSource
import androidx.media3.exoplayer.ExoPlayer
import androidx.media3.exoplayer.source.ProgressiveMediaSource
import com.aispeech.aibase.AILog
import com.aispeech.aimeeting.api.player.PlayerEvent
import com.aispeech.tablet.core.res.TabletStringsUtils
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.isActive
import kotlinx.coroutines.launch
import java.io.File


sealed class PlayerWaitReadyAction {
  data object None: PlayerWaitReadyAction()
  data object Play: PlayerWaitReadyAction()
  data object Seek: PlayerWaitReadyAction()
}

class ExoPlayerManager(
  private val context: Context,
  private val coroutineScope: CoroutineScope = CoroutineScope(Dispatchers.Main + SupervisorJob())
) {

  companion object {
    const val TAG = "ExoPlayerManager"
    const val TIME_PERIOD = 100L // 更新间隔（毫秒）
  }

  private val _playerEvent = MutableStateFlow<PlayerEvent>(PlayerEvent.Unknown)
  val playerEvent: StateFlow<PlayerEvent> = _playerEvent.asStateFlow()

  private val player: ExoPlayer = ExoPlayer.Builder(context).build()

  private var _isLoadError: Boolean = false
  private var waitReadyAction: PlayerWaitReadyAction = PlayerWaitReadyAction.None

  private var progressJob: Job? = null // 用于定时更新进度

  var initDataPrepared: (() -> Unit)? = null

  private val playerListener = object : Player.Listener {
    override fun onPlaybackStateChanged(state: Int) {
      when (state) {
        Player.STATE_READY -> {
          eventChange(PlayerEvent.PlayerActionReady)
          initDataPrepared?.invoke()
          initDataPrepared = null

          when (waitReadyAction) {
            PlayerWaitReadyAction.Play -> {
              waitReadyAction = PlayerWaitReadyAction.None
              play()
            }
            PlayerWaitReadyAction.Seek -> {
              eventChange(PlayerEvent.PlayerActionPlay)
              startProgressTimer()
            }
            else -> Unit
          }
        }
        Player.STATE_IDLE, Player.STATE_BUFFERING -> {
          // 不需要额外处理
        }
        Player.STATE_ENDED -> {
          waitReadyAction = PlayerWaitReadyAction.None
          player.playWhenReady = false
          eventChange(PlayerEvent.PlayerActionComplete)
          AILog.i(TAG, "ExoPlayer complete")
        }
      }
    }

    override fun onPlayerError(error: PlaybackException) {
      AILog.e(TAG, "ExoPlayer error: ${error.errorCode}, ${error.message}")
      _isLoadError = true
      eventChange(PlayerEvent.PlayerActionError(error.errorCode, error.message ?: TabletStringsUtils.getString(com.aispeech.tablet.core.res.R.string.chataidatamanager_1739156714041_14)))
    }

    override fun onIsPlayingChanged(isPlaying: Boolean) {
      // 根据需要实现
    }
  }

  init {
    player.addListener(playerListener)
  }

  private fun eventChange(event: PlayerEvent) {
    AILog.i(TAG, "ExoPlayer eventChange : ${_playerEvent.value} -> $event")
    _playerEvent.value = event
    when (event) {
      PlayerEvent.PlayerActionPlay -> startProgressTimer()
      PlayerEvent.PlayerActionPause -> stopProgressTimer()
      PlayerEvent.PlayerActionStop,
      PlayerEvent.PlayerActionComplete,
      is PlayerEvent.PlayerActionPrepareFailed,
      is PlayerEvent.PlayerActionError -> {
        stopProgressTimer()
      }
      else -> {}
    }
  }

  private fun getProgress(): Int {
    return if (player.isPlaying) player.currentPosition.toInt() else 0
  }

  private fun getDuration(): Int {
    return if (player.isPlaying) player.duration.toInt() else 0
  }

  fun play() {
    AILog.i(TAG, "MediaPlayer play, state = ${_playerEvent.value}, ${if (_isLoadError) "But Load Error!!!" else ""}")
    if (_isLoadError) {
      eventChange(PlayerEvent.PlayerActionError(-1, TabletStringsUtils.getString(com.aispeech.tablet.core.res.R.string.exoplayermanager_1739176765517_0)))
      return
    }

    Log.d(TAG, "play: backState: ${player.playbackState}")
    when (player.playbackState) {
        Player.STATE_READY -> {
          Log.d(TAG, "play: ready")
          player.play()
          eventChange(PlayerEvent.PlayerActionPlay)
        }
        Player.STATE_ENDED -> {
          Log.d(TAG, "play: ended")
          seekTo(0, true)
          eventChange(PlayerEvent.PlayerActionPlay)
        }
        else -> {
          waitReadyAction = PlayerWaitReadyAction.Play
          eventChange(PlayerEvent.PlayerActionPrepare)
        }
    }
  }

  fun seekTo(progress: Int, autoPlay: Boolean) {
    val isPlaying = player.isPlaying
    AILog.i(TAG, "MediaPlayer seekTo, progress = $progress, state = ${_playerEvent.value}, autoPlay = $autoPlay, isPlaying = $isPlaying")

    val canSeek = player.playbackState == Player.STATE_READY || player.playbackState == Player.STATE_ENDED
    if (!canSeek) return

    player.playWhenReady = autoPlay
    if (autoPlay) {
      waitReadyAction = PlayerWaitReadyAction.Seek
    } else {
      val duration = if (player.duration > 0) player.duration.toInt() else getDuration()
      eventChange(PlayerEvent.PlayerActionProgress(progress, duration))
      eventChange(PlayerEvent.PlayerActionPause)
    }

    player.seekTo(progress.toLong())
  }

  fun isPlaying() = player.isPlaying

  fun pause() {
    waitReadyAction = PlayerWaitReadyAction.None
    val isPlaying = player.isPlaying
    AILog.i(TAG, "MediaPlayer pause, state = ${_playerEvent.value}, isPlaying = $isPlaying")
    if (isPlaying) {
      player.pause()
      eventChange(PlayerEvent.PlayerActionPause)
    }
  }


  fun stop() {
    AILog.i(TAG, "MediaPlayer stop, state = ${_playerEvent.value}, playbackState = ${player.playbackState}")
    if (_playerEvent.value !in listOf(PlayerEvent.PlayerActionStop, PlayerEvent.Unknown) &&
      (player.playbackState == Player.STATE_READY || player.playbackState == Player.STATE_ENDED)) {
      eventChange(PlayerEvent.PlayerActionStop)
      waitReadyAction = PlayerWaitReadyAction.None
      player.stop()
    } else {
      AILog.e(TAG, "MediaPlayer invalid stop!!!")
    }
  }

  fun release() {
    stop()
    player.removeListener(playerListener)
    AILog.i(TAG, "MediaPlayer release")
    initDataPrepared = null
    player.release()
  }

  private fun startProgressTimer() {
    progressJob?.cancel()
    progressJob = coroutineScope.launch {
      while (isActive && player.playbackState != Player.STATE_ENDED) {
        val curDuration = getProgress()
        val totalDuration = getDuration()
        AILog.i(TAG, "ExoPlayer progress update. cur: $curDuration, total: $totalDuration, evt: ${_playerEvent.value}")
        if (_playerEvent.value !is PlayerEvent.PlayerActionComplete &&
          totalDuration != 0 &&
          _playerEvent.value !is PlayerEvent.PlayerActionPause
        ) {
          _playerEvent.value = PlayerEvent.PlayerActionProgress(curDuration, totalDuration)
          delay(TIME_PERIOD)
        } else {
          break
        }
      }
    }
  }

  private fun stopProgressTimer() {
    progressJob?.cancel()
    progressJob = null
  }

  @OptIn(UnstableApi::class)
  fun initWithDataSource(audioPath: String, prepared: (() -> Unit)? = null) {
    AILog.i(TAG, "initWithDataSource-isPlaying：${player.isPlaying}, prepare: $prepared")

    initDataPrepared = prepared
    waitReadyAction = PlayerWaitReadyAction.None

    runCatching {
      player.playWhenReady = false
      val uri = Uri.parse(audioPath)
      val isNetworkResource = audioPath.startsWith("http://", ignoreCase = true) || audioPath.startsWith("https://", ignoreCase = true)

      if (isNetworkResource) {
        val dataSourceFactory = DefaultDataSource.Factory(context)
        val mediaSource = ProgressiveMediaSource.Factory(dataSourceFactory)
          .createMediaSource(MediaItem.fromUri(uri))
        player.setMediaSource(mediaSource)
      } else {
        val mediaItem = MediaItem.fromUri(uri)
        player.setMediaItem(mediaItem)
      }

      player.prepare()

      _isLoadError = false
      AILog.i(TAG, "initWithDataSource Success. $audioPath")
    }.onFailure {
      val file = File(audioPath)
      AILog.e(TAG, "initWithDataSource Failed!! $it. path = $audioPath, exists = ${file.exists()}, size = ${if(file.exists()) file.length() else "NotExists"}")
      _isLoadError = true
      eventChange(PlayerEvent.PlayerActionPrepareFailed)
    }
  }

}