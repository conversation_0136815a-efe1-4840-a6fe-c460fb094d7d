package com.aispeech.tablet.feature.meeting.utils

import com.aispeech.tablet.core.common.Tablet
import com.aispeech.tablet.lib.analyse.manager.AnalyseManager
import com.aispeech.tablet.lib.analyse.model.AnalyseConst

object RecordAnalyseUtils {

  /**
   * 录音上报
   */
  fun reportOnStart(listSize: Int) {
    if (listSize == 1) {
      AnalyseManager.recordEvent(Tablet.application, AnalyseConst.TABLET_NOTE_RECORD_FIRST_START)
    }
  }

  fun reportOnStop() {
    AnalyseManager.recordEvent(Tablet.application, AnalyseConst.TABLET_NOTE_RECORD_STOP)
  }

  fun reportOnPause() {
    AnalyseManager.recordEvent(Tablet.application, AnalyseConst.TABLET_NOTE_RECORD_PAUSE)
  }

  fun reportOnResume() {
    AnalyseManager.recordEvent(Tablet.application, AnalyseConst.TABLET_NOTE_RECORD_RESUME)
  }

  fun reportAutoPaused() {
    AnalyseManager.recordEvent(Tablet.application, AnalyseConst.TABLET_NOTE_RECORD_AUTO_PAUSE)
  }

  fun reportOnMaxTime() {
    AnalyseManager.recordEvent(Tablet.application, AnalyseConst.TABLET_NOTE_RECORD_MAX_TIME)
  }

  fun reportAudioDeleted() {
    AnalyseManager.recordEvent(Tablet.application, AnalyseConst.TABLET_NOTE_CLEAN_AUDIO)
  }

  fun reportDeleteAllAudioContent() {
    AnalyseManager.recordEvent(Tablet.application, AnalyseConst.TABLET_NOTE_CLEAN_AUDIO_AND_CONTENT)
  }

  fun reportEditSpeaker(
    noteId: String,
    syncAll: Boolean
  ) {
    AnalyseManager.recordEvent(
      Tablet.application, AnalyseConst.TABLET_NOTE_EDIT_SPEAKER, mapOf(
        "noteId" to noteId,
        "syncAll" to if (syncAll) "1" else "0"
      )
    )
  }
}