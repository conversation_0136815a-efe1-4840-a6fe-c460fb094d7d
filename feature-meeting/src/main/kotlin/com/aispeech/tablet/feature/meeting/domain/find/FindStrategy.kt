package com.aispeech.tablet.feature.meeting.domain.find

import com.aispeech.tablet.feature.meeting.data.MatchPosition

data class SearchResultSummary(val itemCount: Int, val totalMatches: Int)

data class SearchResultItemDetails(
  val identifier: Any?,
  val displayLocation: Int?,
  val matches: List<MatchPosition>
)

data class PlaySeekParams(
  val id: String?,    // 可以是 contentId 或 objectId
  val begin: Int      // 播放起始位置
)

interface FindStrategy {
  // 获取日志前缀，用于日志记录
  fun getLogPrefix(): String

  // 为目标位置更新数据源，用于分页功能
  fun updateDataSourceForTarget(targetLocation: Int)

  // 重置数据源
  fun resetDataSource()

  // 执行搜索操作，根据查询字符串和笔记ID返回搜索结果摘要
  suspend fun search(query: String, noteId: String): SearchResultSummary

  // 刷新搜索并更新结果，返回最新的搜索结果摘要
  suspend fun refreshSearchAndUpdateResults(): SearchResultSummary

  // 重置搜索状态
  fun resetSearch()

  // 获取搜索结果的数量
  fun getResultCount(): Int

  // 获取指定搜索结果索引的详细信息，可根据查询字符串进一步筛选，返回结果详情或为空
  suspend fun getResultItemDetails(searchResultIndex: Int, query: String): SearchResultItemDetails?

  // 获取播放位置
  suspend fun getActivePlayPosition(segmentIndex: Int, matchPosition: Int): PlaySeekParams?
}
