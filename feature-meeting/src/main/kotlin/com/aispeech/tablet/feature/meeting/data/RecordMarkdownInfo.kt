package com.aispeech.tablet.feature.meeting.data

import com.aispeech.tablet.core.common.extensions.formatMillisecond
import com.aispeech.tablet.core.meeting.entity.ConversationMixRow
import com.aispeech.tablet.core.meeting.entity.ConversationRow2
import com.aispeech.tablet.core.meeting.entity.RuntimeDataMixItem

fun List<ConversationRow2.ContentItem>.toMarkdown(): String {
  return this.joinToString("") {
    if (it.text.isNotEmpty()) {
      "<range begin='${it.begin}' end='${it.end}'>${it.text}</range>"
    } else {
      ""
    }
  }
}

fun RuntimeDataMixItem.toMarkdown(): String {
  val stringBuffer = StringBuffer()
  this.text.forEach {
    if (it.text.isNotEmpty()) {
      stringBuffer.append("<range begin='${it.begin}' end='${it.end}'>${it.text}</range>")
    }
  }
  if (this.middle.isNotEmpty()) {
    stringBuffer.append("<range begin='-1' end='-1'>")
    this.middle.forEach { stringBuffer.append(it) }
    stringBuffer.append("</range>")
  }
  return stringBuffer.toString()
}

/**
 *
 * <AUTHOR>
 * @since 2025/3/17
 */
data class RecordMarkdownInfo(
  val time: String,
  val sourceId: Long,
  val contentId: String,
  val firstVisibleIndex: Int,
  val markdownText: String,
  val audioDelete: Boolean,
  val ts: Double = 0.0,
  val visibleLength: Int = -1,
) {
  companion object {
    fun toRecordMarkdownInfo(item: ConversationMixRow): RecordMarkdownInfo {
      val content = item.items.toMarkdown()
      return RecordMarkdownInfo(
        time = item.speaker.time.formatMillisecond(),
        sourceId = item.sourceId,
        contentId = item.contentId,
        firstVisibleIndex = 0,
        markdownText = content,
        audioDelete = item.speaker.audioDelete
      )
    }

    fun toRecordMarkdownInfo(item: RuntimeDataMixItem): RecordMarkdownInfo {
      var visibleLength = 0
      val content = StringBuffer().apply {
        item.text.joinToString("") {
          if (it.text.isNotEmpty()) {
            visibleLength += it.text.length
            "<range begin='${it.begin}' end='${it.end}'>${it.text}</range>"
          } else {
            ""
          }
        }.let {
          append(it)
        }
        if (item.middle.isNotEmpty()) {
          item.middle.joinToString("").let {
            if (it.isNotEmpty()) {
              append("<range begin='-1' end='-1'>$it</range>")
            } else {
              visibleLength = -1
            }
          }
        } else {
          visibleLength = -1
        }
      }.toString()
      return RecordMarkdownInfo(
        time = item.speaker.time.formatMillisecond(),
        sourceId = item.id,
        contentId = item.contentId,
        firstVisibleIndex = 0,
        markdownText = content,
        audioDelete = item.speaker.audioDelete,
        ts = item.ts,
        visibleLength = visibleLength
      )
    }
  }
}