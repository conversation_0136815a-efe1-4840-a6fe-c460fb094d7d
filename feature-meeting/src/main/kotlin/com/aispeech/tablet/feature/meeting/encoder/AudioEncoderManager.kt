package com.aispeech.tablet.feature.meeting.encoder

import com.aispeech.tablet.feature.meeting.encoder.impl.AudioOggOpusEncoderImpl
import javax.inject.Inject

class AudioEncoderManager @Inject constructor() {
  enum class EncoderType {
    OggOpus
  }

  private var mEncoder: AbsEncoder? = null

  fun initWithEncoder(
    encoderType: EncoderType = EncoderType.OggOpus,
    sampleRate: Int,
    channel: Int,
    onEncoderBuffer: (data: ByteArray, size: Int) -> Unit?
  ) {
     if (encoderType == EncoderType.OggOpus) {
       mEncoder  = AudioOggOpusEncoderImpl()
     }
     mEncoder?.init(sampleRate, channel, onEncoderBuffer)
  }

   fun start() {
     mEncoder?.start()
  }

  fun feed(byteArray: ByteArray) {
    mEncoder?.feed(byteArray)
  }

  fun stop() {
    mEncoder?.stop()
  }

   fun destroy() {
     mEncoder?.destroy()
  }

}