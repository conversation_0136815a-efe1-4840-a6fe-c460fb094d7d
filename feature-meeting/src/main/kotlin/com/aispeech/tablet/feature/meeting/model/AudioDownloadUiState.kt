package com.aispeech.tablet.feature.meeting.model

// 定义UI状态，包括 QR 和本地两种操作
sealed class AudioDownloadUiState {
  data object Idle : AudioDownloadUiState()

  // QR 操作状态
  data object LoadingQr : AudioDownloadUiState()
  data class SuccessQr(val qrCodeData: String) : AudioDownloadUiState()

  // 本地操作状态
  data object LoadingLocal : AudioDownloadUiState()
  data class SuccessLocal(val localPath: String) : AudioDownloadUiState()

  // 通用错误状态
  data class Error(val message: String) : AudioDownloadUiState()
}