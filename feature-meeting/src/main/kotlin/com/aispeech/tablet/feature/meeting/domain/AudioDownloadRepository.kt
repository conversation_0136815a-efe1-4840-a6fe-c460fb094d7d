package com.aispeech.tablet.feature.meeting.domain

import com.aispeech.lib_ktx.Resultat
import com.aispeech.tablet.core.model.entity.api.request.AudioDownloadItem
import com.aispeech.tablet.core.model.entity.api.request.AudioDownloadRequest
import com.aispeech.tablet.core.model.entity.api.response.AudioDownloadResponse
import com.aispeech.tablet.core.network.entity.toResultat
import com.aispeech.tablet.core.network.service.business.note.NoteService
import javax.inject.Inject

class AudioDownloadRepository @Inject constructor(
  private val noteService: NoteService
) {

  suspend fun audioDownload(
    noteId: String,
    objectIds: List<String>
  ): Resultat<AudioDownloadResponse> {
    return noteService.noteAudioDownload(
      AudioDownloadRequest(
        noteUid = noteId,
        audios = objectIds.map { AudioDownloadItem(audioUid = it) }
      )
    ).toResultat()
  }
}