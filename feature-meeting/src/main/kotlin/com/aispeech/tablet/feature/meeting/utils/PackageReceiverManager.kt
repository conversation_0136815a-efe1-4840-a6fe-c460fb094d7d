package com.aispeech.tablet.feature.meeting.utils

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.util.Log
import com.aispeech.aibase.AILog

/**
* 包安装监听管理器
*/
class PackageReceiverManager(private val context: Context) {

  companion object {
    private const val TAG = "PackageReceiverManager"
    private const val TARGET_PACKAGE = "com.aispeech.hybridspeech"
  }

  private var pkgReceiver: BroadcastReceiver? = null
  private var isRegistered = false

  /**
   * 注册广播接收器
   */
  fun register() {
    if (isRegistered) return

    pkgReceiver = PackageInstallReceiver().also { receiver ->
      val filter = IntentFilter(Intent.ACTION_PACKAGE_ADDED).apply {
        addDataScheme("package")
      }
      context.registerReceiver(receiver, filter)
      isRegistered = true
      Log.d(TAG, "$TAG is registered")
      // 初次激活
      receiver.ensurePackageActive(TARGET_PACKAGE)
    }
  }

  /**
   * 注销广播接收器
   */
  fun unregister() {
    pkgReceiver?.let { receiver ->
      if (isRegistered) {
        try {
          context.unregisterReceiver(receiver)
        } catch (e: IllegalArgumentException) {
          // 已经注销或未注册
        }
        isRegistered = false
      }
      pkgReceiver = null
    }
  }

  /**
   * 包安装广播接收器
   */
  private inner class PackageInstallReceiver : BroadcastReceiver() {
    override fun onReceive(context: Context, intent: Intent) {
      when (intent.action) {
        Intent.ACTION_PACKAGE_ADDED -> {
          handlePackageAdded(intent)
        }
      }
    }

    private fun handlePackageAdded(intent: Intent) {
      val packageName = intent.data?.schemeSpecificPart
      Log.d(TAG, "handlePackageAdded packageName: $packageName")
      if (packageName == TARGET_PACKAGE) {
        clearPackageStoppedState(packageName)
      }
    }

    private fun clearPackageStoppedState(packageName: String) {
      ensurePackageActive(packageName)
    }

    fun ensurePackageActive(packageName: String) {
      try {
        // 使用智能激活方法，先检查状态再决定是否需要激活
        val success = PackageUnstoppedHelper.ensurePackageActive(
          context = context.applicationContext,
          packageName = packageName
        )
        AILog.d(TAG, "ensurePackageActive for $packageName: $success")
      } catch (e: Exception) {
        AILog.e(TAG, "ensurePackageActive exception for $packageName: ${e.message}")
      }
    }
  }
}