package com.aispeech.tablet.feature.meeting.extensions

import com.aispeech.aimeeting.api.entities.Conversation
import com.aispeech.aimeeting.api.entities.RuntimeDataItem
import com.aispeech.aimeeting.api.entities.RuntimeTextItem

fun Conversation.toRuntimeDataItem(): RuntimeDataItem {
  return RuntimeDataItem(
    id = "",
    ts = ts.toDouble(),
    speaker = n ?: "",
    text = c.map {
      RuntimeTextItem(
        begin = it.b?.toDouble() ?: 0.0,
        end = it.e.toDouble(),
        text = it.t,
        spoken = it.spoken ?: false
      )
    }
  )
}