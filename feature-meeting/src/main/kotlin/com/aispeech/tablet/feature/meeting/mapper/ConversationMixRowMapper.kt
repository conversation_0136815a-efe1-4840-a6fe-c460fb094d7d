package com.aispeech.tablet.feature.meeting.mapper

import com.aispeech.tablet.core.meeting.entity.ConversationRow2
import com.aispeech.tablet.core.model.entity.TranscriptionPart

fun List<ConversationRow2.ContentItem>.toParts(): List<TranscriptionPart> = map { it.toPart() }

fun ConversationRow2.ContentItem.toPart(): TranscriptionPart {
  return TranscriptionPart(
    start = begin,
    end = end,
    spoken = spoken,
    text = text
  )
}