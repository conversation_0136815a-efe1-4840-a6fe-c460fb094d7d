package com.aispeech.tablet.feature.meeting.domain

import com.aispeech.aibase.AILog
import com.aispeech.aimeeting.api.MeetingConfig
import com.aispeech.aimeeting.api.file.CacheFileManager
import com.aispeech.tablet.core.common.FileCacheUtils
import com.aispeech.tablet.core.common.Tablet
import kotlinx.coroutines.Job
import kotlinx.coroutines.launch
import java.util.UUID
import javax.inject.Inject

class MixMultiEndAudioManager @Inject constructor(
  private val audioManager: MixAudioDataManager,
  private val playbackFileManager: CacheFileManager,
  private val pcmFileManager: CacheFileManager
) {

  companion object {
    const val TAG = "MixMultiEndAudioManager"
  }

  private var job: Job? = null
  private val scope = Tablet.workScope

  private var flushTime: Long = 0

  private var audioChangeListener: ((Boole<PERSON>, Long) -> Unit)? = null

  fun getCacheFileManager() = if(MeetingConfig.isMp3()) playbackFileManager else pcmFileManager

  /**
   * 初始化文件路径
   */
  suspend fun createInputFile(fileName: String = UUID.randomUUID().toString()): String {
    playbackFileManager.createFile(fileName, "mp3")?.let {
      if(MeetingConfig.isMp3().not()) {
        val temp = pcmFileManager.createFile("temp", "wav")
        AILog.i(TAG, "createInputFile-path: $it, temp = $temp")
      } else {
        AILog.i(TAG, "createInputFile-path: $it")
      }
      return it
    } ?: throw Exception("创建文件失败")
  }

  suspend fun initWithBasePath(audioBasePath: String) {
    playbackFileManager.setBasePath(audioBasePath)
    if(MeetingConfig.isMp3().not()) {
      pcmFileManager.setBasePath(FileCacheUtils.getAudioCachePath())
    }
  }

  fun audioType() = if(MeetingConfig.isOggOpus()) "ogg_opus" else if(MeetingConfig.isMp3()) "mp3" else "wav"

  private fun bindFlow() {
    job?.cancel()
    job = scope.launch {
      launch {
        audioManager.getMp3DataFlow().collect {//mp3-回听
          //AILog.v(TAG, "recv data flow ${it.size}")
          playbackFileManager.write(it)
          val curTs = System.currentTimeMillis()
          if (curTs - flushTime >= 30_000) {
            flushTime = curTs
            playbackFileManager.flush()
            AILog.i(TAG, "playBackWriteFlushSync")
          }

          val fileLength = playbackFileManager.fileLength()
          audioChangeListener?.invoke(false, fileLength)
        }
      }

      if(MeetingConfig.isMp3().not()) {
        launch {
          audioManager.getPCMDataFlow().collect {//pcm | ogg opus - 断点续传
            pcmFileManager.write(it)
          }
        }
      }
    }
  }

  fun start(audioChangeListener: ((Boolean, Long) -> Unit)? = null) {
    this.audioChangeListener = audioChangeListener
    audioManager.start()
    bindFlow()
    AILog.i(TAG, "start")
  }

  fun pause() {
    audioManager.pause()
    playbackFileManager.flush()
    if(MeetingConfig.isMp3().not()) {
      pcmFileManager.flush()
    }
    AILog.i(TAG, "pause")
  }

  fun resume() {
    audioManager.resume()
    AILog.i(TAG, "resume")
  }

  fun stop(finish: () -> Unit = {}) {
    AILog.i(TAG, "stop begin")
    audioManager.stop {
      playbackFileManager.flush()
      playbackFileManager.close()
      if(MeetingConfig.isMp3().not()) {
        pcmFileManager.flush()
        pcmFileManager.close()
      }
      job?.cancel()
      audioChangeListener?.invoke(true, playbackFileManager.fileLength())
      AILog.i(TAG, "stop end")
      finish?.invoke()
    }
  }

  fun randomReadBuffer(offset: Long, buffer: ByteArray): Int {
    return playbackFileManager.randomReadBuffer(offset, buffer)
  }

  fun fileLength(): Long {
    return playbackFileManager.fileLength()
  }

  fun getDuration() = audioManager.getDuration()
  fun getStateFlow() = audioManager.getStateFlow()
  fun getMp3DataFlow() = audioManager.getMp3DataFlow()
  fun getPCMDataFlow() = audioManager.getPCMDataFlow()
  fun getDurationFlow() = audioManager.getDurationFlow()
  fun getIntensityFlow() = audioManager.getIntensityFlow()

}