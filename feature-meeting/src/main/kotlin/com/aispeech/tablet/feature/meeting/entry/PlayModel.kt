package com.aispeech.tablet.feature.meeting.entry

import com.aispeech.tablet.core.res.TabletStringsUtils

enum class PlayerStateType(val description: String) {
  Start(TabletStringsUtils.getString(com.aispeech.tablet.core.res.R.string.runtimeactivity_1739176348215_1)),
  Pause(TabletStringsUtils.getString(com.aispeech.tablet.core.res.R.string.recordstate_1739006582133_4)),
  Resume(TabletStringsUtils.getString(com.aispeech.tablet.core.res.R.string.runtimeactivity_1739176348215_0)),
  End(TabletStringsUtils.getString(com.aispeech.tablet.core.res.R.string.recordstate_1739006582133_5)),
  Error(TabletStringsUtils.getString(com.aispeech.tablet.core.res.R.string.playmodel_1739176917217_0)),
}
object PlayerModel {
  //播放音频的id
  var PlayerID:String = "";

  //播放音频的路径
  var audioPath:String = "";

  //滑动条重点位置
  var pointList:ArrayList<Int> = ArrayList<Int>();

  //音频总时长
  var audioduration:Long = 0;

  //是否在播放状态
  var isPlaying:Boolean = false;

  //表示播放音频在播放列表的具体位置
  var Index:Int = 0;

}