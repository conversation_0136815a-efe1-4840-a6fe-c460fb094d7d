package com.aispeech.tablet.feature.meeting.domain.find

import com.aispeech.aibase.AILog
import com.aispeech.tablet.core.meeting.domain.NoteMeetingDataManager
import com.aispeech.tablet.core.model.entity.ai.AINoteItemData
import com.aispeech.tablet.feature.meeting.data.MatchPosition
import com.aispeech.tablet.feature.meeting.utils.SearchUtils.findMatchesInText
import com.aispeech.tablet.lib.markdown.view.MarkdownRenderer
import com.blankj.utilcode.util.Utils
import dagger.hilt.android.scopes.ActivityRetainedScoped
import kotlinx.coroutines.flow.first
import javax.inject.Inject

@ActivityRetainedScoped
class AiSummaryStrategy @Inject constructor(
  private val meetingDataManager: NoteMeetingDataManager,
): FindStrategy {
  private var currentQuery: String = ""

  private var dataSnapshot: List<AINoteItemData> = emptyList()
  private var snapshotMatchingCompositeKeys: List<String> = emptyList()
  private val snapshotMatchesCache = mutableMapOf<String, List<MatchPosition>>()
  private var keyToIndexMap: Map<String, Int> = emptyMap()

  override fun getLogPrefix(): String = "[Summary]"
  override fun updateDataSourceForTarget(targetLocation: Int) {
    AILog.d(TAG, "${getLogPrefix()} 更新数据源，目标位置：$targetLocation")
  }

  override fun resetDataSource() {
    AILog.d(TAG, "${getLogPrefix()} 重置数据源")
  }


  override suspend fun search(query: String, noteId: String): SearchResultSummary {
    resetSearchInternal()

    if (query.isBlank()) {
      return SearchResultSummary(0, 0)
    }

    currentQuery = query
    dataSnapshot = meetingDataManager.aiNoteItems.first()
    AILog.i(TAG, "${getLogPrefix()} 数据快照获取完毕，包含 ${dataSnapshot.size} 项。")

    keyToIndexMap = dataSnapshot.withIndex().associate { (index, item) -> item.compositeKey to index }

    snapshotMatchingCompositeKeys = dataSnapshot
      .filter { it.content.contains(query, ignoreCase = true) }
      .map { it.compositeKey }
    AILog.i(TAG, "${getLogPrefix()} 在快照中找到 ${snapshotMatchingCompositeKeys.size} 个匹配项的 compositeKey")

    var totalMatches = 0
    snapshotMatchingCompositeKeys.forEach { key ->
      totalMatches += getItemMatchesInternal(key, query).size
    }
    AILog.i(TAG, "${getLogPrefix()} 基于快照计算总匹配数: $totalMatches")

    return SearchResultSummary(snapshotMatchingCompositeKeys.size, totalMatches)
  }

  override suspend fun refreshSearchAndUpdateResults(): SearchResultSummary {
    if (currentQuery.isBlank()) {
      AILog.w(TAG, "${getLogPrefix()} refresh: 当前无查询词，无法刷新。")
      resetSearchInternal()
      return SearchResultSummary(0, 0)
    }
    AILog.i(TAG, "${getLogPrefix()} refresh: 使用查询词 '$currentQuery' 刷新结果。")
    return search(currentQuery, "")
  }

  override fun resetSearch() {
    resetSearchInternal()
  }

  private fun resetSearchInternal() {
    currentQuery = ""
    dataSnapshot = emptyList()
    snapshotMatchingCompositeKeys = emptyList()
    snapshotMatchesCache.clear()
    keyToIndexMap = emptyMap()
  }

  override fun getResultCount(): Int = snapshotMatchingCompositeKeys.size
  override suspend fun getResultItemDetails(
    searchResultIndex: Int,
    query: String
  ): SearchResultItemDetails? {
    val itemId = snapshotMatchingCompositeKeys.getOrNull(searchResultIndex) ?: return null

    val displayPosition = keyToIndexMap[itemId]
    val matches = getItemMatchesInternal(itemId, query)
    return SearchResultItemDetails(itemId, displayPosition, matches)
  }

  override suspend fun getActivePlayPosition(
    segmentIndex: Int,
    matchPosition: Int
  ): PlaySeekParams? {
    AILog.i(TAG, "getActivePlayPosition: $segmentIndex, position: $matchPosition")
    if (segmentIndex == -1 || segmentIndex > dataSnapshot.size - 1) return null

    val key = snapshotMatchingCompositeKeys.getOrNull(segmentIndex) ?: return null
    val segment = dataSnapshot.find { it.compositeKey == key } ?: return null

    return PlaySeekParams(
      id = segment.objectId,
      begin = segment.begin
    )
  }

  private fun getItemMatchesInternal(compositeKey: String, query: String): List<MatchPosition> {
    return snapshotMatchesCache[compositeKey] ?: run {
      val itemIndex = keyToIndexMap[compositeKey]
      val item = itemIndex?.let { dataSnapshot.getOrNull(it) }
      val textContent = item?.content ?: ""

      val parsedContent = MarkdownRenderer.getInstance(Utils.getApp()).toSpannableStringBuilder(textContent).toString()
      val matches = findMatchesInText(parsedContent, query)
      snapshotMatchesCache[compositeKey] = matches
      matches
    }
  }

  companion object {
    const val TAG = "AiSummaryStrategy"
  }

}