package com.aispeech.tablet.feature.meeting.model

import com.aispeech.tablet.core.res.TabletStringsUtils

data class NoteLanguageMenu(
  val title: String = TabletStringsUtils.getString(com.aispeech.tablet.core.res.R.string.languagemenustate_1739176974426_0),
  val list: List<LanguageMenuItem>
) {

  companion object {
    val source = NoteLanguageMenu(
      title = TabletStringsUtils.getString(com.aispeech.tablet.core.res.R.string.languagemenustate_1739176974426_0),
      list = LanguageMenuItem.sourceItems
    )
    val mixed = NoteLanguageMenu(
      title = TabletStringsUtils.getString(com.aispeech.tablet.core.res.R.string.languagemenustate_1739176974426_1),
      list = LanguageMenuItem.mixItems
    )
    val offlineSource = NoteLanguageMenu(
      title = TabletStringsUtils.getString(com.aispeech.tablet.core.res.R.string.languagemenustate_1739176974426_0),
      list = LanguageMenuItem.offlineDataSource,
    )
    val offlineMixedSource = NoteLanguageMenu(
      title = TabletStringsUtils.getString(com.aispeech.tablet.core.res.R.string.languagemenustate_1739176974426_1),
      list = LanguageMenuItem.offlineMixSource,
    )

    val all = listOf(source, mixed)
    val offlineAll = listOf(offlineSource, offlineMixedSource)
  }
}


sealed class LanguageMenuItem(val enabled: Boolean, open val title: String = "") {

  data class Section(override val title: String) : LanguageMenuItem(title = title, enabled = false)
  data class Content(
    val source: LanguageMenuData,
    val target: LanguageMenuData = LanguageMenuData.empty,
    val useSource: Boolean = true,
  ) : LanguageMenuItem(
    title = "",
    enabled = true
  ) {

    val value = source.value

    override val title: String
      get() {
        return buildString {
          if (useSource) {
            append(source.title)
          } else {
            append(target.group.title)
          }
        }
      }

    private fun targetIsEmpty(): Boolean {
      return target == LanguageMenuData.empty
    }

    fun getTarget(): String? {
      if (target.eqEmpty()) return null
      return target.value
    }

    fun getText(isTranslate: Boolean): String {
      return when (isTranslate) {
        false -> source.title
        else -> target.group.title
      }
    }
  }


  companion object {
    val defaultItem =
      Content(source = LanguageMenuData.chineseData, target = LanguageMenuData.empty)

    val sourceItems = buildList {
      add(Section(title = TabletStringsUtils.getString(com.aispeech.tablet.core.res.R.string.languagemenustate_1739176974426_2)))

      val chinese = LanguageMenuData.sourceData.filter { it.group == LanguageMenuGroup.Mandarin }
      addAll(chinese.filter { it.popular }.map { Content(source = it) })
      add(2, Content(source = LanguageMenuData.englishData))
      add(3, Content(source = LanguageMenuData.mixData))
      add(Section(title = TabletStringsUtils.getString(com.aispeech.tablet.core.res.R.string.languagemenustate_1739176974426_3)))
      addAll(chinese.filter { !it.popular }.map { Content(source = it) })
      val other =
        LanguageMenuData.sourceData.filter { it.group == LanguageMenuGroup.Other && it != LanguageMenuData.englishData }
      add(Section(TabletStringsUtils.getString(com.aispeech.tablet.core.res.R.string.languagemenustate_1739176974426_4)))
      addAll(other.map { Content(it) })
    }

    val mixItems = buildList {
      add(Section(TabletStringsUtils.getString(com.aispeech.tablet.core.res.R.string.languagemenustate_1739176974426_5)))
      val groupedLanguages = LanguageMenuData.sourceData.groupBy { it.group }

      fun addContentForGroup(group: LanguageMenuGroup, targets: List<LanguageMenuData>) {
        groupedLanguages[group]?.let { languages ->
          targets.forEach { target ->
            addAll(languages.map { source ->
              Content(source = source, target = target, useSource = false)
            })
          }
        }
      }

      // Mandarin languages
      addContentForGroup(
        LanguageMenuGroup.Mandarin,
        listOf(LanguageMenuData.empty, LanguageMenuData.englishData)
      )

      // Mix languages
      addContentForGroup(
        LanguageMenuGroup.Mix,
        listOf(LanguageMenuData.empty, LanguageMenuData.englishData, LanguageMenuData.chineseData)
      )

      // Other languages
      addContentForGroup(
        LanguageMenuGroup.Other,
        listOf(LanguageMenuData.empty, LanguageMenuData.chineseData)
      )
    }

    val offlineDataSource = buildList {
      add(Section(title = TabletStringsUtils.getString(com.aispeech.tablet.core.res.R.string.languagemenustate_1739176974426_2)))
      LanguageMenuData.offlineDataSource.forEach {
        add(Content(source = it))
      }
    }

    val offlineMixSource = buildList {
      add(Section(TabletStringsUtils.getString(com.aispeech.tablet.core.res.R.string.languagemenustate_1739176974426_5)))
      LanguageMenuData.offlineDataSource.forEach {
        add(Content(source = it, target = LanguageMenuData.empty, useSource = false))
      }
      add(Content(source = LanguageMenuData.chineseData, target = LanguageMenuData.englishData, useSource = false))
      add(Content(source = LanguageMenuData.englishData, target = LanguageMenuData.chineseData, useSource = false))
    }

  }


}


data class LanguageMenuData(
  val title: String,
  val value: String = "cn",
  val group: LanguageMenuGroup,
  val popular: Boolean = false,
) {

  fun eqEmpty() = this == empty

  fun isChinese() = this.value == "cn"

  companion object {
    val chineseData =
      LanguageMenuData(
        title = TabletStringsUtils.getString(com.aispeech.tablet.core.res.R.string.languagemenustate_1739176974426_6),
        value = "cn",
        group = LanguageMenuGroup.Mandarin,
        popular = true
      )
    val empty =
      LanguageMenuData(title = TabletStringsUtils.getString(com.aispeech.tablet.core.res.R.string.languagemenustate_1739176974426_7), value = "", group = LanguageMenuGroup.Empty)

    val englishData =
      LanguageMenuData(
        title = TabletStringsUtils.getString(com.aispeech.tablet.core.res.R.string.languagemenustate_1739176974426_8),
        value = "en",
        group = LanguageMenuGroup.Other,
        popular = true
      )

    val mixData = LanguageMenuData(
      title = TabletStringsUtils.getString(com.aispeech.tablet.core.res.R.string.languagemenustate_1739176974426_9),
      value = "ce",
      group = LanguageMenuGroup.Mix,
      popular = true
    )

    val sourceData = listOf(
      chineseData,
      mixData,
      LanguageMenuData(title = TabletStringsUtils.getString(com.aispeech.tablet.core.res.R.string.languagemenustate_1739176974426_10), value = "suzhou", group = LanguageMenuGroup.Mandarin),
      LanguageMenuData(title = TabletStringsUtils.getString(com.aispeech.tablet.core.res.R.string.languagemenustate_1739176974426_11), value = "shanghai", group = LanguageMenuGroup.Mandarin),
      LanguageMenuData(
        title = TabletStringsUtils.getString(com.aispeech.tablet.core.res.R.string.languagemenustate_1739176974426_12),
        value = "sichuan",
        group = LanguageMenuGroup.Mandarin,
        popular = true
      ),
      LanguageMenuData(
        title = TabletStringsUtils.getString(com.aispeech.tablet.core.res.R.string.languagemenustate_1739176974426_13),
        value = "yueyu",
        group = LanguageMenuGroup.Mandarin,
        popular = true
      ),
      LanguageMenuData(title = TabletStringsUtils.getString(com.aispeech.tablet.core.res.R.string.languagemenustate_1739176974426_14), value = "hubei", group = LanguageMenuGroup.Mandarin),
      LanguageMenuData(title = TabletStringsUtils.getString(com.aispeech.tablet.core.res.R.string.languagemenustate_1739176974426_15), value = "shanxi", group = LanguageMenuGroup.Mandarin),
      LanguageMenuData(title = TabletStringsUtils.getString(com.aispeech.tablet.core.res.R.string.languagemenustate_1739176974426_16), value = "henan", group = LanguageMenuGroup.Mandarin),
      LanguageMenuData(title = TabletStringsUtils.getString(com.aispeech.tablet.core.res.R.string.languagemenustate_1739176974426_17), value = "tianjin", group = LanguageMenuGroup.Mandarin),
      LanguageMenuData(title = TabletStringsUtils.getString(com.aispeech.tablet.core.res.R.string.languagemenustate_1739176974426_18), value = "dongbei", group = LanguageMenuGroup.Mandarin),
      LanguageMenuData(title = TabletStringsUtils.getString(com.aispeech.tablet.core.res.R.string.languagemenustate_1739176974426_19), value = "gansu", group = LanguageMenuGroup.Mandarin),
      LanguageMenuData(title = TabletStringsUtils.getString(com.aispeech.tablet.core.res.R.string.languagemenustate_1739176974426_20), value = "guizhou", group = LanguageMenuGroup.Mandarin),
      LanguageMenuData(title = TabletStringsUtils.getString(com.aispeech.tablet.core.res.R.string.languagemenustate_1739176974426_21), value = "yunnan", group = LanguageMenuGroup.Mandarin),
      LanguageMenuData(title = TabletStringsUtils.getString(com.aispeech.tablet.core.res.R.string.languagemenustate_1739176974426_22), value = "jiangxi", group = LanguageMenuGroup.Mandarin),
      LanguageMenuData(title = TabletStringsUtils.getString(com.aispeech.tablet.core.res.R.string.languagemenustate_1739176974426_23), value = "guangxi", group = LanguageMenuGroup.Mandarin),
      LanguageMenuData(title = TabletStringsUtils.getString(com.aispeech.tablet.core.res.R.string.languagemenustate_1739176974426_24), value = "ningxia", group = LanguageMenuGroup.Mandarin),
      LanguageMenuData(title = TabletStringsUtils.getString(com.aispeech.tablet.core.res.R.string.languagemenustate_1739176974426_25), value = "hunan", group = LanguageMenuGroup.Mandarin),
      LanguageMenuData(title = TabletStringsUtils.getString(com.aispeech.tablet.core.res.R.string.languagemenustate_1739176974426_26), value = "minnan", group = LanguageMenuGroup.Mandarin),
      englishData,
      LanguageMenuData(title = TabletStringsUtils.getString(com.aispeech.tablet.core.res.R.string.languagemenustate_1739176974426_27), value = "de", group = LanguageMenuGroup.Other),
      LanguageMenuData(title = TabletStringsUtils.getString(com.aispeech.tablet.core.res.R.string.languagemenustate_1739176974426_28), value = "ja", group = LanguageMenuGroup.Other),
      LanguageMenuData(title = TabletStringsUtils.getString(com.aispeech.tablet.core.res.R.string.languagemenustate_1739176974426_29), value = "ko", group = LanguageMenuGroup.Other),
      LanguageMenuData(title = TabletStringsUtils.getString(com.aispeech.tablet.core.res.R.string.languagemenustate_1739176974426_30), value = "fr", group = LanguageMenuGroup.Other),
      LanguageMenuData(title = TabletStringsUtils.getString(com.aispeech.tablet.core.res.R.string.languagemenustate_1739176974426_31), value = "ru", group = LanguageMenuGroup.Other),
      LanguageMenuData(title = TabletStringsUtils.getString(com.aispeech.tablet.core.res.R.string.languagemenustate_1739176974426_32), value = "it", group = LanguageMenuGroup.Other),
      LanguageMenuData(title = TabletStringsUtils.getString(com.aispeech.tablet.core.res.R.string.languagemenustate_1739176974426_33), value = "tr", group = LanguageMenuGroup.Other),
      LanguageMenuData(title = TabletStringsUtils.getString(com.aispeech.tablet.core.res.R.string.languagemenustate_1739176974426_34), value = "es", group = LanguageMenuGroup.Other),
//      LanguageMenuData(title = TabletStringsUtils.getString(com.aispeech.tablet.core.res.R.string.languagemenustate_1739176974426_35), value = "ar", group = LanguageMenuGroup.Other),
    )

    val offlineDataSource = listOf(
      chineseData,
      englishData,
      LanguageMenuData(title = TabletStringsUtils.getString(com.aispeech.tablet.core.res.R.string.languagemenustate_1739176974426_34), value = "es", group = LanguageMenuGroup.Other),
      LanguageMenuData(title = TabletStringsUtils.getString(com.aispeech.tablet.core.res.R.string.languagemenustate_1739176974426_31), value = "ru", group = LanguageMenuGroup.Other),
      LanguageMenuData(title = TabletStringsUtils.getString(com.aispeech.tablet.core.res.R.string.languagemenustate_1739176974426_32), value = "it", group = LanguageMenuGroup.Other),
      LanguageMenuData(title = TabletStringsUtils.getString(com.aispeech.tablet.core.res.R.string.languagemenustate_1739176974426_27), value = "de", group = LanguageMenuGroup.Other),
      LanguageMenuData(title = TabletStringsUtils.getString(com.aispeech.tablet.core.res.R.string.languagemenustate_1739176974426_30), value = "fr", group = LanguageMenuGroup.Other),
      LanguageMenuData(title = TabletStringsUtils.getString(com.aispeech.tablet.core.res.R.string.languagemenustate_1739176974426_28), value = "ja", group = LanguageMenuGroup.Other),
      LanguageMenuData(title = TabletStringsUtils.getString(com.aispeech.tablet.core.res.R.string.languagemenustate_1739176974426_29), value = "ko", group = LanguageMenuGroup.Other),
    )
  }
}

sealed class LanguageMenuGroup(
  open val title: String = "",
) {
  data object Mandarin : LanguageMenuGroup(title = TabletStringsUtils.getString(com.aispeech.tablet.core.res.R.string.languagemenustate_1739176974426_36))
  data object Other : LanguageMenuGroup(title = TabletStringsUtils.getString(com.aispeech.tablet.core.res.R.string.languagemenustate_1739176974426_37))
  data object Mix : LanguageMenuGroup(title = TabletStringsUtils.getString(com.aispeech.tablet.core.res.R.string.languagemenustate_1739176974426_38))
  data object Empty : LanguageMenuGroup(title = TabletStringsUtils.getString(com.aispeech.tablet.core.res.R.string.languagemenustate_1739176974426_7))
  data object Offline : LanguageMenuGroup(title = TabletStringsUtils.getString(com.aispeech.tablet.core.res.R.string.languagemenustate_1750246761144_01))
}
