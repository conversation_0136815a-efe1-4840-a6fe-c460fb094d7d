package com.aispeech.tablet.feature.meeting.domain.offline

import com.aispeech.aibase.AILog
import com.aispeech.aimeeting.data.repository.OfflineTranscribeRepository
import com.aispeech.lib_ktx.getOrThrow
import com.aispeech.tablet.core.meeting.data.repository.RecordPreferenceRepository
import com.aispeech.tablet.core.meeting.entity.RuntimeDataItemV2
import com.aispeech.tablet.core.res.TabletStringsUtils
import com.aispeech.tablet.feature.meeting.mapper.OfflineTaskResultMapper
import com.aispeech.tablet.feature.meeting.model.OfflineTaskData
import com.aispeech.tablet.preferences.Preferences
import kotlinx.coroutines.coroutineScope
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.isActive

sealed class OfflineTaskStatus {
  data object Started : OfflineTaskStatus()
  data class Processing(val progress: Int) : OfflineTaskStatus()
  data class Finished(
    val data: List<RuntimeDataItemV2> = emptyList(),
    val throwable: Throwable? = null,
    val taskData: OfflineTaskData,
  ) : OfflineTaskStatus()
}

interface Task {
  suspend fun execute(): Flow<OfflineTaskStatus>
}

class OfflineTask(
  val data: OfflineTaskData,
  val repository: OfflineTranscribeRepository,
  private val preferenceRepository: RecordPreferenceRepository,
) : Task {

  override fun toString(): String {
    return "OfflineTask[data = $data]"
  }

  private suspend fun taskStart() {
    val (taskId, _, language, translateLanguage) = data
    repository.offlineTaskSubmitV2(
      taskId = taskId,
      language = language,
      translate = translateLanguage,
      modelId = if (preferenceRepository.selectAsrConfig != "-1") {
        preferenceRepository.selectAsrConfig
      } else null,
      enableSmooth = Preferences.smoothSpeech.value ?: true
    ).getOrThrow()
  }

  private suspend fun takeWhenTaskProcessed(update: suspend (Int) -> Unit) = coroutineScope {
    while (isActive) {
      val result = taskProcessing(update)
      if (result != null) {
        return@coroutineScope result
      } else {
        delay(10000L)
      }
    }
    return@coroutineScope false
  }

  private suspend fun taskProcessing(update: suspend (Int) -> Unit): Boolean? {
    val response = repository.offlineTaskProgressV2(data.taskId).getOrThrow()
    return when (response.status) {
      1 -> {
        update.invoke(response.progress)
        return null
      }

      2 -> true
      else -> {
        AILog.d(TAG, "taskProcessing: status = ${response.status},reason = ${response.reason}")
        false
      }
    }
  }

  private suspend fun taskResult(): List<RuntimeDataItemV2> {
    val result = repository.offlineTaskResultV2(data.taskId).getOrThrow()
    return OfflineTaskResultMapper.mapToRuntimeData(
      list = result,
      language = data.language,
      translateLanguage = data.translateLanguage
    )
  }

  override suspend fun execute(): Flow<OfflineTaskStatus> = flow {
    runCatching {
      taskStart()
      emit(OfflineTaskStatus.Started)
      val success = takeWhenTaskProcessed {
        emit(OfflineTaskStatus.Processing(it))
      }
      if (!success) {
        emit(OfflineTaskStatus.Finished(throwable = Exception(TabletStringsUtils.getString(com.aispeech.tablet.core.res.R.string.offlinetask_1739176765515_0)), taskData = data))
      } else {
        val result = taskResult()
        emit(OfflineTaskStatus.Finished(result, taskData = data))
      }
    }.onFailure {
      AILog.e(TAG, "execute Failure : ${it.message} \n ${it.stackTraceToString()}")
      emit(OfflineTaskStatus.Finished(throwable = it, taskData = data))
    }
  }

  private suspend fun <T> retryWithDelay(
    maxRetries: Int = Int.MAX_VALUE,
    delay: Long = 5000,
    block: suspend () -> T
  ): T {
    var currentAttempt = 0

    while (currentAttempt < maxRetries) {
      try {
        return block()
      } catch (e: Throwable) {
        AILog.i(TAG, "retryWithDelay-exception: $e")
        currentAttempt++
        if (currentAttempt >= maxRetries) {
          throw e // 超过最大重试次数后抛出异常
        }
        delay(delay)
      }
    }
    throw IllegalStateException("Unreachable code")
  }

  companion object {
    const val TAG = "OfflineTask"
  }

}