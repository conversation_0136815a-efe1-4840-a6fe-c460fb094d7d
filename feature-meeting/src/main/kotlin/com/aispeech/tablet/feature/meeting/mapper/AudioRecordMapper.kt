package com.aispeech.tablet.feature.meeting.mapper

import com.aispeech.tablet.core.model.entity.audio.AudioRecordInfo
import com.aispeech.tablet.lib.notesync.domain.AudioTaskItem

object AudioRecordMapper {
    fun toTaskItem(
        recordInfo: AudioRecordInfo,
        noteId: String,
        fromExport: Boolean = false,
        audioPath: String,
    ): AudioTaskItem {
        return AudioTaskItem(
            noteId = noteId,
            objectId = recordInfo.objectId!!,
            duration = recordInfo.duration,
            filePath = audioPath,
            markPoints = recordInfo.markPoints,
            uploadPath = recordInfo.tempUploadPath!!,
            fromExport = fromExport
        )
    }
}