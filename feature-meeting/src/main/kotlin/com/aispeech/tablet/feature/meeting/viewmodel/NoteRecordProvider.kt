package com.aispeech.tablet.feature.meeting.viewmodel

import android.annotation.SuppressLint
import com.aispeech.aibase.AILog
import com.aispeech.aimeeting.api.MeetingConfig
import com.aispeech.aimeeting.api.sockets.WebSocketState
import com.aispeech.aimeeting.data.source.RecordingState
import com.aispeech.aimeeting.extensions.takeOrTimeoutEq
import com.aispeech.aiplayback.listener.IUploaderListener
import com.aispeech.lib_ktx.FlowKtxScope
import com.aispeech.lib_ktx.emit
import com.aispeech.lib_ktx.stateFlowOf
import com.aispeech.lib_ktx.tryEmit
import com.aispeech.tablet.core.common.ActionShutdownProvider
import com.aispeech.tablet.core.common.DeviceUtils
import com.aispeech.tablet.core.common.FileCacheUtils
import com.aispeech.tablet.core.common.Tablet
import com.aispeech.tablet.core.common.showShortToast
import com.aispeech.tablet.core.meeting.domain.NoteRecordingDataManager
import com.aispeech.tablet.core.meeting.entity.ConversationRow2
import com.aispeech.tablet.core.meeting.entity.RecordingMarkResult
import com.aispeech.tablet.core.meeting.entity.RuntimeDataMixItem
import com.aispeech.tablet.core.model.entity.TranscriptionMarkdownTag
import com.aispeech.tablet.core.model.entity.audio.AudioRecordInfo
import com.aispeech.tablet.core.model.entity.audio.CreateRecordResponse
import com.aispeech.tablet.core.model.entity.audio.RecordState
import com.aispeech.tablet.core.network.config.NetWorkConfig
import com.aispeech.tablet.core.res.TabletStringsUtils
import com.aispeech.tablet.feature.meeting.data.RecordEndNotification
import com.aispeech.tablet.feature.meeting.domain.MixAudioSocketManager
import com.aispeech.tablet.feature.meeting.domain.MixMultiEndAudioManager
import com.aispeech.tablet.feature.meeting.encoder.AudioEncoderManager
import com.aispeech.tablet.feature.meeting.model.LanguageMenuItem
import com.aispeech.tablet.feature.meeting.preference.NoteRecordPreference
import com.aispeech.tablet.feature.meeting.utils.RecordAnalyseUtils
import com.aispeech.tablet.feature.meeting.viewmodel.NoteRecordProvider.Companion.CONNECT_TIMEOUT
import com.aispeech.tablet.lib.connectivity.Connectivity
import com.aispeech.tablet.lib.notesync.domain.NoteOssUploadAudioManager
import com.aispeech.tablet.lib.system.VoiceGuardian
import com.aispeech.tablet.lib.system.bluetooth.utils.BtHelper
import com.aispeech.tablet.preferences.Preferences
import com.aispeech.tablet.preferences.UserEmpty
import com.blankj.utilcode.util.FileUtils
import com.blankj.utilcode.util.NetworkUtils
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.launch
import java.io.File
import java.io.FileOutputStream
import javax.inject.Inject
import javax.inject.Singleton
import kotlin.math.abs

@SuppressLint("MissingPermission")
@Singleton
class NoteRecordProvider @Inject constructor(
  private val connectivityManager: Connectivity,
  private val adapter: HybridSpeechAdapter,
  private val audioDataManager: MixMultiEndAudioManager,
  private val audioSocketManager: MixAudioSocketManager,
  private val dataManager: NoteRecordingDataManager,
  private val ossUploadAudioManager: NoteOssUploadAudioManager,
  private val encoderManager: AudioEncoderManager,
) : FlowKtxScope {

  private val scope = Tablet.workScope

  // preferences
  private val preferences: Preferences = Preferences

  // record
  private var meetingJob: Job? = null
  private var audioRecordInfo: AudioRecordInfo? = null

  //save debug encode audio
  private var encodeOutputStream: FileOutputStream? = null

  /**
   * 临时储存 socket 状态
   */
  private var socketState: WebSocketState? = null

  /**
   * 连接超时计数器
   * 如果连接断开超过 [CONNECT_TIMEOUT] 分钟，将影响续传的逻辑
   */
  private var connectTimeoutCountDownJob: Job? = null

  /**
   * 选择的语言
   */
  val selectLanguage = stateFlowOf(LanguageMenuItem.defaultItem)

  // outer
//  val runtimeMessages = audioSocketManager.messageItems
  val realtimeAgendaMessages = adapter.getAllAgentFlow()
  val currentLine = adapter.getCurrentLineMix()

  val pagingItemFlow = audioSocketManager.pagingItemFlow
  fun pagingItemFlowV2(cacheScope: CoroutineScope, targetKey: Int? = null) = adapter.pagingItemFlowV2(cacheScope, targetKey)

  val connectivity = connectivityManager.getNetworkStatusFlow()
  val endNotificationFlow: StateFlow<RecordEndNotification> = stateFlowOf(
    RecordEndNotification.Normal
  )

  val updateMarkTime = stateFlowOf(Int.MAX_VALUE)
  val recordingState: StateFlow<RecordState> = stateFlowOf(RecordState.IDLE)
  val recordDuration = adapter.uiState.map { it.durationMs }

  val socketQualityFlow = audioSocketManager.socketStateFlow

  private var lastTriggerUploadFileLength: Long = 0

  // getter
  private val userId: Long
    get() = preferences.userId.value!!

  private val hasConnected: Boolean
    get() = connectivityManager.isConnected()

  private val logged: Boolean
    get() = userId != Long.UserEmpty

  val recordingNoteId: String?
    get() {
      return if (this::noteId.isInitialized) noteId else null
    }

  val recordingInfo: AudioRecordInfo?
    get() = if (recordingState.value.isMeeting()) audioRecordInfo else null

  // late init
  private lateinit var noteId: String
  private lateinit var pageFlow: StateFlow<String>
  private var onMeetingMaxFinished: (() -> Unit)? = null

  init {
    scope.launch {
      ossUploadAudioManager.initAudioAppendUploader()

      launch {
        connectivityManager.getNetworkStatusFlow().collect {
          val connected = NetworkUtils.isConnected()
          listenNetworkChanged(connected)
        }
      }
      launch {
        ActionShutdownProvider.doSomethingWhenShutdown(TAG) {
          scope.launch {
            stop(withBack = false, focus = true, reason = "shutdown") {}
          }
        }
      }
      launch {
        if (VoiceGuardian.support()) {
          VoiceGuardian.listenDeviceChange().collect {
            AILog.i(
              TAG,
              "listenDeviceChange-state: ${recordingState.value}  device: ${BtHelper.printDevice(it)}"
            )
            if (recordingState.value.isMeeting()) {
              VoiceGuardian.stopVoiceRecognition()
              VoiceGuardian.startVoiceRecognition()
            }
          }
        }

      }
    }
  }

  private fun onLanguageChanged(item: LanguageMenuItem.Content) {
    AILog.i(TAG, "onLanguageChanged-item: $item")
    scope.launch {
      selectLanguage.emit(item)
    }
  }

  fun clearLastNoteState() {
    AILog.i(TAG, "clearLastNoteState")
    resetRecordState()

    scope.launch {
      updateMarkTime.emit(Int.MAX_VALUE)
    }
  }

  /**
   * 监听网络变化
   */
  private fun listenNetworkChanged(connected: Boolean) {
    val state = recordingState.value
    AILog.i(TAG, "listenNetworkChanged triggered. Connected: $connected, State: $state, SocketStopped: ${audioSocketManager.socketStopped}, SocketReady: ${audioSocketManager.isReady()}")

    scope.launch {
      // 处理暂停状态
      if (state.isPaused()) {
        AILog.i(TAG, "State is Paused. Disconnecting socket.")
        audioSocketManager.disconnect(focus = true)
        return@launch
      }

      // 处理录制状态
      if (state.isRecording() && !audioSocketManager.socketStopped) {
        if (connected) {
          if (!audioSocketManager.isReady()) {
            // 网络已连接，但 Socket 未就绪 -> 尝试重连
            AILog.i(TAG, "Recording, Network UP, but Socket NOT Ready. Reconnecting...")
            audioSocketManager.disconnect(focus = true) // 先断开旧连接
            socketStart() // 启动新连接
            if (logged) {
              AILog.i(TAG, "User logged in, starting connect timeout countdown.")
              startConnectTimeoutCountDown() // 启动超时计时(以决定是否需要续传）
            }
          } else {
            AILog.i(TAG, "Recording, Network UP, Socket IS Ready.")
          }
        } else {
          AILog.i(TAG, "Recording, but Network DOWN. Disconnecting socket.")
          audioSocketManager.disconnect(focus = true)
          if (logged) {
            AILog.i(TAG, "User logged in, starting connect timeout countdown.")
            startConnectTimeoutCountDown() // 启动超时计时(以决定是否需要续传）
          }
        }
        return@launch // 处理完毕，退出协程
      }
      AILog.d(TAG, "No action needed for state: $state or socket already stopped.")
    }
  }

//  /**
//   * 绑定数据流
//   */
//  @OptIn(FlowPreview::class)
//  private fun bindMeetingFlow() {
//    meetingJob = scope.launch {
//      launch {
//        if (MeetingConfig.isMp3()) {
//          audioDataManager.getMp3DataFlow().collect {
//            coroutineContext.ensureActive()
//            audioSocketManager.send(it)
//          }
//        } else {
//          audioDataManager.getPCMDataFlow().collect {
//            coroutineContext.ensureActive()
////            AILog.i(TAG, "pcm: ${it.size}")
//            if (MeetingConfig.isPcm()) {
//              audioSocketManager.send(it)
//            } else {
//              encoderManager.feed(it)
//            }
//          }
//        }
//      }
//      launch {
//        // 一秒一次
//        var lastDurationUpdateTs = 0L
//        audioDataManager.getDurationFlow()/*.sample(1000L)*/.collect {
//          coroutineContext.ensureActive()
//
//          if (System.currentTimeMillis() - lastDurationUpdateTs >= 1000) {
//            lastDurationUpdateTs = System.currentTimeMillis()
//            recordDuration.emit(it)
//            audioSocketManager.updateRecordDuration(it)
//
//            updateRecordInfo(it)
//            listenRecordDuration(it)
//          }
//        }
//      }
//      launch {
//        audioDataManager.getStateFlow().collect {
//          coroutineContext.ensureActive()
//          AILog.i(TAG, "bindMeetingFlow-state: $it")
//        }
//      }
//      launch {
//        audioSocketManager.stateFlow.collect {
//          coroutineContext.ensureActive()
//          AILog.i(TAG, "audioSocketFlow-state: $it")
//          when (it) {
//            WebSocketState.CONNECTED -> {
//              cancelConnectTimeoutCountDown()
//              var printCount = 0
//              //ogg重连续传需要重新编码
//              if (MeetingConfig.isOggOpus()) {
//                encoderManager.initWithEncoder(AudioEncoderManager.EncoderType.OggOpus, 16000, 1,
//                  onEncoderBuffer = { data, _ ->
//                    if (printCount++ % 50 == 0) {
//                      AILog.i(TAG, "print encoder: ${data.size}")
//                    }
//                    audioSocketManager.send(data)
//
//                    try {
//                      encodeOutputStream?.write(data)
//                    } catch (e: IOException) {
//                      AILog.i(
//                        TAG,
//                        "onEncoderBuffer: Attempted to write to a closed debug stream. $e"
//                      )
//                    } catch (e: Exception) {
//                      AILog.e(
//                        TAG,
//                        "onEncoderBuffer: Unexpected exception during debug stream write. $e"
//                      )
//                    }
//                  })
//                encoderManager.start()
//              }
//            }
//
//            is WebSocketState.DISCONNECTED -> {
//              if (MeetingConfig.isOggOpus()) {
//                encoderManager.stop()
//                encoderManager.destroy()
//              }
//            }
//
//            else -> Unit
//          }
//          socketState = it
//        }
//      }
//    }
//  }

  /**
   * socket 连接前的准备
   */
  private fun socketPrepare() {
    AILog.d(TAG, "socketPrepare: ")
    if (!logged) return
    audioSocketManager.prepare()
  }

  /**
   * 开始建立链接
   */
  private fun socketStart(firstStart: Boolean = false) {
    AILog.i(TAG, "socketStart-logged: $logged, connected: $hasConnected, firstStart: $firstStart")
    if (!logged) return
    audioRecordInfo?.let { item ->
      AILog.i(TAG, "socketStart-recordId: ${item.recordId}")
      audioSocketManager.connect(firstStart)
    }
  }

  suspend fun updateHistoryData() {
    AILog.d(TAG, "updateHistoryData: ${recordingState.value}")
    if (recordingState.value.isMeeting()) {
      audioSocketManager.updateHistoryData()
    }
  }

  fun replaceCurrentDataListText(searchText: String, replaceText: String): Int {
    if (recordingState.value.isMeeting()) {
      return audioSocketManager.replaceCurrentDataListText(searchText, replaceText)
    }
    return 0
  }

  /**
   * 开始绑定记录的 noteId 相关的信息，
   * 后续可能记录会切换，得确认音频的记录存在那个 noteId 下面
   */
  fun startWith(
    noteId: String,
    pageFlow: StateFlow<String>,
    audioBasePath: String,
    useOnlineMode: Boolean,
    language: LanguageMenuItem.Content,
    onCreated: suspend (CreateRecordResponse) -> Unit = {},
    onMaxTimeFinished: () -> Unit = {},
  ) {
    AILog.i(TAG, "startWith-state: ${recordingState.value}")
    cancelConnectTimeoutCountDown()
    if (recordingState.value.isEnd.not()) {
      throw Exception(TabletStringsUtils.getString(com.aispeech.tablet.core.res.R.string.noterecordprovider_1739179192196_0))
    }

    this.noteId = noteId
    this.pageFlow = pageFlow
    recordingState.tryEmit(RecordState.LOADING)

    // 如果是离线模式，先检查离线资源路径
    if (!useOnlineMode) {
      if (!checkOfflineResourcePath(OFFLINE_MODEL_PATH)) {
        AILog.e(TAG, "离线资源路径检查失败: $OFFLINE_MODEL_PATH")
        recordingState.tryEmit(RecordState.IDLE)
        showShortToast("离线模式启动失败：离线资源文件不存在，请检查模型文件路径")
        return
      }
      AILog.i(TAG, "离线资源路径检查通过: $OFFLINE_MODEL_PATH")
    }

    adapter.startWith(
      noteId = noteId,
      pageFlow = pageFlow,
      audioBasePath = audioBasePath,
      language = language,
      userId = userId,
      useOnlineMode = useOnlineMode,
      offlineModelPath = OFFLINE_MODEL_PATH,
      enableTranslation = !language.target.eqEmpty(),
      onCreated = {
        emitWithUpdateState(RecordState.RECORDING)
        onCreated.invoke(it)
      },
      onMaxTimeFinished = onMaxTimeFinished,
      audioChangeListener = { end, fileLength ->
        // 兼容原来的音频上传逻辑
        val diff = fileLength - lastTriggerUploadFileLength
        if (diff >= UPLOAD_DIFF_SIZE || end) {
          lastTriggerUploadFileLength = fileLength
          AILog.i(TAG, "multiPart split diff=$diff, end=$end, fileLength=$fileLength")
          audioRecordInfo?.recordId?.let { recordId ->
            audioRecordInfo?.objectId?.let { objectId ->
              multiPartAppendAudio(recordId, objectId, end)
            }
          }
        }
      }
    )

//    scope.launch {
//      startPrepare(
//        noteId = noteId,
//        pageFlow = pageFlow,
//        audioBasePath = audioBasePath,
//        onMaxTimeFinished = onMaxTimeFinished
//      )
//      start(
//        language = language,
//        onCreated = onCreated,
//      )
//    }
  }

  /**
   * 开始会议
   */
  private suspend fun start(
    language: LanguageMenuItem.Content,
    onCreated: suspend (CreateRecordResponse) -> Unit = {},
  ) {

    AILog.i(TAG, "start-state: ${recordingState.value}")
    if (recordingState.value.isLoading()) return

    audioRecordInfo = null

    //todo oggopus目前接ali不出字，短时间可能搞不定，先客户端来指定指定几种语言送oggopus
    if (language.source.value == "cn" || language.source.value == "en" || language.source.value == "ce") {
      MeetingConfig.setAsrType(MeetingConfig.AsrType.Type_OGGOPUS)
    } else {
      MeetingConfig.setAsrType(MeetingConfig.AsrType.Type_MP3)
    }

    recordingState.emit(RecordState.LOADING)
    resetRecordState()

    val filePath = audioDataManager.createInputFile()
    AILog.i(TAG, "start-filePath: $filePath, exist: ${FileUtils.isFileExists(filePath)}")

    //encode audio debug
    if (MeetingConfig.isDebugSaveEncodeAudio() && (MeetingConfig.isPcm().not() && MeetingConfig.isMp3().not())) {
      val encodeAudioPath = FileCacheUtils.getAudioCachePath() + "/" + audioDataManager.audioType()
      File(encodeAudioPath).delete()
      encodeOutputStream = FileOutputStream(File(encodeAudioPath), true)
    }

    audioSocketManager.setCacheFileManager(audioDataManager.getCacheFileManager())
    val recordResponse = audioSocketManager.start(language, userId, audioDataManager.audioType(), 1, onCacheBuffer = {
      if (it.isNotEmpty()) {
        //断点续传的缓存数据，拿到外部来处理，是encode还是send
        if (MeetingConfig.isOggOpus()) {
          encoderManager.feed(it)
        } else {
          audioSocketManager.send(it, ignoreUpdateQuality = true)
        }
      }
    })
    Preferences.recordingNoteId = noteId
    val (recordId, objectId) = recordResponse

    audioRecordInfo = dataManager.createRecord(
      noteId = noteId,
      filePath = filePath,
      recordId = recordId,
      objectId = objectId,
      pageInfo = pageFlow,
      language = language.source.value,
      translateLanguage = language.target.value,
    )

    onCreated.invoke(recordResponse)

    scope.launch {
      audioSocketManager.prepareHistoryData()
    }

    VoiceGuardian.startVoiceRecognition()
    socketPrepare()

    lastTriggerUploadFileLength = 0
    audioDataManager.start { end, fileLength ->
      // schedule send to cloud
      val diff = fileLength - lastTriggerUploadFileLength
      if (diff >= UPLOAD_DIFF_SIZE || end) {
        lastTriggerUploadFileLength = fileLength
        AILog.i(TAG, "multiPart split diff=$diff, end=$end, fileLength=$fileLength")
        audioRecordInfo?.recordId?.let { recordId ->
          audioRecordInfo?.objectId?.let { objectId ->
            multiPartAppendAudio(recordId, objectId, end)
          }
        }
      }
    }

//    bindMeetingFlow()

    onLanguageChanged(language)
    emitWithUpdateState(RecordState.RECORDING)

    if (!hasConnected) {
      showShortToast(TabletStringsUtils.getString(com.aispeech.tablet.core.res.R.string.noteeditwidgetainotelist_1739177045462_5))
    }

    socketStart(true)
    if (logged) {
      startConnectTimeoutCountDown()
    }
  }

  /**
   * 暂停
   *
   */
  suspend fun pause(manual: Boolean = true, hallClose: Boolean = false) {
    AILog.i(TAG, "pause-manual: $manual, state: ${recordingState.value} hallClose: $hallClose")
    val state = recordingState.value
    if (!state.isRecording()) return

    emitWithUpdateState(RecordState.PAUSED(manual = manual))
    adapter.pause(manual)

    Preferences.recordPausedTime = System.currentTimeMillis()
    RecordAnalyseUtils.reportOnPause()

    if (hallClose && state.isRecording()) {
      endNotificationFlow.emit(RecordEndNotification.Confirm.CaseClosed)
    }

//    audioDataManager.pause()
//    emitWithUpdateState(RecordState.PAUSED(manual = manual))
//    updateRecordInfo(audioDataManager.getDuration())
//
//    // 只有网络已经连接的情况，才考虑清除掉连接超时
//    if (socketState == WebSocketState.CONNECTED) {
//      cancelConnectTimeoutCountDown()
//    }
//    audioSocketManager.disconnect(false)
//    // 记录暂停的时间
//    Preferences.recordPausedTime = System.currentTimeMillis()
//    RecordAnalyseUtils.reportOnPause()
//
//    if (hallClose && state.isRecording()) {
//      endNotificationFlow.emit(RecordEndNotification.Confirm.CaseClosed)
//    }
  }

  fun isMeeting(): Boolean {
    return recordingState.value.isMeeting()
  }

  /**
   * 返回暂停
   */
  suspend fun backPause() {
    val state = recordingState.value
    AILog.i(TAG, "backPause: $state, end: ${endNotificationFlow.value}")
    if (state.isRecording()) {
      endNotificationFlow.emit(RecordEndNotification.Confirm.ManualBack)
    }
    pause(manual = false)
  }

  /**
   * 继续
   */
  suspend fun resume() {
    AILog.i(TAG, "resume-state: ${recordingState.value}")
    if (recordingState.value.isLoading()) return

    recordingState.emit(RecordState.LOADING)
    adapter.resume()

    emitWithUpdateState(RecordState.RECORDING)
    startConnectTimeoutCountDown()
    RecordAnalyseUtils.reportOnResume()


//    audioRecordInfo?.let {
//      recordingState.emit(RecordState.LOADING)
//      socketPrepare()
//      audioDataManager.resume()
//      if (checkRecordDataState(RecordingState.RECORDING)) {
//        emitWithUpdateState(RecordState.RECORDING)
//        startConnectTimeoutCountDown()
//        socketStart()
//        RecordAnalyseUtils.reportOnResume()
//      } else {
//        showShortToast(TabletStringsUtils.getString(com.aispeech.tablet.core.res.R.string.noterecordprovider_1739179192197_1))
//      }
//    }
  }

  suspend fun updateModelId() {
    val state = recordingState.value
    AILog.i(TAG, "updateModelId-state: $state")
    if (state.isRecording()) {
      audioSocketManager.prepare()
      audioSocketManager.connect()
    }
  }

  /**
   * 结束
   */
  suspend fun stop(
    withBack: Boolean = false,
    focus: Boolean = false,
    timeout: Boolean = false,
    reason: String = "manual",
    force: Boolean = true, //全部停止 包括暂停状态
    finish: () -> Unit // 这个一定得走
  ) {
    AILog.i(TAG, "stop: ")

    recordingState.emit(RecordState.STOPPING)

    adapter.stop()
    finish.invoke()

    Preferences.recordingNoteId = ""
    Preferences.recordPausedTime = 0L

    recordingState.emit(RecordState.STOPPED)
    RecordAnalyseUtils.reportOnStop()

//    val state = recordingState.value
//    AILog.i(
//      TAG,
//      "stop state: ${state}, force: $force, withBack: $withBack, focus: $focus, timeout: $timeout," +
//        " reason: $reason, info: $audioRecordInfo"
//    )
//
//    when {
//      state.isStopped() -> return finish.invoke()
//      force && (state == RecordState.IDLE || state.isStopping()) -> return finish.invoke()
//      state == RecordState.LOADING -> {
//        if (checkRecordDataState(RecordingState.RECORDING)) {
//          stop(withBack, focus, timeout, "loadingStop", force, finish)
//        }
//        return
//      }
//    }
//    Preferences.recordingNoteId = ""
//    Preferences.recordPausedTime = 0L
//    RecordAnalyseUtils.reportOnStop()
//
//    recordingState.emit(RecordState.STOPPING)
//    VoiceGuardian.stopVoiceRecognition()
//
//    if (timeout) {
//      stopWithItem(withBack, focus, finish)
//    } else { //先停数据源再结束
//      audioDataManager.stop {
//        stopWithItem(withBack, focus, finish)
//      }
//    }
  }

  private fun stopWithItem(
    withBack: Boolean = false,
    focus: Boolean = false,
    finish: () -> Unit
  ) {
    AILog.i(TAG, "stopWithItem begin")
    scope.launch {
      AILog.i(TAG, "stopWithItem execute")
      // socket 是否准备好
      val socketReady = logged && hasConnected
      // 是否强制结束
      val socketFocus = focus || socketReady.not()

      val disconnectResult = audioSocketManager.disconnect(socketFocus, manual = true)
      audioSocketManager.release()

      meetingJob?.cancel()
      encoderManager.stop()

      encodeOutputStream?.flush()
      encodeOutputStream?.close()

      val fixDuration = audioDataManager.getDuration().fixMaxDuration()
      AILog.i(
        TAG,
        "withBack: $withBack, disconnectResult = $disconnectResult, fixDuration = $fixDuration, $audioRecordInfo"
      )

      audioRecordInfo?.let {
        updateRecordInfo(fixDuration, end = true)
      }
      audioSocketManager.stopRecording()

      resetRecordingAllState(withBack)
      finish.invoke()
      AILog.i(TAG, "stopWithItem end")
    }
  }

  suspend fun removeLastPoint() {
    AILog.i(TAG, "removeLastPoint-current: $audioRecordInfo")
    audioRecordInfo?.markPoints?.dropLast(1)?.let {
      audioRecordInfo = audioRecordInfo?.copy(markPoints = it)
      dataManager.updateRecordInfo(audioRecordInfo!!)
    }
  }

  /**
   * 标记
   */
  fun markPoint(): RecordingMarkResult {
    AILog.i(TAG, "markPoint-state: ${recordingState.value}")

    if (recordingState.value is RecordState.PAUSED) {
      throw RecordingPauseMarkerException()
    }

    if (recordingState.value.canMark().not()) {
      throw MarkNotRecordingException()
    }

    val endTime = audioDataManager.getDuration().toInt()
    val last = getLastPoint()

    val marked = IntRange(last, endTime)
    updateMarkTime.tryEmit(endTime)
    AILog.i(TAG, "markPoint-last: $last, end: $endTime")

    audioRecordInfo?.markPoints?.toMutableList()?.plus(endTime)?.let {
      audioRecordInfo = audioRecordInfo?.copy(
        markPoints = it
      )
    }

    return RecordingMarkResult(range = marked, data = null)
  }

  fun focusStopAndStart(
    language: LanguageMenuItem.Content,
    audioBasePath: String,
    useOfflineMode: Boolean = false,
    onCreated: (CreateRecordResponse) -> Unit = {},
    onStopped: () -> Unit = {},
    onMaxTimeFinished: () -> Unit = {},
  ) {
    AILog.i(TAG, "focusStopAndStart: $language")
    scope.launch {
      if (USE_HYBRID_SPEECH_ADAPTER) {
        // 如果是离线模式，先检查离线资源路径
        if (useOfflineMode) {
          val offlineModelPath = "/storage/emulated/0/hybrid_speech_debug/offline_model/magnus_lasr_android_aarch64_v0.0.3-sp05-joint-debug-v6"
          if (!checkOfflineResourcePath(offlineModelPath)) {
            AILog.e(TAG, "focusStopAndStart 离线资源路径检查失败: $offlineModelPath")
            showShortToast("离线模式启动失败：离线资源文件不存在，请检查模型文件路径")
            return@launch
          }
          AILog.i(TAG, "focusStopAndStart 离线资源路径检查通过: $offlineModelPath")
        }

        // 使用新的 HybridSpeechAdapter
        adapter.focusStopAndStart(
          language = language,
          audioBasePath = audioBasePath,
          useOfflineMode = useOfflineMode,
          onCreated = { response ->
            emitWithUpdateState(RecordState.RECORDING)
            onCreated.invoke(response)
          },
          onStopped = onStopped,
          onMaxTimeFinished = onMaxTimeFinished
        )
      } else {
        // 原来的逻辑
        stop(withBack = true, focus = false, reason = "focusStopAndStart", finish = onStopped)
        recordingState.takeOrTimeoutEq(state = RecordState.STOPPED, timeoutMillis = 3500)?.let {
          AILog.i(TAG, "focusStopAndStart-waited: $it, language: $language")
          rebindOnMaxTimeFinished(onMaxTimeFinished)

          // 如果是离线模式，先检查离线资源路径
          if (useOfflineMode) {
            val offlineModelPath = "/storage/emulated/0/hybrid_speech_debug/offline_model/magnus_lasr_android_aarch64_v0.0.3-sp05-joint-debug-v6"
            if (!checkOfflineResourcePath(offlineModelPath)) {
              AILog.e(TAG, "focusStopAndStart 原逻辑分支离线资源路径检查失败: $offlineModelPath")
              showShortToast("离线模式启动失败：离线资源文件不存在，请检查模型文件路径")
              return@let
            }
            AILog.i(TAG, "focusStopAndStart 原逻辑分支离线资源路径检查通过: $offlineModelPath")
          }

          adapter.startWith(
            noteId = noteId,
            pageFlow = pageFlow,
            audioBasePath = audioBasePath,
            language = language,
            userId = userId,
            useOnlineMode = !useOfflineMode,
            offlineModelPath = "/storage/emulated/0/hybrid_speech_debug/offline_model/magnus_lasr_android_aarch64_v0.0.3-sp05-joint-debug-v6",
            enableTranslation = !language.target.eqEmpty(),
            onCreated = {
              emitWithUpdateState(RecordState.RECORDING)
              onCreated.invoke(it)
            },
            onMaxTimeFinished = onMaxTimeFinished,
            audioChangeListener = { end, fileLength ->
              // 兼容原来的音频上传逻辑
              val diff = fileLength - lastTriggerUploadFileLength
              if (diff >= UPLOAD_DIFF_SIZE || end) {
                lastTriggerUploadFileLength = fileLength
                AILog.i(TAG, "multiPart split diff=$diff, end=$end, fileLength=$fileLength")
                audioRecordInfo?.recordId?.let { recordId ->
                  audioRecordInfo?.objectId?.let { objectId ->
                    multiPartAppendAudio(recordId, objectId, end)
                  }
                }
              }
            }
          )
        }
      }
    }
  }

  private fun getLastPoint(): Int {
    return audioRecordInfo?.markPoints?.lastOrNull() ?: 0
  }

  /**
   * 开始前准备
   */
  private suspend fun startPrepare(
    noteId: String,
    pageFlow: StateFlow<String>,
    audioBasePath: String,
    onMaxTimeFinished: () -> Unit
  ) {
    AILog.i(TAG, "setParameter: noteId: $noteId, pageFlow: ${pageFlow.value}")
    this.noteId = noteId
    this.pageFlow = pageFlow
    audioDataManager.initWithBasePath(audioBasePath)
    onMeetingMaxFinished = onMaxTimeFinished
    audioSocketManager.updateNoteId(noteId)
  }

  /**
   * 重新绑定超时结束函数
   */
  fun rebindOnMaxTimeFinished(finish: (() -> Unit)? = null) {
    onMeetingMaxFinished = finish

    // 如果使用新的 HybridSpeechAdapter，也需要更新 adapter 中的回调
    if (USE_HYBRID_SPEECH_ADAPTER) {
      adapter.rebindOnMaxTimeFinished(finish)
    }
  }

  /**
   * 监听录音时长
   */
  private suspend fun listenRecordDuration(it: Long) {
    if (it >= RECORD_MAX_DURATION) {
      AILog.i(TAG, "listenRecordDuration: $it, state: ${recordingState.value}")
      if (recordingState.value != RecordState.STOPPING && recordingState.value != RecordState.STOPPED) {
        endNotificationFlow.emit(RecordEndNotification.Alert.MaxDuration)
        audioDataManager.stop {
          scope.launch {
            stop(timeout = true, reason = "listenRecordDuration") {
              onMeetingMaxFinished?.invoke()
              RecordAnalyseUtils.reportOnMaxTime()
            }

            //mock-restart
            if (Preferences.mockLoopRecorder) {//确保执行一次
              AILog.i(TAG, "start mock loop recorder, language : ${selectLanguage.value}")
              while (recordingState.value != RecordState.STOPPED && recordingState.value != RecordState.IDLE) {//确保已经停止
                delay(2000)
              }
              start(selectLanguage.value)
            }
          }

        }
      }
    }
  }

  /**
   * 重制记录状态
   */
  fun resetRecordState() {
    AILog.i(TAG, "resetRecordState")
    scope.launch {
      endNotificationFlow.emit(RecordEndNotification.Normal)
    }
  }

  /**
   * 超过
   */
  private fun connectTimeout() {
    AILog.i(TAG, "connectTimeout")
    audioSocketManager.updateIgnoreBefore()
  }

  /**
   * 开始连接倒计时
   */
  private fun startConnectTimeoutCountDown() {
    cancelConnectTimeoutCountDown()
    AILog.i(TAG, "startConnectTimeoutCountDown: ")
    connectTimeoutCountDownJob = scope.launch {
//      for (i in 1..CONNECT_TIMEOUT) {
//        delay(MINUTE_MILE)
//        if (i == CONNECT_TIMEOUT) {
//          connectTimeout()
//        }
//      }
      delay(CONNECT_TIMEOUT)
      AILog.e(TAG, "connect timeout")
      connectTimeout()
    }
  }

  private fun cancelConnectTimeoutCountDown() {
    connectTimeoutCountDownJob?.let {
      AILog.i(TAG, "cancelConnectTimeoutCountDown")
      connectTimeoutCountDownJob?.cancel()
      connectTimeoutCountDownJob = null
    }
  }

  /**
   * 推送和更新状态
   */
  private suspend fun emitWithUpdateState(state: RecordState) {
    AILog.i(TAG, "emit state $state")
    recordingState.emit(state)
  }

  private var printCount = 0
  private suspend fun updateRecordInfo(duration: Long, end: Boolean = false) {
    audioRecordInfo?.let { info ->
      audioRecordInfo = info.copy(duration = duration.toInt(), recordFinished = end)
      if (printCount++ % 10 == 0) {
        AILog.v(TAG, "updateRecordInfo, duration = $duration")
        printCount = 1
      }
//      AILog.v(TAG, "updateRecordInfo, duration = $duration")
      dataManager.updateRecordInfo(audioRecordInfo!!)
    }
  }

  private suspend fun resetRecordingAllState(withBack: Boolean) {
    AILog.i(TAG, "resetRecordingAllState: withBack: $withBack")
    emitWithUpdateState(if (withBack) RecordState.STOPPED else RecordState.IDLE)
//    recordDuration.emit(0L)
  }

  private suspend fun checkRecordDataState(state: RecordingState): Boolean {
    return audioDataManager.getStateFlow().takeOrTimeoutEq(100L, state) != null
  }

  suspend fun markdownContentUpdate(
    index: Int,
    tagList: List<TranscriptionMarkdownTag>
  ) = audioSocketManager.markdownContentUpdate(
    index, tagList
  )

  suspend fun editSpeaker(
    id: Long,
    speaker: String,
    newSpeaker: String,
    syncAll: Boolean,
  ): Int = audioSocketManager.editSpeaker(
    id = id,
    speaker = speaker,
    newSpeaker = newSpeaker,
    syncAll = syncAll
  )

  suspend fun editSelection(
    selectionRange: IntRange,
    replaceText: String,
    index: Int,
    replaceAll: Boolean,
    item: RuntimeDataMixItem? = null,
    matchText: String? = null
  ): Int {
    return audioSocketManager.editSelection(
      replaceText = replaceText,
      item = item,
      replaceAll = replaceAll,
      matchText = matchText
    )
  }

  suspend fun onAiTextTestClick() {
    audioSocketManager.onAiTextTestClick()
  }

  /**
   * 分片上传音频
   * @param recordId 录音id
   * @param objectId 记录id
   * @param end 录音结束与否
   */
  private fun multiPartAppendAudio(recordId: Long, objectId: String, end: Boolean) {
    val objectKey = NetWorkConfig.getOssTempAudioPath(DeviceUtils.getSN(), objectId)
    val nextPosition = NoteRecordPreference.read(objectId)
    val uploadBuffer = ByteArray(1024 * 1024) //buffer复用
    val size = audioDataManager.randomReadBuffer(nextPosition, uploadBuffer)
    if (size > 0) {
      val uploadBuffer = uploadBuffer.copyOfRange(0, size)
      AILog.i(TAG, "multiPart nextPosition: $nextPosition, readSize: $size, end: $end, $objectId")
      //内部单线程上传
      NoteOssUploadAudioManager.getAppendUploader()
        .appendUpload(objectKey, nextPosition, uploadBuffer, object : IUploaderListener {
          override fun onProgress(currentSize: Long, totalSize: Long) {
            //AILog.i(TAG, "multiPart onProgress: $currentSize, $totalSize")
          }

          override fun onNext(nextPosition: Long) {
            val left = audioDataManager.fileLength() - nextPosition
            AILog.i(TAG, "multiPart onNext: $nextPosition, left: $left, end: $end, $objectId")
            NoteRecordPreference.save(objectId, nextPosition)
            if (left >= UPLOAD_DIFF_SIZE || end) {
              multiPartAppendAudio(recordId, objectId, end)
            }
          }

          override fun onPositionChanged(nextPosition: Long) {
            val left = audioDataManager.fileLength() - nextPosition
            AILog.i(
              TAG,
              "multiPart onPositionChanged: $nextPosition, left: $left, end: $end, $objectId"
            )
            NoteRecordPreference.save(objectId, nextPosition)
            if (left >= UPLOAD_DIFF_SIZE || end) {
              multiPartAppendAudio(recordId, objectId, end)
            }
          }

          override fun onFailure(errCode: String?) {
            AILog.i(TAG, "multiPart onFailure: $errCode, end: $end, $objectId")
          }
        })
    } else {
      AILog.w(TAG, "multiPart read size: $size <= 0, end: $end, $objectId")
      if (end) {
        NoteRecordPreference.delete(objectId)
      }
    }
  }

  companion object {
    private const val TAG = "NoteRecordProvider"
//    const val MINUTE_MILE = 60000L // 1分钟
//    const val CONNECT_TIMEOUT = 5 // 5分钟
    const val CONNECT_TIMEOUT = 30 * 1000L // 30秒
    const val RECORD_MAX_DURATION = 1000 * 60 * 60 * 4 // 4 小时

    private const val UPLOAD_DIFF_SIZE = 250 * 1024 //250k/min

    // 离线模型路径常量
    private const val OFFLINE_MODEL_PATH = "/storage/emulated/0/hybrid_speech_debug/offline_model/magnus_lasr_android_aarch64_v0.0.3-sp05-joint-debug-v6"
  }

  private fun Long.fixMaxDuration(): Long {
    return when {
      this < 0 -> 0
      abs(RECORD_MAX_DURATION - this) <= 1000 -> RECORD_MAX_DURATION.toLong()
      this > RECORD_MAX_DURATION -> RECORD_MAX_DURATION.toLong()
      else -> this
    }
  }

  /**
   * max：用于限定当前实时录音原文的更新范围
   */
  fun updateCurrentDataListText(
    newItems: List<ConversationRow2.ContentItem>,
    max: Int = newItems.maxOfOrNull { it.end } ?: 0
  ) {
    AILog.i(TAG, "updateCurrentDataListText: [max = $max]")
    audioSocketManager.updateCurrentDataListText(newItems, max)
  }

  fun notifyNewDataAvailable() {
    audioSocketManager.notifyNewDataAvailable()
  }

  /**
   * 检查离线资源路径是否有效
   * @param modelPath 离线模型路径
   * @return true 如果路径有效，false 如果路径无效
   */
  private fun checkOfflineResourcePath(modelPath: String): Boolean {
    return try {
      val modelDir = File(modelPath)

      // 检查文件是否存在
      if (!modelDir.exists()) {
        AILog.e(TAG, "离线模型目录不存在: $modelPath")
        return false
      }

      // 检查当前文件夹下面文件不能为空
      val files = modelDir.listFiles()
      if (files == null || files.isEmpty()) {
        AILog.e(TAG, "离线模型目录为空: $modelPath")
        return false
      }

      AILog.i(TAG, "离线资源路径检查通过: $modelPath, 文件数量: ${files.size}")
      true

    } catch (e: Exception) {
      AILog.e(TAG, "检查离线资源路径时发生异常: $modelPath", e)
      false
    }
  }
}