package com.aispeech.tablet.feature.meeting.domain

import android.annotation.SuppressLint
import com.aispeech.aibase.AILog
import com.aispeech.tablet.core.common.NoteFilePath
import com.aispeech.tablet.core.common.Tablet
import com.aispeech.tablet.core.common.UserConfig
import com.aispeech.tablet.core.db.dao.NoteContentDao
import com.aispeech.tablet.core.db.dao.NoteDao
import com.aispeech.tablet.core.db.use
import com.aispeech.tablet.core.meeting.data.repository.NoteMeetingDataRepository
import com.aispeech.tablet.core.model.converters.NoteContentTypeConverters
import com.aispeech.tablet.core.model.entity.AttachmentDownloadStatus
import com.aispeech.tablet.core.model.entity.DownloadStatus
import com.aispeech.tablet.core.model.entity.NoteContentEntity
import com.aispeech.tablet.core.model.entity.NoteContentType
import com.aispeech.tablet.core.model.entity.NoteEntity
import com.aispeech.tablet.core.model.entity.audio.AudioRecordInfo
import com.aispeech.tablet.lib.downloadmanager.DownloadManager
import com.aispeech.tablet.preferences.LOCAL_USER_ID
import com.blankj.utilcode.util.NetworkUtils
import com.squareup.moshi.JsonAdapter
import kotlinx.coroutines.CoroutineExceptionHandler
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.FlowPreview
import kotlinx.coroutines.Job
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.cancel
import kotlinx.coroutines.channels.Channel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.buffer
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.debounce
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.firstOrNull
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import javax.inject.Inject
import javax.inject.Singleton
import kotlin.coroutines.cancellation.CancellationException

@Singleton
class NoteAttachmentHandler @Inject constructor(
  private val downloadManager: DownloadManager,
  private val repository: NoteMeetingDataRepository,
  private val adapter: JsonAdapter<AudioRecordInfo>,
) {

  companion object {
    const val TAG = "NoteAttachmentHandler"
  }

  private val contentDao by lazy {
    NoteContentDao.use()
  }

  private val handlerScope = CoroutineScope(
    SupervisorJob() + Dispatchers.IO + CoroutineExceptionHandler { _, throwable ->
      AILog.e(TAG, "Coroutine error: ${throwable.message}", throwable)
    }
  )

  private var databaseObserverJob: Job? = null
  private var downloadManagerObserverJob: Job? = null

  @SuppressLint("MissingPermission")
  private val _netWorkConnected = MutableStateFlow(NetworkUtils.isConnected())
  private val netWorkConnected: StateFlow<Boolean> = _netWorkConnected.asStateFlow()

  private val netListener = object : NetworkUtils.OnNetworkStatusChangedListener {
    override fun onDisconnected() {
      handlerScope.launch {
        AILog.i(TAG, "Network disconnected, pausing downloads...")
        _netWorkConnected.emit(false)
        downloadManager.handleNetworkLoss()
      }
    }

    override fun onConnected(networkType: NetworkUtils.NetworkType?) {
      handlerScope.launch {
        AILog.i(TAG, "Network connected (${networkType?.name}), resuming downloads...")
        _netWorkConnected.emit(true)
        downloadManager.handleNetworkRestored()
      }
    }
  }

  @SuppressLint("MissingPermission")
  fun initialize() {
    AILog.i(TAG, "Initializing NoteAttachmentHandler...")
    NetworkUtils.registerNetworkStatusChangedListener(netListener)

    handlerScope.launch {
      downloadManager.initialize()
      contentDao.getNotesRequiringAttachmentDownloadSync(
        userId = UserConfig.userId,
        type = NoteContentTypeConverters().noteContentTypeToStr(NoteContentType.Voice)
      ).forEach {
        AILog.d(
          TAG, "initialize-noteOwnerId: ${it.noteOwnerId} status: ${it.attachmentDownloadStatus}"
        )
        contentDao.updateDownloadStatus(it.noteContentId, AttachmentDownloadStatus.NONE)
      }
      listenToLoginStatus()
    }
  }

  fun cleanup() {
    AILog.i(TAG, "Cleaning up NoteAttachmentHandler...")
    NetworkUtils.unregisterNetworkStatusChangedListener(netListener)
    stopObserving("Handler cleanup called")
    handlerScope.cancel()
  }

  suspend fun cancelTaskByNoteId(noteOwnerId: String) {
    val externalIdsToCancel = getTaskIdsToCancelForNote(noteOwnerId)
    externalIdsToCancel.forEach { id ->
      downloadManager.cancelDownloadById(id)
    }
  }

  /**
   * 根据笔记ID将所有需要下载的附件任务添加到下载队列
   */
  suspend fun enqueueTasksForNote(noteOwnerId: String) {
    withContext(Dispatchers.IO) {
      AILog.d(TAG, "Enqueueing tasks for noteOwnerId: $noteOwnerId")

      // 获取笔记实体
      val noteEntity = repository.getNoteEntity(noteOwnerId) ?: run {
        AILog.e(TAG, "Cannot enqueue tasks: NoteEntity not found for noteOwnerId: $noteOwnerId")
        return@withContext
      }
      val noteEntityMap = mapOf(noteOwnerId to noteEntity)

      // 获取需要处理的语音附件内容
      val voiceContentType = NoteContentTypeConverters().noteContentTypeToStr(NoteContentType.Voice)
      val contentsToDownload = contentDao.getDownloadContentsByNoteOwnerIdAndType(
        UserConfig.userId,
        voiceContentType,
        noteOwnerId
      ).filter { it.attachmentDownloadStatus != AttachmentDownloadStatus.COMPLETED }

      if (contentsToDownload.isEmpty()) {
        AILog.i(TAG, "No voice attachments found for noteOwnerId: $noteOwnerId")
        return@withContext
      }

      for (content in contentsToDownload) {
        if (UserConfig.userId == LOCAL_USER_ID) {
          AILog.w(TAG, "User is local user, skipping enqueue for content ${content.noteContentId}")
          break
        }
        val externalId = determineExternalId(content)
        contentDao.updateDownloadStatus(content.noteContentId, AttachmentDownloadStatus.PENDING)
        AILog.d(
          TAG,
          "Marked content ${content.noteContentId} (externalId: $externalId) as PENDING before enqueueing."
        )

        processAndEnqueue(content, noteEntityMap)
      }

      AILog.i(TAG, "Finished enqueueing tasks for noteOwnerId: $noteOwnerId")
    }
  }

  suspend fun getTaskIdsToCancelForNote(noteOwnerId: String): List<Long> {
    return withContext(Dispatchers.IO) {
      val voiceContentType = NoteContentTypeConverters().noteContentTypeToStr(NoteContentType.Voice)

      val relevantExternalIdsFromDb = contentDao.getDownloadContentsByNoteOwnerIdAndType(
        UserConfig.userId,
        voiceContentType,
        noteOwnerId
      ).mapNotNull { content ->
        determineExternalId(content).takeIf { it.isNotBlank() }
      }.toSet()

      if (relevantExternalIdsFromDb.isEmpty()) {
        AILog.i(
          TAG,
          "No relevant externalIds found in DB for noteOwnerId: $noteOwnerId to check for cancellation."
        )
        return@withContext emptyList()
      }
      AILog.d(TAG, "Relevant externalIds from DB for note $noteOwnerId: $relevantExternalIdsFromDb")

      val allCurrentDownloadStates = downloadManager.observeAllTasks().firstOrNull() ?: emptyList()

      if (allCurrentDownloadStates.isEmpty()) {
        AILog.i(TAG, "No tasks currently in DownloadManager to check for cancellation.")
        return@withContext emptyList()
      }

      val idsToCancel = allCurrentDownloadStates
        .filter { downloadState ->
          downloadState.externalId != null && relevantExternalIdsFromDb.contains(
            downloadState.externalId
          ) &&
            (downloadState.status == DownloadStatus.PENDING || downloadState.status == DownloadStatus.DOWNLOADING)
        }
        .map { it.id }
        .distinct()

      AILog.i(
        TAG,
        "Found ${idsToCancel.size} tasks to cancel for noteOwnerId: $noteOwnerId. idsToCancel: $idsToCancel"
      )
      idsToCancel
    }
  }

  private fun listenToLoginStatus() {
    handlerScope.launch {
      UserConfig.userChangeFlow.collect { userId ->
        AILog.i(TAG, "User changed to: $userId")
        if (userId != LOCAL_USER_ID) {
          AILog.i(TAG, "Valid user detected. Starting observers.")
          stopObserving("User changed, restarting observers.")
          observeDownloadManagerUpdates()
          startObservingInternal()
        } else {
          AILog.i(TAG, "No valid user or local user. Stopping observers.")
          stopObserving("User changed to local or invalid.")
          runCatching {
            downloadManager.handleUserLogout()
            AILog.i(TAG, "DownloadManager.handleUserLogout() completed.")
          }.onFailure {
            AILog.e(TAG, "Error during DownloadManager cleanup on logout. $it")
          }
        }
      }
    }
  }

  private fun stopObserving(reason: String) {
    AILog.i(TAG, "Stopping observers due to: $reason")
    databaseObserverJob?.cancel("Stopping DB observation: $reason")
    downloadManagerObserverJob?.cancel("Stopping DM observation: $reason")
    databaseObserverJob = null
    downloadManagerObserverJob = null
  }

  @OptIn(FlowPreview::class)
  fun startObservingInternal() {
    if (databaseObserverJob?.isActive == true) { // 检查 databaseObserverJob
      AILog.d(TAG, "Database observation is already active.")
      return
    }

    AILog.i(TAG, "Starting to observe notes for downloads...")
    databaseObserverJob = handlerScope.launch {
      contentDao.getNotesRequiringAttachmentDownload(
        userId = UserConfig.userId,
        type = NoteContentTypeConverters().noteContentTypeToStr(NoteContentType.Voice)
      )
        .debounce(5000L)
        .distinctUntilChanged()
        .buffer(capacity = Channel.BUFFERED)
        .collect { contentsFromDb ->
          if (contentsFromDb.isEmpty()) return@collect

          val idsToMarkPending = contentsFromDb.map { it.noteContentId }
          try {
            contentDao.updateDownloadStatusBatch(idsToMarkPending, AttachmentDownloadStatus.PENDING)
            AILog.i(
              TAG, "Successfully marked ${idsToMarkPending.size} attachments as PENDING in DB."
            )
          } catch (e: Exception) {
            AILog.e(
              TAG,
              "Failed to batch update status to PENDING for ${idsToMarkPending.size} attachments. Aborting this batch.",
              e
            )
            return@collect
          }

          AILog.i(TAG, "Received batch of ${contentsFromDb.size} items. Processing in chunks...")
          contentsFromDb.chunked(50).forEachIndexed { chunkIndex, chunk ->
            try {
              val chunkNoteIds = chunk.map { it.noteOwnerId }.distinct()
              if (chunkNoteIds.isEmpty()) {
                AILog.i(TAG, "Skipped chunk ${chunkIndex + 1} due to empty note IDs.")
                return@forEachIndexed
              }

              val chunkNoteEntityMap =
                repository.getNotesByIds(chunkNoteIds).associateBy { it.noteId }
              AILog.i(
                TAG,
                "Successfully fetched ${chunkNoteEntityMap.size} note entities for paths in chunk ${chunkIndex + 1}."
              )

              if (UserConfig.userId == LOCAL_USER_ID) {
                AILog.i(TAG, "Skipped processing chunk ${chunkIndex + 1} as user is local.")
                return@forEachIndexed
              }

              for (content in chunk) {
                processAndEnqueue(content, chunkNoteEntityMap)
              }

              AILog.i(TAG, "Finished enqueuing chunk ${chunkIndex + 1}.")
            } catch (e: Exception) {
              AILog.e(
                TAG,
                "Error fetching note entities for paths in chunk ${chunkIndex + 1}, batch skipped. $e"
              )

              // 延迟计算 externalIdsForChunk，仅在异常时使用
              val externalIdsForChunk = chunk.map { determineExternalId(it) }

              AILog.w(
                TAG,
                "Attempting to roll back status to NONE for ${externalIdsForChunk.size} items due to error in chunk ${chunkIndex + 1}."
              )
              try {
                contentDao.updateDownloadStatusBatch(
                  externalIdsForChunk, AttachmentDownloadStatus.NONE
                )
                AILog.i(TAG, "Successfully rolled back status to NONE for chunk ${chunkIndex + 1}.")
              } catch (rollbackEx: Exception) {
                AILog.e(
                  TAG,
                  "FATAL: Failed to roll back status to NONE for chunk ${chunkIndex + 1}! $rollbackEx"
                )
              }

              if (e is CancellationException) throw e
            }

          }
        }
    }

    observeDownloadManagerUpdates()
  }

  private suspend fun processAndEnqueue(
    content: NoteContentEntity,
    noteEntityMap: Map<String, NoteEntity>,
  ) {

    if (Tablet.hasDetailLog) {
      withContext(Dispatchers.IO) {
        NoteDao.use().getNote(content.noteOwnerId, UserConfig.userId)?.let { note ->
          AILog.i(TAG, "processAndEnqueue debug title: ${note.noteName}")
        }
      }
    }

    val audioInfo = content.extraInfo?.let { safeParseJson(it) }
    val cloudUrl = audioInfo?.audioPath
    val externalId = determineExternalId(content) // 使用提取的函数

    if (cloudUrl.isNullOrBlank() || externalId.isBlank()) {
      AILog.w(
        TAG,
        "Skipping content ${content.noteContentId} (externalId $externalId): Missing cloudUrl or externalId."
      )
      runCatching {
        contentDao.updateDownloadStatus(content.noteContentId, AttachmentDownloadStatus.FAILED)
        AILog.i(TAG, "Updated status to FAILED for ${content.noteContentId} due to missing info.")
      }.onFailure {
        AILog.e(
          TAG, "Failed to mark ${content.noteContentId} as FAILED after missing info check: $it"
        )
      }
      return
    }

    val schemeLower = cloudUrl.substringBefore("://").lowercase()
    if (schemeLower != "https" && schemeLower != "http") {
      AILog.w(
        TAG,
        "Skipping content ${content.noteContentId} (externalId $externalId): Unsupported URL scheme '$schemeLower'. URL: $cloudUrl"
      )
      runCatching {
        contentDao.updateDownloadStatus(content.noteContentId, AttachmentDownloadStatus.FAILED)
        AILog.i(TAG, "Updated status to FAILED for ${content.noteContentId} due to missing info.")
      }.onFailure {
        AILog.e(
          TAG, "Failed to mark ${content.noteContentId} as FAILED after missing info check: $it"
        )
      }
      return
    }

    val noteEntity = noteEntityMap[content.noteOwnerId]
    val noteFilePath = noteEntity?.let { NoteFilePath(it) }
    val destinationDirPath = noteFilePath?.audioPath

    if (destinationDirPath == null) {
      AILog.e(
        TAG,
        "Skipping content ${content.noteContentId}: Could not determine destination directory."
      )
      try {
        contentDao.updateDownloadStatus(content.noteContentId, AttachmentDownloadStatus.FAILED)
      } catch (e: Exception) {
        AILog.e(TAG, "Failed to mark ${content.noteContentId} as FAILED after path error: $e")
      }
      return
    }

    val audioFileName = audioInfo.objectId ?: content.noteContentId
    AILog.d(TAG, "Processing content ${content.noteContentId} for audio download. URL: $cloudUrl")

    downloadManager.enqueueDownload(
      url = cloudUrl,
      fileName = audioFileName,
      destinationDir = destinationDirPath, // 传递目录路径
      externalId = externalId
    ).catch { e ->
      AILog.e(TAG, "Error in enqueueDownload flow for $externalId", e)
      try {
        contentDao.updateDownloadStatus(content.noteContentId, AttachmentDownloadStatus.FAILED)
      } catch (e: Exception) {
        AILog.e(TAG, "Failed to mark ${content.noteContentId} as FAILED after enqueue error: $e")
      }
    }.launchIn(handlerScope)
  }

  private fun observeDownloadManagerUpdates() {
    if (downloadManagerObserverJob?.isActive == true) return

    AILog.i(TAG, "Starting DownloadManager updates observation.")
    downloadManagerObserverJob = handlerScope.launch {
      downloadManager.observeAllTasks()
        .collect { allDownloadStates ->

          val nonFinalExternalIdsSet = contentDao.getNonFinalContentIds(
            userId = UserConfig.userId,
            type = NoteContentTypeConverters().noteContentTypeToStr(NoteContentType.Voice)
          ).toSet()

          val relevantStates = allDownloadStates.filter { currentState ->
            currentState.externalId != null && nonFinalExternalIdsSet.contains(
              currentState.externalId
            )
          }

          for (currentState in relevantStates) {
            val externalId = currentState.externalId!!
            val newStatus = currentState.status

            when (newStatus) {
              DownloadStatus.COMPLETED -> {
                val finalPath = currentState.finalPath
                if (finalPath != null) {
                  AILog.i(TAG, "Download COMPLETED for externalId: $externalId. Path: $finalPath")
                  updateDatabaseOnSuccess(externalId, finalPath)
                } else {
                  AILog.e(
                    TAG, "Download COMPLETED for externalId: $externalId, but finalPath is null!"
                  )
                  updateDatabaseOnFailure(externalId, "Completed but path missing")
                }
              }

              DownloadStatus.FAILED -> {
                AILog.e(
                  TAG,
                  "Download FAILED for externalId: $externalId, message: ${currentState.errorMsg}"
                )
                updateDatabaseOnFailure(externalId, currentState.errorMsg ?: "Unknown error")
              }

              else -> {}
            }
          }
        }
    }
  }

  private suspend fun updateDatabaseOnSuccess(
    externalId: String,
    finalPath: String,
  ) {
    withContext(Dispatchers.IO) {
      try {
        val content = contentDao.getContent(externalId)

        if (content == null) {
          AILog.e(
            TAG, "Cannot update success: No NoteContentEntity found for externalId $externalId"
          )
          return@withContext
        }

        val currentExtra = content.extraInfo
        val audioInfo = currentExtra?.let { safeParseJson(it) }

        if (audioInfo == null) {
          AILog.e(
            TAG,
            "Cannot update success: Failed to parse extraInfo for content ${content.noteContentId} (externalId $externalId)"
          )
          contentDao.updateDownloadStatus(content.noteContentId, AttachmentDownloadStatus.FAILED)
          return@withContext
        }

        val updatedAudioInfo = audioInfo.copy(audioPath = finalPath)
        val newExtraInfoJson = adapter.toJson(updatedAudioInfo)

        if (newExtraInfoJson != null) {
          contentDao.updateDownloadSuccess(
            content.noteContentId, newExtraInfoJson, AttachmentDownloadStatus.COMPLETED
          )
          AILog.d(
            TAG,
            "Updated DB for successful download: content ${content.noteContentId} (externalId $externalId), status=COMPLETED, path=$finalPath"
          )
        } else {
          AILog.e(
            TAG,
            "Cannot update success: Failed to serialize updated extraInfo for content ${content.noteContentId}"
          )
          contentDao.updateDownloadStatus(content.noteContentId, AttachmentDownloadStatus.FAILED)
        }
      } catch (e: Exception) {
        AILog.e(TAG, "Error updating database on success for externalId $externalId", e)
      }
    }
  }

  private suspend fun updateDatabaseOnFailure(
    externalId: String,
    reason: String,
  ) {
    withContext(Dispatchers.IO) {
      try {
        val content = contentDao.getContent(externalId)

        if (content == null) {
          AILog.e(
            TAG, "Cannot update failure: No NoteContentEntity found for externalId $externalId"
          )
          return@withContext
        }

        if (content.attachmentDownloadStatus != AttachmentDownloadStatus.FAILED) {
          contentDao.updateDownloadStatus(content.noteContentId, AttachmentDownloadStatus.FAILED)
          AILog.d(
            TAG,
            "Updated DB for failed download: content ${content.noteContentId} (externalId $externalId), status=FAILED. Reason: $reason"
          )
        }
      } catch (e: Exception) {
        AILog.e(TAG, "Error updating database on failure for externalId $externalId", e)
      }
    }
  }

  private suspend fun determineExternalId(content: NoteContentEntity): String {
    return content.extraInfo?.let { safeParseJson(it)?.foreignId } ?: content.noteContentId
  }

  private suspend fun safeParseJson(json: String): AudioRecordInfo? = withContext(Dispatchers.IO) {
    return@withContext try {
      adapter.fromJson(json)
    } catch (e: Exception) {
      null
    }
  }
}