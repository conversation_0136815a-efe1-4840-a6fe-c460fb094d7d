package com.aispeech.tablet.feature.meeting.domain

import com.aispeech.lib_ktx.getOrThrow
import com.aispeech.tablet.core.meeting.data.repository.RecordPreferenceRepository
import com.aispeech.tablet.core.model.entity.api.response.AsrConfigResponse
import com.aispeech.tablet.core.network.entity.toResultat
import com.aispeech.tablet.core.network.service.business.note.NoteService
import javax.inject.Inject

class NoteAsrConfigRepository @Inject constructor(
  private val service: NoteService,
  private val recordPreferenceRepository: RecordPreferenceRepository
) {

  suspend fun asrConfig(forceRefresh: Boolean = false): AsrConfigResponse {
    if (!forceRefresh) {
      recordPreferenceRepository.asrConfigList?.let { cachedList ->
        return AsrConfigResponse(cachedList)
      }
    }

    val response = service.asrConfig().toResultat().getOrThrow()
    recordPreferenceRepository.asrConfigList = response.domainModel
    return response
  }
}