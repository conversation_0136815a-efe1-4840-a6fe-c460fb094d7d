package com.aispeech.tablet.feature.meeting.utils

import com.aispeech.tablet.feature.meeting.data.MatchPosition

object SearchUtils {

  fun findMatchesInText(text: String, query: String): List<MatchPosition> {
    if (query.isEmpty() || text.isEmpty()) return emptyList()
    val matches = mutableListOf<MatchPosition>()
    var index = text.indexOf(query, ignoreCase = true)
    while (index >= 0) {
      matches.add(MatchPosition(index, index + query.length))
      val nextStart = index + query.length.coerceAtLeast(1)
      if (nextStart > text.length) break
      index = text.indexOf(query, nextStart, ignoreCase = true)
    }
    return matches
  }
}