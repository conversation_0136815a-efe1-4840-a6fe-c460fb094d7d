package com.aispeech.tablet.feature.meeting.domain

import androidx.compose.runtime.mutableStateListOf
import com.aispeech.aibase.AILog
import com.aispeech.aimeeting.api.OtMessageTransformInterface
import com.aispeech.aimeeting.api.entities.OtSocketData
import com.aispeech.aimeeting.api.entities.RuntimeDataItem
import com.aispeech.aimeeting.api.extensions.removeEmphasisTags
import com.aispeech.aimeeting.api.extensions.splitData
import com.aispeech.aimeeting.api.meeting.OtMessageTransformManger
import com.aispeech.tablet.core.res.TabletStringsUtils
import javax.inject.Inject

class OtMessageTransformManager @Inject constructor() : OtMessageTransformInterface {

  /**
   * 数据源
   */
  private val _dataList = mutableStateListOf<RuntimeDataItem>()
  val dataList = _dataList

  /**
   * OT 缓存相关
   */
  private val vtDataCache = mutableListOf<OtSocketData.IncrementData.Middle.Middle2>()
  private var lastVarDataCache: OtSocketData.IncrementData? = null

  /**
   * 说话人相关
   * speakerList: exmaple = [{ id: "0-0", name: TabletStringsUtils.getString(com.aispeech.tablet.core.res.R.string.noterecordingdatamanager_1739006393921_0) }, { id: "1-0", name: TabletStringsUtils.getString(com.aispeech.tablet.core.res.R.string.meetingviewmodel_1739176496088_0) }]
   * speakerObj: exmaple = { 0-0: TabletStringsUtils.getString(com.aispeech.tablet.core.res.R.string.noterecordingdatamanager_1739006393921_0), 1-0: TabletStringsUtils.getString(com.aispeech.tablet.core.res.R.string.meetingviewmodel_1739176496088_0) }
   */
  private var speakerList = mutableListOf<Map<String, String>>()
  private val speakerObj = mutableMapOf<String, String>()

  override fun cleanDataCache() {
    _dataList.clear()
    vtDataCache.clear()
    lastVarDataCache = null
    speakerList.clear()
    speakerObj.clear()
  }

  override fun incrementDataMiddle(otData: OtSocketData.IncrementData.Middle) {
    removeLatestMiddle()

    otData.let {
      vtDataCache.addAll(it.vt ?: listOf())
      lastVarDataCache = it
    }

    // vt 数据排序 - 从小到大 - 语气词过滤
    val vtData = vtDataCache.filter { it.vt.isNotEmpty() }.let { it ->
      it.sortedBy { it.begin }.joinToString { it.vt.removeEmphasisTags() }
    }

    // 数据的合并
    val allVal = vtData.plus((lastVarDataCache as OtSocketData.IncrementData.Middle).value ?: "")

    appendData(allVal, otData)
  }

  override fun incrementDataText(otData: OtSocketData.IncrementData.Text) {
    removeLatestMiddle()
    lastVarDataCache = otData
    appendData(otData.text, otData, true)
    cleanVtDataCache(otData)
  }

  override fun initData(it: OtSocketData.InitData) {
    cleanDataCache()
    val initList = it.list.map { it ->
      RuntimeDataItem(
        id = it.id,
        speaker = getSpeakerName(it.speaker),
        ts = it.ts,
        text = it.list.map {
          it.splitData()
        }.flatten()
      )
    }
    _dataList.addAll(initList)
  }


  /**
   * 删除 VT 缓存
   */
  private fun cleanVtDataCache(otData: OtSocketData.IncrementData.Text) {
    AILog.i(OtMessageTransformManger.TAG, "cleanVtDataCache-before: $lastVarDataCache, ot: $otData")
    if (lastVarDataCache != null && lastVarDataCache?.begin != null && otData.end > lastVarDataCache!!.begin!!) {
      lastVarDataCache = null
    }
    val iterator = vtDataCache.listIterator()
    while (iterator.hasNext()) {
      val element = iterator.next()
      if (element.begin < otData.end) {
        iterator.remove()
      }
    }
    AILog.i(OtMessageTransformManger.TAG, "cleanVtDataCache-vtCache: $vtDataCache $lastVarDataCache")
  }


  /**
   * 添加数据
   */
  private fun appendData(
    value: String,
    otData: OtSocketData.IncrementData,
    isText: Boolean = false
  ) {
    val speakerName = getSpeakerName(otData.speaker)
    val needNewLine = _dataList.isNotEmpty() && _dataList.last().id != otData.id

    // 是否需要换行，或者是第一行
    if (needNewLine || _dataList.isEmpty()) {
      val item = if (isText) {
        val textData = otData as OtSocketData.IncrementData.Text
        val items = textData.splitData(value)
        RuntimeDataItem(otData.id, textData.begin, speakerName, emptyList(), items)
      } else {
        RuntimeDataItem(otData.id, 0.0, speakerName, listOf(value), listOf())
      }
      _dataList.add(item)
    } else {
      val last = _dataList.last()
      val newItem = if (isText) {
        val items = (otData as OtSocketData.IncrementData.Text).splitData(value)
        last.copy(text = last.text.plus(items), middle = emptyList())
      } else {
        last.copy(middle = last.middle.plus(value))
      }
      _dataList[_dataList.size - 1] = newItem
    }
  }


  /**
   * 删除最新的中间态
   */
  private fun removeLatestMiddle() {
    AILog.i(
      OtMessageTransformManger.TAG,
      "removeLatestMiddle: ${vtDataCache.isNotEmpty()}, varDataCache: $lastVarDataCache"
    )
    if (lastVarDataCache != null || vtDataCache.isNotEmpty()) {
      if (_dataList.isNotEmpty()) {
        val last = _dataList.last()
        if (last.middle.isNotEmpty()) {
          val newLast = last.copy(middle = last.middle.dropLast(1))
          _dataList[_dataList.size - 1] = newLast
          if (last.middle.isEmpty() && last.text.isEmpty()) {
            _dataList.removeAt(_dataList.size - 1)
          }
        }
      }
    }
  }

  private fun getSpeakerName(speaker: String): String {
    val item = speakerObj[speaker]
    if (item != null) return item

    val filterInvalid = fun(ele: Map<String, String>): Boolean {
      val validIds = listOf("0-0", "1-0")
      if (validIds.contains(speaker)) {
        return validIds.contains(ele["id"])
      }
      return !validIds.contains(ele["id"])
    }

    var speakerName = TabletStringsUtils.getString(com.aispeech.tablet.core.res.R.string.noteaudioaidatamanager_1739156714051_0)+"${speakerList.filter(filterInvalid).size + 1}"
    if (speaker == "0-1" && speakerObj["0-0"] != null) speakerName = this.speakerObj["0-0"]!!
    if (speaker == "1-1" && speakerObj["1-0"] != null) speakerName = this.speakerObj["1-0"]!!

    speakerObj[speaker] = speakerName
    speakerList =
      speakerObj.keys.map { mapOf("id" to it, "speakerName" to speakerName) }.toMutableList()

    return speakerName
  }

}