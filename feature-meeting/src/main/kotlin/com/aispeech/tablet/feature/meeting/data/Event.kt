package com.aispeech.tablet.feature.meeting.data


/**
 * 用于处理一次性事件的包装器，例如导航或显示 Snackbar/Toast。
 *
 * [StateFlow] 或 [LiveData] 会在观察者重新订阅时（例如屏幕旋转后）重新发送最后一个状态。
 * 这个包装器可以确保事件内容只被消费一次。
 *
 * @param T 事件内容的类型。
 */
open class Event<out T>(private val content: T) {

  private var hasBeenHandled = false
    private set // 只允许在类内部修改

  /**
   * 返回内容，并将其标记为已处理。
   * 如果内容已经被处理过，则返回 null。
   *
   * @return 未被处理过的内容，否则为 null。
   */
  fun getContentIfNotHandled(): T? {
    return if (hasBeenHandled) {
      null
    } else {
      hasBeenHandled = true
      content
    }
  }

  /**
   * 返回内容，即使它已经被处理过。
   * 主要用于预览或调试。
   *
   * @return 事件的内容。
   */
  fun peekContent(): T = content
}
