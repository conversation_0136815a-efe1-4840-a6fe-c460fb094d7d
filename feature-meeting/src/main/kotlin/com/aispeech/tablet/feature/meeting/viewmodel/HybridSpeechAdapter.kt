package com.aispeech.tablet.feature.meeting.viewmodel

import android.util.Log
import com.aispeech.aimeeting.api.MeetingConfig
import com.aispeech.aimeeting.data.repository.RecordRepository
import com.aispeech.hybridspeech.HybridSpeechClient
import com.aispeech.hybridspeech.IHybridSpeechConfigProvider
import com.aispeech.hybridspeech.INetworkConfigCallback
import com.aispeech.hybridspeech.ITranscriptionCallback
import com.aispeech.hybridspeech.NetworkConfigRequest
import com.aispeech.hybridspeech.RecordingConfig
import com.aispeech.hybridspeech.TranscriptionResult
import com.aispeech.hybridspeech.signing.HybridSpeechSignManager
import com.aispeech.lib_ktx.getOrThrow
import com.aispeech.tablet.core.common.FileCacheUtils
import com.aispeech.tablet.core.meeting.data.repository.NoteTranscriptionRepository
import com.aispeech.tablet.core.meeting.domain.NoteRecordingDataManager
import com.aispeech.tablet.core.meeting.entity.ConversationRow2
import com.aispeech.tablet.core.meeting.entity.RuntimeDataItemV2
import com.aispeech.tablet.core.model.entity.audio.AudioRecordInfo
import com.aispeech.tablet.core.model.entity.audio.CreateRecordResponse
import com.aispeech.tablet.core.model.entity.audio.addMarkPoint
import com.aispeech.tablet.core.model.entity.audio.updateDurationAndFinish
import com.aispeech.tablet.core.network.config.NetWorkConfig
import com.aispeech.tablet.core.network.domain.config.ApiOptionsProviderUtils
import com.aispeech.tablet.core.network.service.interceptor.HeaderUtils
import com.aispeech.tablet.feature.meeting.data.Event
import com.aispeech.tablet.feature.meeting.data.RecordStartParams
import com.aispeech.tablet.feature.meeting.di.MeetingScope
import com.aispeech.tablet.feature.meeting.domain.HybridRealtimeTransformManager
import com.aispeech.tablet.feature.meeting.domain.HybridRecordAgentDataManager
import com.aispeech.tablet.feature.meeting.model.LanguageMenuItem
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.isActive
import kotlinx.coroutines.launch
import java.io.File
import java.util.UUID
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 混合语音识别适配器
 *
 */
@Singleton
class HybridSpeechAdapter @Inject constructor(
  private val dataManager: NoteRecordingDataManager,
  private val agentDataManager: HybridRecordAgentDataManager,
  private val transcriptionRepository: NoteTranscriptionRepository,
  private val realtimeTransformManager: HybridRealtimeTransformManager,
  private val recordRepository: RecordRepository,
  @MeetingScope private val meetingScope: CoroutineScope
) {

  companion object {
    private const val TAG = "HybridSpeechAdapter"
    private const val UPLOAD_DIFF_SIZE = 250 * 1024L // 250KB，与原来的逻辑保持一致
    private const val RECORD_MAX_DURATION = 1000 * 60 * 60 * 4L // 4 小时，与原来的逻辑保持一致
  }

  enum class RecordingState { IDLE, LOADING, RECORDING, PAUSED, STOPPING, STOPPED }
  data class AdapterUiState(
    val recordingState: RecordingState = RecordingState.IDLE,
    val durationMs: Long = 0L,
    val serviceConnected: Boolean = false,
    val error: Event<String>? = null, // 使用 Event 包装器处理一次性事件
    val createdRecordResponse: Event<CreateRecordResponse>? = null // 用于 onCreated 回调
  )

  // 服务客户端 - 使用新的 HybridSpeechClient 替代 HybridSpeechServiceManager
  private val serviceClient = HybridSpeechClient(
    context = dataManager.context, // 从 dataManager 获取 context
    servicePackage = "com.aispeech.hybridspeech",
    serviceClass = "com.aispeech.hybridspeech.HybridSpeechService"
  )

  // 签名管理器
  private val signManager = HybridSpeechSignManager.getInstance()

  // 单一的数据源
  private val _uiState = MutableStateFlow(AdapterUiState())
  val uiState: StateFlow<AdapterUiState> = _uiState.asStateFlow()

  private var currentParams: RecordStartParams? = null
  private var currentAudioRecordInfo: AudioRecordInfo? = null
  private var recordingProgressJob: Job? = null
  private var periodicSaveJob: Job? = null

  // 音频上传相关
  private var audioUploadMonitorJob: Job? = null
  private var lastTriggerUploadFileLength = 0L
  private var audioChangeListener: ((Boolean, Long) -> Unit)? = null

  // 超时监听相关
  private var durationMonitorJob: Job? = null
  private var onMaxTimeFinishedCallback: (() -> Unit)? = null

  // 配置提供者实现
  private val configProvider = object : IHybridSpeechConfigProvider.Stub() {
    override fun requestNetworkConfig(
      request: NetworkConfigRequest,
      callback: INetworkConfigCallback?
    ) {
      Log.d(TAG, "配置提供者收到请求: recordId=${request.recordId}, callback=$callback")

      // 检查 callback 是否为 null（根据 AIDL 接口定义，callback 可以为空）
      if (callback == null) {
        Log.e(TAG, "配置提供者收到 null callback，无法返回配置")
        return
      }

      meetingScope.launch {
        try {
          val voiceResult = recordRepository.recordVoiceResult(
            recordId = request.recordId,
            userId = request.userId.toLongOrNull() ?: 0L,
          ).getOrThrow()

          val resumeOffset = voiceResult.sendAllSuccessDuration ?: 0L
          val mp3ChunkCount = voiceResult.sendAllSuccessCount ?: 0

          Log.d(TAG, "配置提供者: recordId=${request.recordId}, 服务端时长=${resumeOffset}ms, MP3 chunk数=${mp3ChunkCount}")

          // 根据音频类型决定续传方式
          val resumeFromMp3ChunkIndex = if (request.audioType.lowercase().contains("mp3") && mp3ChunkCount > 0) {
            mp3ChunkCount
          } else {
            null
          }

          // 构建最终请求 - 始终启用续传能力，根据音频类型设置对应的续传参数
          val finalRequest = request.copy(
            resumeFromOffset = resumeOffset, // 使用服务端返回的准确时长
            resumeFromMp3ChunkIndex = resumeFromMp3ChunkIndex, // MP3模式下使用chunk索引
            sessionId = request.sessionId,
            isResume = true // 始终生成续传配置，支持录音过程中的断线重连
          )

          Log.d(TAG, "最终请求: isResume=${finalRequest.isResume}, resumeFromOffset=${finalRequest.resumeFromOffset}ms, resumeFromMp3ChunkIndex=${finalRequest.resumeFromMp3ChunkIndex}")

          // 使用统一配置生成器
          val networkConfig = signManager.generateNetworkConfig(
            request = finalRequest,
            baseUrl = NetWorkConfig.getBaseUrl(),
            customOptions = HeaderUtils.makeCommonHeader() + ApiOptionsProviderUtils.getOptions().mapValues {
              it.value.toString()
            }
          )

          Log.d(TAG, "网络配置生成成功, 是否包含续传配置: ${networkConfig.asrResumeConfig != null}")
          networkConfig.asrResumeConfig?.let { resumeConfig ->
            Log.d(TAG, "续传配置详情: sessionId=${resumeConfig.sessionId}, resumeFromOffset=${resumeConfig.resumeFromOffset}ms, resumeFromMp3ChunkIndex=${resumeConfig.resumeFromMp3ChunkIndex}")
          }

          callback.onConfigReady(networkConfig)

        } catch (e: Exception) {
          Log.e(TAG, "网络配置生成失败: ${e.message}", e)
          callback.onConfigError(
            3001,
            "网络配置生成失败: ${e.message}"
          )
        }
      }
    }
  }

  // 数据源
  fun pagingItemFlowV2(cacheScope: CoroutineScope, targetKey: Int? = null) =
    transcriptionRepository.getTranscriptionPairStreamV2(cacheScope = cacheScope, targetKey = targetKey)

  // 转写结果回调
  private val transcriptionCallback = object : ITranscriptionCallback.Stub() {
    override fun onTranscriptionResult(result: TranscriptionResult?) {
      result?.let {
        handleTranscriptionResult(it)
      }
    }

    override fun onError(errorMessage: String?) {
      handleTranscriptionError(errorMessage ?: "未知错误")
    }

    override fun onStatusChanged(status: Int) {
      handleStatusChanged(status)
    }
  }

  init {
    // 初始化签名管理器
    initializeSignManager()

    // 监听服务连接状态变化
    meetingScope.launch {
      serviceClient.connectionState.collect { isConnected ->
        _uiState.update { it.copy(serviceConnected = isConnected) }
        if (!isConnected && _uiState.value.recordingState != RecordingState.IDLE) {
          Log.e(TAG, "Service disconnected unexpectedly during recording.")
          recordingProgressJob?.cancel()
          cleanupRecordingState()
          _uiState.update {
            it.copy(
              recordingState = RecordingState.IDLE,
              error = Event("服务连接意外断开")
            )
          }
        }
      }
    }
    Log.d(TAG, "HybridSpeechAdapter 初始化完成")
  }

  /**
   * 初始化签名管理器
   */
  private fun initializeSignManager() {
    signManager.initialize(
      apiKey = "84fb45aa-4400-422a-b1c8-9c91f6b665af",
      apiSecret = "fde4f4e2-0fff-4de8-bcad-4aa463dadf39",
      signType = "HMAC-MD5",
      environment = "debug"
    )
    Log.d(TAG, "签名管理器初始化完成")
  }



  /**
   * 启动定期更新的操作
   * 只有录音状态时才进行自动保存，暂停和结束时不保存
   */
  private fun launchPeriodicSave() {
    periodicSaveJob?.cancel() // 先取消旧的
    periodicSaveJob = meetingScope.launch(Dispatchers.IO) {
      while (isActive) { // 只要协程是活跃的
        delay(30_000) // 每30秒执行一次

        // 只有在录音状态时才进行自动保存
        val currentState = _uiState.value.recordingState
        if (currentState == RecordingState.RECORDING) {
          // 获取当前最新的时长
          val currentDuration = _uiState.value.durationMs
          currentAudioRecordInfo?.let {
            Log.d(TAG, "Periodically saving duration to DB: ${currentDuration}ms")
            val updatedInfo = it.updateDurationAndFinish(currentDuration.toInt())
            updatedInfo?.let {
              dataManager.updateRecordInfo(it)
            }
            currentAudioRecordInfo = updatedInfo
          }
        } else {
          Log.d(TAG, "Skipping periodic save, current state: $currentState")
        }
      }
    }
  }

  /**
   * 添加标记点
   */
  fun markPoint(): Boolean {
    if (_uiState.value.recordingState != RecordingState.RECORDING) {
      Log.w(TAG, "Cannot mark point, not in RECORDING state.")
      return false
    }
    return currentAudioRecordInfo?.let { audioInfo ->
      val currentTime = _uiState.value.durationMs.toInt()
      val updatedInfo = audioInfo.addMarkPoint(currentTime)
      meetingScope.launch {
        updatedInfo?.let { newInfo ->
          dataManager.updateRecordInfo(newInfo)
        }
      }
      currentAudioRecordInfo = updatedInfo
      Log.d(TAG, "Mark point added at ${currentTime}ms.")
      true
    } ?: false
  }


  /**
   * 移除最后一个标记点
   * 模拟 NoteRecordProvider.removeLastPoint 方法
   */
  fun removeLastPoint(): Boolean {
    return false
//    return currentAudioRecordInfo?.let { audioInfo ->
//      try {
//        if (audioInfo.markPoints.isEmpty()) {
//          Log.w(TAG, "没有标记点可以移除")
//          return false
//        }
//
//        val updatedInfo = audioInfo.removeLastMarkPoint()
//        dataManager.updateRecordInfo(updatedInfo)
//        currentAudioRecordInfo = updatedInfo
//
//        Log.d(TAG, "最后标记点移除成功，剩余标记点=${updatedInfo.markPoints.size}")
//        true
//      } catch (e: Exception) {
//        Log.e(TAG, "移除标记点失败", e)
//        false
//      }
//    } ?: false
  }

  /**
   * 完成录音
   * 标记录音结束并更新最终状态
   */
  private fun finishRecording(finalDurationMs: Long) {
    currentAudioRecordInfo?.let { audioInfo ->
      val completedInfo = audioInfo.updateDurationAndFinish(finalDurationMs.toInt(), true)
      currentAudioRecordInfo = completedInfo
      meetingScope.launch {
        dataManager.updateRecordInfo(completedInfo!!)
      }
      Log.d(TAG, "Recording finished. Final duration ${finalDurationMs}ms saved to DB.")
    }
  }

  /**
   * 清理录音的状态
   */
  private fun cleanupRecordingState() {
    Log.d(TAG, "Cleaning up recording state...")
    periodicSaveJob?.cancel() // 取消定期保存任务
    periodicSaveJob = null
    audioUploadMonitorJob?.cancel() // 取消音频上传监控任务
    audioUploadMonitorJob = null
    durationMonitorJob?.cancel() // 取消录音时长监控任务
    durationMonitorJob = null
    currentParams = null
    currentAudioRecordInfo = null
    audioChangeListener = null
    onMaxTimeFinishedCallback = null
    lastTriggerUploadFileLength = 0L
    meetingScope.launch {
      realtimeTransformManager.saveAndCleanCurrentLine()
      agentDataManager.cleanData()
    }
  }


  private suspend fun handleProgressState(progressState: HybridSpeechClient.RecordingProgressState) {
    when (progressState) {
      is HybridSpeechClient.RecordingProgressState.Started -> {
        _uiState.update { it.copy(recordingState = RecordingState.RECORDING, durationMs = 0L) }
        launchPeriodicSave()
      }
      is HybridSpeechClient.RecordingProgressState.Progress -> {
        _uiState.update { it.copy(durationMs = progressState.durationMs) }
      }
      is HybridSpeechClient.RecordingProgressState.Stopped -> {
        _uiState.update { it.copy(recordingState = RecordingState.STOPPED, durationMs = progressState.totalDurationMs) }
        finishRecording(progressState.totalDurationMs)

        // 录音结束时，触发最终的音频上传回调
        audioChangeListener?.let { listener ->
          meetingScope.launch {
            // 获取最终文件长度并触发回调
            currentAudioRecordInfo?.filePath?.let { filePath ->
              val file = File(filePath)
              val finalFileLength = if (file.exists()) file.length() else 0L
              Log.d(TAG, "录音结束: 触发最终音频上传回调, finalFileLength=$finalFileLength")
              listener.invoke(true, finalFileLength)
            }
          }
        }

        delay(500)
        _uiState.update { it.copy(recordingState = RecordingState.IDLE) }
        cleanupRecordingState()
      }
      is HybridSpeechClient.RecordingProgressState.Error -> {
        _uiState.update { it.copy(recordingState = RecordingState.IDLE, error = Event(progressState.message)) }
        cleanupRecordingState()
      }
    }
  }

  /**
   * 开始录音和转写
   * 完全对接 NoteRecordProvider.startWith 方法的参数和逻辑
   */
  fun startWith(
    noteId: String,
    pageFlow: StateFlow<String>,
    audioBasePath: String,
    language: LanguageMenuItem.Content,
    userId: Long,
    useOnlineMode: Boolean = true,
    offlineModelPath: String? = null,
    enableTranslation: Boolean = false,
    onCreated: suspend (CreateRecordResponse) -> Unit = {},
    onMaxTimeFinished: () -> Unit = {},
    audioChangeListener: ((Boolean, Long) -> Unit)? = null // 新增：兼容原来的音频上传回调
  ) {
    Log.d(TAG, "开始录音: noteId=$noteId, language=${language.source.value}->${language.target.value}, useOnlineMode=$useOnlineMode")

    if (_uiState.value.recordingState != RecordingState.IDLE) {
      Log.w(TAG, "Current state is not IDLE, ignoring start request: ${_uiState.value.recordingState}")
      return
    }

    // 保存音频变化监听器和超时回调
    this.audioChangeListener = audioChangeListener
    this.onMaxTimeFinishedCallback = onMaxTimeFinished
    lastTriggerUploadFileLength = 0L

    _uiState.update { it.copy(recordingState = RecordingState.LOADING) }

    meetingScope.launch {
      // 在开始录音时连接服务并注册回调
      if (!serviceClient.connectionState.value) {
        Log.d(TAG, "Service not connected, connecting now...")
        try {
          val connected = serviceClient.connectAndWait()
          if (!connected) {
            Log.e(TAG, "Failed to connect to service")
            _uiState.update { it.copy(recordingState = RecordingState.IDLE, error = Event("服务连接失败")) }
            return@launch
          }
        } catch (e: Exception) {
          Log.e(TAG, "Error connecting to service", e)
          _uiState.update { it.copy(recordingState = RecordingState.IDLE, error = Event("服务连接异常: ${e.message}")) }
          return@launch
        }
      }

      // 连接成功后注册配置提供者和转写回调
      serviceClient.registerConfigProvider(configProvider)
      serviceClient.registerTranscriptionCallback(transcriptionCallback)
      try {
          // 设置笔记 id
          transcriptionRepository.setNoteId(noteId)
          // 更新时间
          realtimeTransformManager.updateTimeWeight(transcriptionRepository.currentTimeWeight)

          // 直接创建核心录音配置，简化转换过程
          val recordId = System.currentTimeMillis()
          val objectId = "obj_$recordId"
          val asrType = if (language.source.value == "cn" || language.source.value == "en" || language.source.value == "ce") {
            MeetingConfig.AsrType.Type_OGGOPUS
          } else {
            MeetingConfig.AsrType.Type_MP3
          }

          // 映射音频类型
          val audioType = when (asrType) {
            MeetingConfig.AsrType.Type_OGGOPUS -> "ogg_opus"
            MeetingConfig.AsrType.Type_MP3 -> "mp3"
            MeetingConfig.AsrType.Type_PCM -> "pcm"
          }

          // 生成文件路径
          val mp3FilePath = "$audioBasePath${File.separator}${UUID.randomUUID()}.mp3"
          val pcmFilePath = FileCacheUtils.getAudioCachePath() + "temp.wav"

          Log.d(TAG, "录音配置: recordId=$recordId, audioType: $audioType")

          // 保存简化的当前配置
          currentParams = RecordStartParams(
            noteId = noteId,
            pageFlow = pageFlow,
            audioBasePath = audioBasePath,
            language = language,
            userId = userId,
            useOnlineMode = useOnlineMode,
            enableTranslation = enableTranslation,
            onMaxTimeFinished = onMaxTimeFinished
          )

          // 创建录音信息
          val response = CreateRecordResponse(recordId = recordId, objectId = objectId)
          val audioRecordInfo = dataManager.createRecord(
            noteId = noteId,
            filePath = mp3FilePath,
            recordId = recordId,
            objectId = objectId,
            pageInfo = pageFlow,
            language = language.source.value,
            translateLanguage = language.target.value
          )
          currentAudioRecordInfo = audioRecordInfo

          onCreated.invoke(response)
          _uiState.update { it.copy(createdRecordResponse = Event(response)) }

          // 订阅进度
          recordingProgressJob?.cancel()
          recordingProgressJob = launch {
            serviceClient.createProgressFlow()
              .catch { e ->
                Log.e(TAG, "Progress Flow caught an exception", e)
                _uiState.update { it.copy(recordingState = RecordingState.IDLE, error = Event(e.message ?: "未知进度错误")) }
                cleanupRecordingState()
              }
              .collect { progressState ->
                handleProgressState(progressState)
              }
          }

          // 创建录音配置
          val config = if (useOnlineMode) {
            // 在线模式：创建支持ASR续传的配置
            RecordingConfig.createOnlineRecordingConfigWithResumeSupport(
              recordId = recordId,
              userId = userId.toString(),
              pcmFilePath = pcmFilePath,
              mp3FilePath = mp3FilePath,
              serverUrl = "", // 将由配置提供者填充
              apiKey = "", // 将由配置提供者填充
              language = language.source.value,
              audioType = audioType,
              translate = language.getTarget()
            )
          } else {
            // 离线模式：创建离线配置
            RecordingConfig.createOfflineRecordingConfig(
              recordId = recordId,
              userId = userId.toString(),
              pcmFilePath = pcmFilePath,
              mp3FilePath = mp3FilePath,
              language = language.source.value,
              translate = language.getTarget(),
              enableTranslation = enableTranslation,
              offlineModelPath = offlineModelPath ?: ""
            )
          }

          Log.d(TAG, "开始录音: useOnlineMode=$useOnlineMode, audioType=$audioType")
          serviceClient.startRecordingAsync(config)

          // 启动音频文件监控（兼容原来的上传逻辑）
          startAudioFileMonitoring(mp3FilePath)

          // 启动录音时长监控（兼容原来的超时逻辑）
          startDurationMonitoring()

        } catch (e: Exception) {
          Log.e(TAG, "启动录音过程中发生异常", e)
          recordingProgressJob?.cancel()
          cleanupRecordingState()
          _uiState.update { it.copy(recordingState = RecordingState.IDLE, error = Event("启动失败: ${e.message}")) }
        }
      }
    }
  }

  /**
   * 停止录音
   */
  suspend fun stop() {
    val currentState = _uiState.value.recordingState
    if (currentState != RecordingState.RECORDING && currentState != RecordingState.PAUSED) {
      Log.w(TAG, "Not in a stoppable state: $currentState")
      return
    }

    if (!_uiState.value.serviceConnected) {
      Log.w(TAG, "Service not connected, performing local cleanup.")
      recordingProgressJob?.cancel()
      cleanupRecordingState()
      _uiState.update { it.copy(recordingState = RecordingState.IDLE, error = Event("服务未连接，强制停止")) }
      return
    }

    _uiState.update { it.copy(recordingState = RecordingState.STOPPING) }

    try {
      serviceClient.stopRecordingWithResultAsync()
      realtimeTransformManager.saveAndCleanCurrentLine()
    } catch (e: Exception) {
      Log.e(TAG, "Failed to call stopRecording, cleaning up.", e)
      recordingProgressJob?.cancel()
      cleanupRecordingState()
      _uiState.update { it.copy(recordingState = RecordingState.IDLE, error = Event("停止失败: ${e.message}")) }
    }
  }

  /**
   * 更新最终录音信息
   * 模拟 NoteRecordProvider.updateRecordInfo 的最终调用
   */
  private suspend fun updateFinalRecordingInfo() {
//    currentAudioRecordInfo?.let { audioInfo ->
//      try {
//        val finalDuration = System.currentTimeMillis() - recordStartTime
//        val finalInfo = audioInfo
//          .updateDuration(finalDuration.toInt())
//          .markFinished()
//
//        dataManager.updateRecordInfo(finalInfo)
//        currentAudioRecordInfo = finalInfo
//
//        Log.d(TAG, "最终录音信息: ${finalInfo.getSummary()}")
//        Log.d(TAG, "录音时长: ${finalDuration}ms, 标记点数: ${finalInfo.markPoints.size}")
//
//      } catch (e: Exception) {
//        Log.e(TAG, "更新最终录音信息失败", e)
//      }
//    }
  }


  // ========== 暂停和恢复录音 ==========

  /**
   * 暂停录音
   * 参考 NoteRecordProvider.pause 方法
   */
  suspend fun pause(manual: Boolean = true) {
    val currentState = _uiState.value.recordingState
    if (currentState != RecordingState.RECORDING) {
      Log.w(TAG, "Cannot pause, state is not RECORDING: $currentState")
      return
    }

    try {
      serviceClient.pauseRecordingAsync()
      _uiState.update { it.copy(recordingState = RecordingState.PAUSED) }
      Log.d(TAG, "Recording paused.")
    } catch (e: Exception) {
      Log.e(TAG, "Failed to pause recording", e)
      _uiState.update { it.copy(error = Event("暂停失败: ${e.message}")) }
    }
  }

  /**
   * 恢复录音
   * 参考 NoteRecordProvider.resume 方法
   */
  suspend fun resume() {
    val currentState = _uiState.value.recordingState
    if (currentState != RecordingState.PAUSED) {
      Log.w(TAG, "Cannot resume, state is not PAUSED: $currentState")
      return
    }

    try {
      serviceClient.resumeRecordingAsync()
    } catch (e: Exception) {
      Log.e(TAG, "恢复录音时发生异常", e)
      _uiState.update { it.copy(error = Event("恢复失败: ${e.message}")) }
    }
  }

  /**
   * 重新绑定超时结束函数
   * 用于处理 activity 重新创建时更新回调的情况
   * 参考 NoteRecordProvider.rebindOnMaxTimeFinished 方法
   */
  fun rebindOnMaxTimeFinished(onMaxTimeFinished: (() -> Unit)? = null) {
    Log.d(TAG, "重新绑定超时回调: ${onMaxTimeFinished != null}")
    this.onMaxTimeFinishedCallback = onMaxTimeFinished
  }

  /**
   * 强制停止并重新开始录音
   * 完全对接 NoteRecordProvider.focusStopAndStart 方法的逻辑
   */
  fun focusStopAndStart(
    language: LanguageMenuItem.Content,
    audioBasePath: String,
    useOfflineMode: Boolean = false,
    onCreated: (CreateRecordResponse) -> Unit = {},
    onStopped: () -> Unit = {},
    onMaxTimeFinished: () -> Unit = {}
  ) {
    Log.d(TAG, "强制停止并重新开始: language=${language.source.value}->${language.target.value}, useOfflineMode=$useOfflineMode")

    meetingScope.launch {
      try {
        // 在停止之前先保存当前参数，避免在 cleanupRecordingState 中被清空
        val savedParams = currentParams
        if (savedParams == null) {
          Log.e(TAG, "无法重新开始录音：当前没有录音参数")
          _uiState.update { it.copy(error = Event("重新开始失败：当前没有录音参数")) }
          return@launch
        }

        // 先停止当前录音
        stop()
        onStopped.invoke()

        // 等待停止完成，最多等待3.5秒
        var waitCount = 0
        while (_uiState.value.recordingState != RecordingState.IDLE && waitCount < 35) {
          delay(100)
          waitCount++
        }

        if (_uiState.value.recordingState != RecordingState.IDLE) {
          Log.w(TAG, "等待停止超时，当前状态: ${_uiState.value.recordingState}")
        }

        Log.d(TAG, "停止完成，开始重新录音: language=${language.source.value}")

        // 使用保存的参数重新开始录音

        // 重新开始录音，使用新的语言设置和保存的参数
        startWith(
          noteId = savedParams.noteId,
          pageFlow = savedParams.pageFlow,
          audioBasePath = audioBasePath,
          language = language,
          userId = savedParams.userId,
          useOnlineMode = !useOfflineMode,
          offlineModelPath = "/storage/emulated/0/hybrid_speech_debug/offline_model/magnus_lasr_android_aarch64_v0.0.3-sp05-joint-debug-v6",
          enableTranslation = !language.target.eqEmpty(),
          onCreated = { response ->
            meetingScope.launch {
              onCreated.invoke(response)
            }
          },
          onMaxTimeFinished = onMaxTimeFinished,
          audioChangeListener = audioChangeListener // 保持原有的音频上传监听器
        )

      } catch (e: Exception) {
        Log.e(TAG, "强制停止并重新开始过程中发生异常", e)
        _uiState.update { it.copy(error = Event("重新开始失败: ${e.message}")) }
      }
    }
  }

  // ========== 连接管理 ==========

  /**
   * 连接服务（已废弃，现在在开始录音时自动连接）
   */
  @Deprecated("Service connection is now handled automatically when starting recording")
  fun connect(): Boolean {
    Log.d(TAG, "连接 HybridSpeechService")
    return serviceClient.connect()
  }

  /**
   * 断开服务连接
   */
  fun disconnect() {
    meetingScope.launch {
      if (_uiState.value.recordingState != RecordingState.IDLE &&
        _uiState.value.recordingState != RecordingState.STOPPED) {
        stop()
      }
      serviceClient.cleanup()
      cleanupRecordingState()
    }
  }


  // ========== 状态查询和数据访问 ==========

  /**
   * 获取录音时长
   */
  fun getRecordingDuration(): Long {
    val currentState = _uiState.value.recordingState
    if (currentState != RecordingState.RECORDING) {
      Log.d(TAG, "Recording duration not in meetting")
      return 0L
    }
    return serviceClient.getCurrentDuration() ?: 0L
  }

  /**
   * 获取标记点列表
   */
  fun getMarkPoints(): List<Int> {
    return currentAudioRecordInfo?.markPoints ?: emptyList()
  }
  // ========== 配置创建方法 ==========


  /**
   * 创建 API 选项
   */
  private fun createApiOptions(): Map<String, String> {
    return HeaderUtils.makeCommonHeader() + ApiOptionsProviderUtils.getOptions().mapValues {
      it.value.toString()
    }
  }

  // ========== 私有方法：处理转写结果 ==========

  private fun handleTranscriptionResult(result: TranscriptionResult) {
    Log.d(TAG, "处理转写结果: ${getResultSummary(result)}")

    // 使用协程处理转写结果
    meetingScope.launch {
      try {
        val language = currentParams?.language?.source?.value ?: "cn"

        when (result) {
          is TranscriptionResult.IntermediateResult -> {
            realtimeTransformManager.incrementDataMiddle(result, language)
          }

          is TranscriptionResult.ProcessingTextResult -> {
            realtimeTransformManager.incrementDataText(result, language)
          }

          is TranscriptionResult.FinalTextResult -> {
            realtimeTransformManager.incrementDataText(result, language)
          }

          is TranscriptionResult.AgendaResult -> {
            agentDataManager.insertAgendaData(result)
          }

          else -> {
            Log.d(TAG, "未处理的转写结果类型: ${result::class.simpleName}")
          }
        }
      } catch (e: Exception) {
        Log.e(TAG, "处理转写结果时发生异常", e)
      }
    }
  }

  /**
   * 处理转写错误
   */
  private fun handleTranscriptionError(error: String) {
    // 处理转写错误的逻辑
    Log.e(TAG, "处理转写错误: $error")
  }

  /**
   * 处理状态变化
   */
  private fun handleStatusChanged(status: Int) {
    // 处理状态变化的逻辑
    Log.d(TAG, "处理状态变化: $status")
  }

  /**
   * 获取转写结果摘要
   */
  private fun getResultSummary(result: TranscriptionResult): String {
    return "TranscriptionResult(text='${result})"
  }


  /**
   * 获取当前实时转写行的混合数据流
   * 这是最重要的数据访问接口，供 UI 订阅使用
   */
  fun getCurrentLineMix() = realtimeTransformManager.currentLineMix

  /**
   * 获取当前的所有议程的数据流
   */
  fun getAllAgentFlow() = agentDataManager.allAgendaList

  /**
   * 将当前中间结果转换为最终文本
   * 通常在录音结束时调用
   */
  fun finalizeCurrentText() {
    val endTime = getRecordingDuration().toInt()
    realtimeTransformManager.toText(endTime)
  }

  /**
   * 更新时间戳偏移量
   * 用于断点续传等场景
   */
  fun updateTimeWeight(weight: Int) {
    realtimeTransformManager.updateTimeWeight(weight)
  }


  /**
   * 获取当前原文行数据
   */
  fun getCurrentLine(): RuntimeDataItemV2? {
    return realtimeTransformManager.getCurrentLine()
  }

  /**
   * 在当前数据中查找并替换文本
   * 参考 NoteRecordProvider 的相关方法
   */
  fun replaceCurrentDataListText(searchText: String, replaceText: String): Int {
    return realtimeTransformManager.replaceCurrentDataListText(searchText, replaceText)
  }

  /**
   * 更新当前行的文本项列表
   * 用于编辑和修正功能
   */
  fun updateCurrentDataListText(
    newItems: List<ConversationRow2.ContentItem>,
    max: Int
  ) {
    realtimeTransformManager.updateCurrentDataListText(newItems, max)
  }

  // ========== 音频文件监控（兼容原来的上传逻辑）==========

  /**
   * 启动音频文件监控
   * 模拟原来 MixMultiEndAudioManager 的回调机制
   * @param mp3FilePath MP3文件路径
   */
  private fun startAudioFileMonitoring(mp3FilePath: String) {
    audioUploadMonitorJob?.cancel()
    audioUploadMonitorJob = meetingScope.launch {
      val file = File(mp3FilePath)
      var lastFileLength = 0L

      while (isActive && _uiState.value.recordingState == RecordingState.RECORDING) {
        try {
          val currentFileLength = if (file.exists()) file.length() else 0L

          // 如果文件长度有变化，触发回调
          if (currentFileLength != lastFileLength) {
            lastFileLength = currentFileLength

            // 检查是否需要触发上传
            val diff = currentFileLength - lastTriggerUploadFileLength
            if (diff >= UPLOAD_DIFF_SIZE) {
              lastTriggerUploadFileLength = currentFileLength
              Log.d(TAG, "音频文件监控: 触发上传, diff=$diff, fileLength=$currentFileLength")

              // 调用原来的回调逻辑
              audioChangeListener?.invoke(false, currentFileLength)
            }
          }

          delay(1000) // 每秒检查一次文件大小
        } catch (e: Exception) {
          Log.e(TAG, "音频文件监控异常", e)
          break
        }
      }

      // 录音结束时，最后一次调用回调
      if (_uiState.value.recordingState == RecordingState.STOPPED) {
        val finalFileLength = if (file.exists()) file.length() else 0L
        Log.d(TAG, "音频文件监控: 录音结束, finalFileLength=$finalFileLength")
        audioChangeListener?.invoke(true, finalFileLength)
      }
    }
  }

  /**
   * 启动录音时长监控
   * 模拟原来 listenRecordDuration 的超时处理逻辑
   */
  private fun startDurationMonitoring() {
    durationMonitorJob?.cancel()
    durationMonitorJob = meetingScope.launch {
      while (isActive && _uiState.value.recordingState == RecordingState.RECORDING) {
        try {
          val currentDuration = _uiState.value.durationMs

          // 检查是否超过最大录音时长
          if (currentDuration >= RECORD_MAX_DURATION) {
            Log.i(TAG, "录音时长监控: 超过最大时长, currentDuration=${currentDuration}ms")

            // 触发超时停止
            if (_uiState.value.recordingState == RecordingState.RECORDING) {
              Log.i(TAG, "录音时长监控: 触发超时停止")

              // 停止录音
              stop()

              // 调用超时回调
              onMaxTimeFinishedCallback?.invoke()
            }
            break
          }

          delay(1000) // 每秒检查一次录音时长
        } catch (e: Exception) {
          Log.e(TAG, "录音时长监控异常", e)
          break
        }
      }
    }
  }

}
