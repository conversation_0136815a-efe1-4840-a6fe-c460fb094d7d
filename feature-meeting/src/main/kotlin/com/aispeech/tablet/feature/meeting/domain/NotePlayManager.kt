package com.aispeech.tablet.feature.meeting.domain

import com.aispeech.aibase.AILog
import com.aispeech.tablet.core.meeting.domain.NoteMeetingDataManager
import com.aispeech.tablet.core.model.entity.audio.AudioRecordInfo
import dagger.hilt.android.scopes.ActivityRetainedScoped
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.withContext
import javax.inject.Inject

//@ActivityRetainedScoped
//class NotePlayManager @Inject constructor(
//  private val dataManager: NoteMeetingDataManager,
//) {
//
//  private var _playIndex = MutableStateFlow(0)
//  val playIndex: StateFlow<Int> = _playIndex.asStateFlow()
//
//  suspend fun changeRecordInfoItem(index: Int): AudioRecordInfo {
//    _playIndex.value = index
//    AILog.i(TAG, "changeCurrentAudio: $index")
//    return withContext(Dispatchers.IO) {
//      return@withContext dataManager.findRecordInfo(index)
//    }
//  }
//
//  companion object {
//    const val TAG = "NotePlayManager"
//  }
//}