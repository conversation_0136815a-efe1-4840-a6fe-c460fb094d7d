package com.aispeech.tablet.feature.meeting.extensions

import android.annotation.SuppressLint
import com.aispeech.tablet.feature.meeting.entry.CustomDateFormat
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale

@SuppressLint("SimpleDateFormat")
fun Long.timestampFormat(customFormat: CustomDateFormat = CustomDateFormat.YYYY_MM_DD_HH_MM_SS_Z): String {
  val format = SimpleDateFormat(customFormat.content)
  val date = Date(this)
  return format.format(date)
}

fun Int.formatMillisecond(): String {
  val seconds = this / 1000
  return formatSeconds(seconds)
}

fun Int.formatSecond(): String {
  val seconds = this
  return formatSeconds(seconds)
}

private fun formatSeconds(seconds: Int): String {
  val hours = seconds / 3600
  val minutes = (seconds % 3600) / 60
  val remainingSeconds = seconds % 60

  return String.format(Locale.US, "%02d:%02d:%02d", hours, minutes, remainingSeconds)
}
