package com.aispeech.tablet.feature.meeting.data

import com.aispeech.tablet.core.res.TabletStringsUtils

sealed class RecordEndNotification(
  val reason: String,
  open val title: String?,
  open val content: String?
) {
  data object Normal : RecordEndNotification(TabletStringsUtils.getString(com.aispeech.tablet.core.res.R.string.recordendnotification_1739176666062_0), null, null)

  sealed class Alert(
    reason: String,
    val message: String,
    override val title: String = TabletStringsUtils.getString(com.aispeech.tablet.core.res.R.string.globalservice_1738985432084_0)
  ) : RecordEndNotification(reason, title, message) {
    data object PauseTimeout : Alert(TabletStringsUtils.getString(com.aispeech.tablet.core.res.R.string.recordendnotification_1739176666062_1), TabletStringsUtils.getString(com.aispeech.tablet.core.res.R.string.recordendnotification_1739176666062_2))
    data object MaxDuration : Alert(TabletStringsUtils.getString(com.aispeech.tablet.core.res.R.string.recordendnotification_1739176666062_3), TabletStringsUtils.getString(com.aispeech.tablet.core.res.R.string.recordendnotification_1739176666062_4))
  }

  sealed class Confirm(
    reason: String,
    val message: String,
    override val title: String = TabletStringsUtils.getString(com.aispeech.tablet.core.res.R.string.recordendnotification_1739176666062_5)
  ) : RecordEndNotification(reason, title, message) {
    data object CaseClosed :
      Confirm(TabletStringsUtils.getString(com.aispeech.tablet.core.res.R.string.recordendnotification_1739176666062_6), TabletStringsUtils.getString(com.aispeech.tablet.core.res.R.string.recordendnotification_1739176666062_7))

    data object ManualBack :
      Confirm(TabletStringsUtils.getString(com.aispeech.tablet.core.res.R.string.recordendnotification_1739176666062_8), TabletStringsUtils.getString(com.aispeech.tablet.core.res.R.string.recordendnotification_1739176666062_9))
  }
}
