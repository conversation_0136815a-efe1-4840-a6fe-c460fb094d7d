package com.aispeech.tablet.feature.meeting.di

import com.aispeech.aimeeting.api.OtMessageTransformInterface
import com.aispeech.aimeeting.api.audio.processors.AudioDurationProcessor
import com.aispeech.aimeeting.api.audio.processors.AudioIntensityProcessor
import com.aispeech.aimeeting.api.entities.AudioConfig
import com.aispeech.tablet.feature.meeting.domain.MixAudioDataManager
import com.aispeech.tablet.feature.meeting.domain.OtMessageTransformManager
import dagger.Binds
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.components.ViewModelComponent
import javax.inject.Qualifier


//@Qualifier
//@Retention(AnnotationRetention.BINARY)
//annotation class LocalAudioDataManager
//
//@Qualifier
//@Retention(AnnotationRetention.BINARY)
//annotation class RemoteAudioDataManager


@Module
@InstallIn(ViewModelComponent::class)
internal interface RecordModule {
  @Binds
  fun bindOtMessageTransform(manager: OtMessageTransformManager): OtMessageTransformInterface

  @Module
  @InstallIn(ViewModelComponent::class)
  object Providers {


    @Provides
    fun provideLocalAudioDataManager(
      durationProcessor: AudioDurationProcessor,
      intensityProcessor: AudioIntensityProcessor,
    ): MixAudioDataManager {
      return MixAudioDataManager(
        audioConfig = AudioConfig(),
        durationProcessor = durationProcessor,
        intensityProcessor = intensityProcessor
      )
    }

//    @Provides
//    @RemoteAudioDataManager
//    fun provideRemoteAudioDataManager(
//      durationProcessor: AudioDurationProcessor,
//      intensityProcessor: AudioIntensityProcessor,
//    ): MixAudioDataManager {
//      return MixAudioDataManager(
//        audioConfig = RemoteAudioConfig(),
//        durationProcessor = durationProcessor,
//        intensityProcessor = intensityProcessor
//      )
//    }
  }
}