package com.aispeech.tablet.feature.meeting.encoder.impl

import com.aispeech.tablet.feature.meeting.encoder.AbsEncoder
import com.aispeech.tablet.lib.duilite.OggOpusEncoder

class AudioOggOpusEncoderImpl : AbsEncoder() {
  private var opusDecoder: OggOpusEncoder? = null

  override fun init(sampleRate: Int, channel: Int,onEncoderBuffer:(data: ByteArray, size: Int) -> Unit?) {
    opusDecoder = OggOpusEncoder()
    opusDecoder?.init(sampleRate, channel) { data, size ->
      onEncoderBuffer?.invoke(data, size)
    }
  }

  override fun start() {
    opusDecoder?.start()
  }

  override fun feed(byteArray: ByteArray) {
    opusDecoder?.feed(byteArray)
  }

  override fun stop() {
    opusDecoder?.stop()
  }

  override fun destroy() {
    opusDecoder?.destroy()
    opusDecoder = null
  }

}