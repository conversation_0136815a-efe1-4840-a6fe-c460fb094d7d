@file:Suppress("PrivateApi", "DiscouragedPrivateApi")
package com.aispeech.tablet.feature.meeting.utils

import android.os.IBinder
import android.util.Log
import java.lang.reflect.Method

/**
 * 对 Doze / PowerSave 白名单的系统级操作工具。
 *
 * 注意事项：
 * - 仅限 system-signed / privileged app 使用
 * - 需要 DEVICE_POWER 权限
 * - 完全基于反射实现，兼容性依赖于系统版本
 */
object DozeWhitelistManager {

  private const val TAG = "DozeWhitelistMgr"
  private const val DEVICE_IDLE_SERVICE = "deviceidle"

  /**
   * 操作结果密封类，提供更详细的错误信息
   */
  sealed class Result {
    object Success : Result()
    data class Failure(val reason: String, val exception: Throwable? = null) : Result()
  }

  // 反射获取的类和方法缓存
  private val serviceManagerClass: Class<*>? by lazy {
    runCatching {
      Class.forName("android.os.ServiceManager")
    }.onFailure {
      Log.e(TAG, "Cannot find ServiceManager class", it)
    }.getOrNull()
  }

  private val getServiceMethod: Method? by lazy {
    runCatching {
      serviceManagerClass?.getDeclaredMethod("getService", String::class.java)
    }.onFailure {
      Log.e(TAG, "Cannot find getService method", it)
    }.getOrNull()
  }

  // DeviceIdle 服务的 Binder 接口
  private val deviceIdleBinder: IBinder? by lazy {
    runCatching {
      getServiceMethod?.invoke(null, DEVICE_IDLE_SERVICE) as? IBinder
    }.onFailure {
      Log.e(TAG, "Cannot get deviceidle service binder", it)
    }.getOrNull()
  }

  /**
   * 将包名加入永久 Doze 白名单
   *
   * @param packageName 要加入白名单的包名
   * @return 操作结果，包含成功状态或详细错误信息
   */
  fun addPackage(packageName: String): Result {
    if (!isValidPackageName(packageName)) {
      return Result.Failure("Invalid package name: $packageName")
    }

    return executeBinderOperation("addPowerSaveWhitelistApp", packageName) {
      Log.i(TAG, "Successfully added $packageName to Doze whitelist")
    }
  }

  /**
   * 从白名单移除包名
   *
   * @param packageName 要移除的包名
   * @return 操作结果，包含成功状态或详细错误信息
   */
  fun removePackage(packageName: String): Result {
    if (!isValidPackageName(packageName)) {
      return Result.Failure("Invalid package name: $packageName")
    }

    return executeBinderOperation("removePowerSaveWhitelistApp", packageName) {
      Log.i(TAG, "Successfully removed $packageName from Doze whitelist")
    }
  }

  /**
   * 查询包名是否在白名单中
   *
   * @param packageName 要查询的包名
   * @return 如果在白名单中返回 true，否则返回 false。服务不可用时也返回 false
   */
  fun isWhitelisted(packageName: String): Boolean {
    if (!isValidPackageName(packageName)) {
      Log.w(TAG, "Invalid package name for whitelist check: $packageName")
      return false
    }

    return runCatching {
      val binder = deviceIdleBinder ?: return false

      // 创建 Parcel 用于跨进程通信
      val data = android.os.Parcel.obtain()
      val reply = android.os.Parcel.obtain()

      try {
        data.writeInterfaceToken("android.os.IDeviceIdleController")
        data.writeString(packageName)

        // 调用 isPowerSaveWhitelistApp 方法 (通常是事务码 6)
        binder.transact(6, data, reply, 0)
        reply.readException()
        reply.readInt() != 0
      } finally {
        data.recycle()
        reply.recycle()
      }
    }.onFailure { exception ->
      Log.e(TAG, "Failed to check whitelist status for $packageName", exception)
    }.getOrDefault(false)
  }

  /**
   * 检查 DeviceIdle 服务是否可用
   *
   * @return 如果服务可用返回 true，否则返回 false
   */
  fun isServiceAvailable(): Boolean = deviceIdleBinder != null

  /**
   * 获取当前所有白名单应用包名列表
   *
   * @return 白名单应用包名数组，失败时返回空数组
   */
  fun getWhitelistedPackages(): Array<String> {
    return runCatching {
      val binder = deviceIdleBinder ?: return emptyArray()

      val data = android.os.Parcel.obtain()
      val reply = android.os.Parcel.obtain()

      try {
        data.writeInterfaceToken("android.os.IDeviceIdleController")

        // 调用 getFullPowerWhitelist 方法 (通常是事务码 7)
        binder.transact(7, data, reply, 0)
        reply.readException()
        reply.createStringArray() ?: emptyArray()
      } finally {
        data.recycle()
        reply.recycle()
      }
    }.onFailure { exception ->
      Log.e(TAG, "Failed to get whitelisted packages", exception)
    }.getOrDefault(emptyArray())
  }

  // 执行 Binder 操作的通用方法
  private inline fun executeBinderOperation(
    operationName: String,
    packageName: String,
    onSuccess: () -> Unit
  ): Result {
    val binder = deviceIdleBinder
    if (binder == null) {
      val message = "DeviceIdle service not available for operation: $operationName"
      Log.e(TAG, message)
      return Result.Failure(message)
    }

    return runCatching {
      val data = android.os.Parcel.obtain()
      val reply = android.os.Parcel.obtain()

      try {
        data.writeInterfaceToken("android.os.IDeviceIdleController")
        data.writeString(packageName)

        // 根据操作类型选择事务码
        val transactionCode = when (operationName) {
          "addPowerSaveWhitelistApp" -> 4
          "removePowerSaveWhitelistApp" -> 5
          else -> throw IllegalArgumentException("Unknown operation: $operationName")
        }

        binder.transact(transactionCode, data, reply, 0)
        reply.readException()

        onSuccess()
        Result.Success
      } finally {
        data.recycle()
        reply.recycle()
      }
    }.onFailure { exception ->
      val message = when (exception) {
        is SecurityException -> "Permission denied for $operationName"
        is IllegalArgumentException -> "Invalid operation: $operationName"
        else -> "Unexpected error during $operationName"
      }
      Log.e(TAG, "$message: ${exception.message}", exception)
    }.getOrElse { exception ->
      Result.Failure(exception.message ?: "Unknown error", exception)
    }
  }

  // 简单的包名有效性检查
  private fun isValidPackageName(packageName: String): Boolean {
    return packageName.isNotBlank() &&
      packageName.contains('.') &&
      packageName.matches(Regex("^[a-zA-Z0-9._]+$"))
  }
}

// 扩展函数，提供更便利的使用方式
/**
 * 批量添加包名到白名单
 */
fun DozeWhitelistManager.addPackages(packageNames: List<String>): Map<String, DozeWhitelistManager.Result> {
  return packageNames.associateWith { addPackage(it) }
}

/**
 * 批量移除包名从白名单
 */
fun DozeWhitelistManager.removePackages(packageNames: List<String>): Map<String, DozeWhitelistManager.Result> {
  return packageNames.associateWith { removePackage(it) }
}

/**
 * 检查多个包名的白名单状态
 */
fun DozeWhitelistManager.checkPackages(packageNames: List<String>): Map<String, Boolean> {
  return packageNames.associateWith { isWhitelisted(it) }
}

