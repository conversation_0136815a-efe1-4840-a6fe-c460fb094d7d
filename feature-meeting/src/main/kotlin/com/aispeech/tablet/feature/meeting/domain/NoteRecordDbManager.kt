package com.aispeech.tablet.feature.meeting.domain

import com.aispeech.aibase.AILog
import com.aispeech.aimeeting.api.entities.ConversationRow
import com.aispeech.aimeeting.api.entities.RuntimeDataItem
import com.aispeech.aimeeting.api.entities.RuntimeTextItem
import com.aispeech.tablet.core.common.UserConfig
import com.aispeech.tablet.core.db.DbManager
import com.aispeech.tablet.core.db.dao.NoteContentDao
import com.aispeech.tablet.core.db.dao.NoteTranscriptionDao
import com.aispeech.tablet.core.db.use
import com.aispeech.tablet.core.meeting.entity.ConversationRow2
import com.aispeech.tablet.core.meeting.entity.RuntimeTextItemV2
import com.aispeech.tablet.core.model.entity.NoteContentEntity
import com.aispeech.tablet.core.model.entity.NoteContentType
import com.aispeech.tablet.core.model.entity.NoteTranscriptionEntity
import com.aispeech.tablet.core.model.entity.TranscriptionPart
import com.aispeech.tablet.core.model.entity.TranscriptionType
import com.aispeech.tablet.core.model.entity.audio.AudioRecordInfo
import com.squareup.moshi.JsonAdapter
import dagger.hilt.android.scopes.ActivityRetainedScoped
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.withContext
import javax.inject.Inject
//
//@ActivityRetainedScoped
//class NoteRecordDbManager @Inject constructor(
//  private val infoAdapter: JsonAdapter<AudioRecordInfo>
//) {
//
//  lateinit var noteId: String
//  private lateinit var pageFlow: StateFlow<String>
//
//  private var contentList: MutableList<NoteContentEntity> = mutableListOf()
//
//  fun setNoteId(noteId: String, pageFlow: StateFlow<String>) {
//    this.noteId = noteId
//    this.pageFlow = pageFlow
//    AILog.i(TAG, "setNoteId: $noteId, page: ${pageFlow.value}")
//  }
//
//  suspend fun getAll(
//    type: NoteContentType = NoteContentType.Voice,
//    sortDescending: Boolean = true
//  ): List<NoteContentEntity> =
//    withContext(Dispatchers.IO) {
//      contentList = NoteContentDao.use().getAll(noteId)
//        .filter { it.type == type }.toMutableList()
//
//      if (sortDescending) {
//        // 需要倒序显示
//        contentList.sortByDescending {
//          it.extraInfo?.let { it1 -> infoAdapter.fromJson(it1)?.createTime }
//        }
//      }
//      return@withContext contentList
//    }
//
//
//  suspend fun getTranscriptionItem(index: Int): List<ConversationRow2> =
//    withContext(Dispatchers.Default) {
//      val current = contentList[index]
//      val noteTranscriptions = NoteTranscriptionDao.use().getAll(noteId)
//        .filter { it.noteContentOwnerId == current.noteContentId }
//      return@withContext noteTranscriptions.toRows2()
//    }
//
//  suspend fun insertItem(item: RuntimeDataItem, id: String) =
////    withContext(Dispatchers.IO) {
////      val parts = item.text.map { it.toTranscriptionPart() }
////      val entity = createTranscriptionEntity(id, item.ts.toInt(), item.speaker, parts)
////      NoteTranscriptionDao.use().upsert(entity)
//    }
//
//  private suspend fun insertContentEntity(contentEntity: NoteContentEntity) =
//    withContext(Dispatchers.IO) {
////      AILog.i(TAG, "insertContentEntity: $contentEntity")
//      DbManager.getAITabletDB().noteContentDao().upsert(contentEntity)
//    }
//
//
//  private fun createTranscriptionEntity(
//    id: String,
//    timestamp: Int,
//    speaker: String,
//    type: TranscriptionType,
//    parts: List<TranscriptionPart>
//  ): NoteTranscriptionEntity {
//    return NoteTranscriptionEntity(
//      userOwnerId = UserConfig.userId,
//      noteOwnerId = noteId,
//      pageOwnerId = pageFlow.value,
//      noteContentOwnerId = id,
//      timeStamp = timestamp,
//      speaker = speaker,
//      type = type,
//      tpList = parts
//    )
//  }
//
//
//  private suspend fun cleanTranscriptionEntityWith(content: NoteContentEntity) =
//    withContext(Dispatchers.IO) {
//      AILog.i(TAG, "cleanTranscriptionEntity: $content")
//      val dao = NoteTranscriptionDao.use()
//      val notes = dao.getAll(noteId).filter { it.noteContentOwnerId == content.noteContentId }
//      notes.forEach {
//        dao.delete(it)
//      }
//    }
//
//  suspend fun cleanTranscriptionEntity(id: String) {
//    val content = contentList.find { it.noteContentId == id }
//    content?.let { cleanTranscriptionEntityWith(it) }
//  }
//
//  suspend fun cleanTranscriptionEntity(index: Int = 0) =
//    withContext(Dispatchers.IO) {
//      val current = contentList[index]
//      cleanTranscriptionEntityWith(current)
//    }
//
//  suspend fun createContentEntityAndSave(): NoteContentEntity {
//    val entity = NoteContentEntity.build(
//      userOwnerId = UserConfig.userId,
//      noteOwnerId = noteId,
//      pageOwnerId = pageFlow.value,
//      type = NoteContentType.Voice,
//    )
//    contentList.add(0, entity)
//    insertContentEntity(entity)
//    return entity
//  }
//
//  suspend fun updateCurrentInfo(audioRecordInfo: AudioRecordInfo) {
//    val json = infoAdapter.toJson(audioRecordInfo)
//    if (audioRecordInfo.foreignId != null) {
//      updateContentEntity(audioRecordInfo.foreignId!!) {
//        it.copy(extraInfo = json)
//      }
//    } else {
//      updateContentEntity {
//        it.copy(extraInfo = json)
//      }
//    }
//  }
//
//  private suspend fun updateContentEntity(
//    id: String,
//    apply: (NoteContentEntity) -> NoteContentEntity
//  ) {
//    val index = contentList.indexOfFirst { it.noteContentId == id }
//    if (index != -1) {
//      val update = apply(contentList[index])
//      contentList[index] = update
//      insertContentEntity(update)
//    }
//
//  }
//
//  private suspend fun updateContentEntity(
//    index: Int = 0,
//    apply: ((NoteContentEntity) -> NoteContentEntity)
//  ) {
//    val current = apply(contentList[index])
//    contentList[index] = current
//    insertContentEntity(current)
//  }
//
//  companion object {
//    const val TAG = "NoteRecordDbManager"
//  }
//}
//
//private fun RuntimeTextItem.toTranscriptionPart(): TranscriptionPart {
//  return TranscriptionPart(start = begin.toInt(), end = end.toInt(), text = text, spoken = spoken)
//}
//
//private fun RuntimeTextItemV2.toTranscriptionPart(): TranscriptionPart {
//  return TranscriptionPart(start = begin.toInt(), end = end.toInt(), text = text, spoken = spoken)
//}
//
//private fun List<NoteTranscriptionEntity>.toRows2(): List<ConversationRow2> {
//  return map {
//    val result = mutableListOf<ConversationRow2>()
//    val info =
//      ConversationRow2.SpeakerInfo(it.speaker.substring(3), it.speaker, it.timeStamp.toString())
//    val items = it.tpList.map { it ->
//      ConversationRow2.ContentItem(it.start, it.end, it.spoken, it.text)
//    }
//    result.add(
//      ConversationRow2(
//        info,
//        items
//      )
//    )
//    result
//  }.flatten()
//}
//
//private fun List<NoteTranscriptionEntity>.toRows(): List<ConversationRow> {
//  return map {
//    val result = mutableListOf<ConversationRow>()
//    result.add(
//      ConversationRow.SpeakerRow(
//        it.speaker.substring(3), it.speaker, it.timeStamp.toString()
//      )
//    )
//    val items = it.tpList.map { it ->
//      ConversationRow.ContentRow.ContentItem(it.start, it.end, it.spoken, it.text)
//    }
//    result.add(ConversationRow.ContentRow(items))
//    result
//  }.flatten()
//}