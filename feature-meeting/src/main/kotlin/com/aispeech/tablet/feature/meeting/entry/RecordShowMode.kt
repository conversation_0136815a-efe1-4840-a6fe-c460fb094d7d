package com.aispeech.tablet.feature.meeting.entry

import javax.annotation.concurrent.Immutable

@Immutable
data class RecordShowMode(
  val isShow: Boolean = false,
  val windowSizeClass: RecordWindowSizeClass = RecordWindowSizeClass.Medium,
  val floatingWindow: RecordFloatingWindow = RecordFloatingWindow.None
) {
  fun switchShowState() = this.copy(isShow = isShow.not())

  val isMedium = windowSizeClass == RecordWindowSizeClass.Medium

  val isExpanded = windowSizeClass == RecordWindowSizeClass.Expanded
}

@JvmInline
value class RecordFloatingWindow private constructor(val value: Int) {
  companion object {
    val None = RecordFloatingWindow(0)
    val AudioList = RecordFloatingWindow(1)
  }

  override fun toString(): String {
    return "RecordFloatingWindow." + when (this) {
      None -> "None"
      AudioList -> "AudioList"
      else -> ""
    }
  }
}

@JvmInline
value class RecordWindowSizeClass private constructor(val value: Int) {
  companion object {
    val Compact = RecordWindowSizeClass(1)
    val Medium = RecordWindowSizeClass(2)
    val Expanded = RecordWindowSizeClass(3)
  }

  override fun toString(): String {
    return "RecordWindowSizeClass." + when (this) {
      Compact -> "Compact"
      Medium -> "Medium"
      Expanded -> "Expanded"
      else -> ""
    }
  }
}