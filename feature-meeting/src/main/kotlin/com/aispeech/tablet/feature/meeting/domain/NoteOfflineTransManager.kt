package com.aispeech.tablet.feature.meeting.domain

import com.aispeech.tablet.core.meeting.domain.NoteMeetingDataManager
import com.aispeech.tablet.core.meeting.entity.RecordOfflineData
import com.aispeech.tablet.core.meeting.entity.RecordOfflineState
import com.aispeech.tablet.core.meeting.entity.RuntimeDataItemV2
import dagger.hilt.android.scopes.ActivityRetainedScoped
import javax.inject.Inject

@ActivityRetainedScoped
class NoteOfflineTransManager @Inject constructor(
  private val recordDataManager: NoteMeetingDataManager,
) {

  /**
   * 更新离线转写状态
   */
  suspend fun updateOfflineToNormal(objectId: String) {
    updateOfflineData(objectId) {
      it.copy(state = RecordOfflineState.Normal, progress = 0, offlineTask = null)
    }
  }

  /**
   * 更新离线转写数据
   */
  private suspend fun updateOfflineData(objectId: String, copy: (RecordOfflineData) -> RecordOfflineData) {
//    recordDataManager.updateOfflineData(objectId, copy)
  }

  /**
   * 文件转写清理更新新的内容
   */
  suspend fun offlineCleanAndUpdateContent(
    objectId: String,
    list: List<RuntimeDataItemV2>,
  ) {
    recordDataManager.offlineCleanAndUpdateContent(objectId, list)
  }

  /**
   * 通知内容更新
   */
  suspend fun notifyContentUpdate() {
    recordDataManager.updateAllData()
  }

  companion object {
    const val TAG = "NoteOfflineTransManager"
  }

}