package com.aispeech.tablet.feature.meeting.utils

import android.content.ComponentName
import android.content.Context
import android.content.Intent
import android.content.pm.ApplicationInfo
import android.content.pm.PackageManager
import com.aispeech.aibase.AILog

object PackageUnstoppedHelper {
  private const val TAG = "PackageUnstoppedHelper"

  /**
   * 检查应用是否处于 stopped 状态
   * @param context 当前 context
   * @param packageName 目标包名
   * @return true 表示应用处于 stopped 状态，false 表示应用正常或不存在
   */
  fun isPackageStopped(context: Context, packageName: String): Boolean {
    return try {
      val packageManager = context.packageManager
      val applicationInfo = packageManager.getApplicationInfo(packageName, PackageManager.GET_META_DATA)

      // 检查 ApplicationInfo.FLAG_STOPPED 标志
      val isStopped = (applicationInfo.flags and ApplicationInfo.FLAG_STOPPED) != 0
      AILog.d(TAG, "Package $packageName stopped state: $isStopped")
      isStopped
    } catch (e: PackageManager.NameNotFoundException) {
      AILog.w(TAG, "Package $packageName not found")
      false
    } catch (e: Exception) {
      AILog.e(TAG, "Error checking stopped state for $packageName: ${e.message}")
      false
    }
  }

  /**
   * 检查应用是否可用（已安装且未被停用）
   * @param context 当前 context
   * @param packageName 目标包名
   * @return true 表示应用可用，false 表示应用不可用或不存在
   */
  fun isPackageAvailable(context: Context, packageName: String): Boolean {
    return try {
      val packageManager = context.packageManager
      val applicationInfo = packageManager.getApplicationInfo(packageName, PackageManager.GET_META_DATA)

      // 检查应用是否启用
      val isEnabled = applicationInfo.enabled
      // 检查应用是否未被停止
      val isNotStopped = (applicationInfo.flags and ApplicationInfo.FLAG_STOPPED) == 0

      val isAvailable = isEnabled && isNotStopped
      AILog.d(TAG, "Package $packageName availability: enabled=$isEnabled, notStopped=$isNotStopped, available=$isAvailable")
      isAvailable
    } catch (e: PackageManager.NameNotFoundException) {
      AILog.w(TAG, "Package $packageName not found")
      false
    } catch (e: Exception) {
      AILog.e(TAG, "Error checking availability for $packageName: ${e.message}")
      false
    }
  }

  /**
   * 智能激活包：先检查是否需要激活，如果需要则激活
   * @param context 当前 context（建议用 applicationContext）
   * @param packageName 目标包名
   * @param activityName 目标透明 Activity 全类名（如 com.aispeech.hybridspeech.UnfreezeActivity）
   * @return true 成功（包括本来就是可用状态），false 失败
   */
  fun ensurePackageActive(
    context: Context,
    packageName: String,
    activityName: String = "$packageName.UnfreezeActivity"
  ): Boolean {
    // 先检查应用状态
    if (isPackageAvailable(context, packageName)) {
      AILog.d(TAG, "Package $packageName is already available, no activation needed")
      return true
    }

    // 如果应用处于 stopped 状态，尝试激活
    if (isPackageStopped(context, packageName)) {
      AILog.i(TAG, "Package $packageName is stopped, attempting to activate...")
      return activateByActivity(context, packageName, activityName)
    }

    // 应用不存在或其他问题
    AILog.w(TAG, "Package $packageName is not available and not in stopped state")
    return false
  }

  /**
   * 通过启动目标包的透明 Activity 激活包
   * @param context 当前 context（建议用 applicationContext）
   * @param packageName 目标包名
   * @param activityName 目标透明 Activity 全类名（如 com.aispeech.hybridspeech.UnfreezeActivity）
   * @return true 成功 false 失败
   */
  fun activateByActivity(
    context: Context,
    packageName: String,
    activityName: String = "$packageName.UnfreezeActivity"
  ): Boolean {
    return try {
      val intent = Intent().apply {
        component = ComponentName(packageName, activityName)
        addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
      }
      context.startActivity(intent)
      AILog.i(TAG, "已通过 Activity 激活 $packageName")
      true
    } catch (e: Exception) {
      AILog.e(TAG, "通过 Activity 激活 $packageName 失败, exception=${e.message}")
      false
    }
  }

  /**
   * 通过 shell 命令清除应用的 stopped 状态
   * 需要有 system 或 shell 权限
   * @param packageName 要解冻的包名
   * @return true 表示执行成功，false 表示失败
   */
  fun clearStoppedStateByShell(packageName: String): Boolean {
    if (packageName.isBlank()) {
      AILog.e(TAG, "包名不能为空！")
      return false
    }

    return try {
      val cmd = arrayOf("sh", "-c", "cmd package clear-stopped-state $packageName")
      val process = Runtime.getRuntime().exec(cmd)
      val exitCode = process.waitFor()

      if (exitCode == 0) {
        AILog.i(TAG, "已成功通过 shell 清除 $packageName 的 stopped 状态")
        true
      } else {
        val error = process.errorStream.bufferedReader().use { it.readText() }
        AILog.e(TAG, "Shell 命令执行失败，exitCode=$exitCode，错误信息：$error")
        false
      }
    } catch (e: Exception) {
      AILog.e(TAG, "通过 shell 解冻包时发生异常: ${e.message}")
      false
    }
  }

  /**
   * 综合激活方法：优先使用 Activity 激活，失败时尝试 shell 命令
   * @param context 当前 context（建议用 applicationContext）
   * @param packageName 目标包名
   * @param activityName 目标透明 Activity 全类名
   * @param useShellFallback 是否在 Activity 激活失败时使用 shell 命令作为备选方案
   * @return true 成功，false 失败
   */
  fun activatePackageComprehensive(
    context: Context,
    packageName: String,
    activityName: String = "$packageName.UnfreezeActivity",
    useShellFallback: Boolean = true
  ): Boolean {
    // 先检查应用状态
    if (isPackageAvailable(context, packageName)) {
      AILog.d(TAG, "Package $packageName is already available")
      return true
    }

    // 如果应用不是处于 stopped 状态，直接返回失败
    if (!isPackageStopped(context, packageName)) {
      AILog.w(TAG, "Package $packageName is not in stopped state, cannot activate")
      return false
    }

    // 尝试通过 Activity 激活
    AILog.i(TAG, "Attempting to activate $packageName via Activity...")
    if (activateByActivity(context, packageName, activityName)) {
      // 验证激活是否成功
      Thread.sleep(1000) // 等待一秒让系统更新状态
      if (isPackageAvailable(context, packageName)) {
        AILog.i(TAG, "Package $packageName activated successfully via Activity")
        return true
      }
    }

    // Activity 激活失败，尝试 shell 命令（如果启用）
    if (useShellFallback) {
      AILog.i(TAG, "Activity activation failed, trying shell command for $packageName...")
      if (clearStoppedStateByShell(packageName)) {
        // 验证激活是否成功
        Thread.sleep(1000) // 等待一秒让系统更新状态
        if (isPackageAvailable(context, packageName)) {
          AILog.i(TAG, "Package $packageName activated successfully via shell command")
          return true
        }
      }
    }

    AILog.e(TAG, "Failed to activate package $packageName using all available methods")
    return false
  }
}
