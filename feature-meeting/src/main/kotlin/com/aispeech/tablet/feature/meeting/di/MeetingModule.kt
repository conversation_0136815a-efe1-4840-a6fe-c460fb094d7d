package com.aispeech.tablet.feature.meeting.di

import com.aispeech.tablet.core.model.adapter.RecordStateAdapter
import com.aispeech.tablet.core.model.entity.audio.AudioRecordInfo
import com.squareup.moshi.JsonAdapter
import com.squareup.moshi.Moshi
import com.squareup.moshi.adapter
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import javax.inject.Qualifier
import javax.inject.Singleton

@Qualifier
@Retention(AnnotationRetention.BINARY)
annotation class MeetingScope

@Module
@InstallIn(SingletonComponent::class)
internal object MeetingModule {

  @Provides
  @MeetingScope
  fun provideMeetingScope(): CoroutineScope = CoroutineScope(SupervisorJob() + Dispatchers.Default)

  @OptIn(ExperimentalStdlibApi::class)
  @Singleton
  @Provides
  fun provideRecordInfoAdapter(): JsonAdapter<AudioRecordInfo> {
    return Moshi.Builder().add(RecordStateAdapter()).build().adapter<AudioRecordInfo>()
  }


}