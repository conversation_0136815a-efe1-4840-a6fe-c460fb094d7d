package com.aispeech.tablet.feature.meeting.entry

import com.aispeech.tablet.core.res.TabletStringsUtils
import javax.annotation.concurrent.Immutable

@Immutable
enum class RecordModel(val description: String, val hasTrailing: Boolean) {
  Recording(TabletStringsUtils.getString(com.aispeech.tablet.core.res.R.string.recordstate_1739176917221_0), false), Playing(TabletStringsUtils.getString(com.aispeech.tablet.core.res.R.string.recordstate_1739176917221_1), true);
  val isRecording get() = this == Recording
}

