package com.aispeech.tablet.feature.meeting.extensions

import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.drawWithCache
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.graphicsLayer

fun Modifier.drawBorderLine(
  start: <PERSON><PERSON><PERSON> = false,
  end: <PERSON>olean = false,
  top: <PERSON>olean = false,
  bottom: <PERSON>ole<PERSON> = false,
  centerVertical: <PERSON>olean = false,
  centerHorizontal: <PERSON>ole<PERSON> = false,
  color: Color = Color.Black
): Modifier {

  return this then graphicsLayer {}.drawWithCache {
    onDrawBehind {

      val leftTop = Offset(0f, 0f)
      val rightTop = Offset(size.width, 0f)
      val centerLeft = Offset(0f, size.height / 2)
      val centerTop = Offset((rightTop.x - leftTop.x) / 2f, 0f)

      val leftBottom = Offset(0f, size.height)
      val rightBottom = Offset(size.width, size.height)
      val centerRight = Offset(size.width, size.height / 2)
      val centerBottom = Offset((rightBottom.x - leftBottom.x) / 2f, size.height)

      if (start) {
        drawLine(
          color = color,
          start = leftTop,
          end = leftBottom
        )
      }

      if (end) {
        drawLine(
          color = color,
          start = rightTop,
          end = rightBottom
        )
      }

      if (top) {
        drawLine(
          color = color,
          start = leftTop,
          end = rightTop
        )
      }

      if (bottom) {
        drawLine(
          color = color,
          start = leftBottom,
          end = rightBottom
        )
      }

      if (centerHorizontal) {
        drawLine(
          color = color,
          start = centerLeft,
          end = centerRight
        )
      }

      if (centerVertical) {
        drawLine(
          color = color,
          start = centerTop,
          end = centerBottom
        )
      }
    }
  }
}
