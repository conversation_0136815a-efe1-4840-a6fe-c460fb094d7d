package com.aispeech.tablet.feature.meeting.mapper

import com.aispeech.tablet.core.model.entity.audio.AudioRecordInfo
import com.aispeech.tablet.feature.meeting.model.OfflineTaskData

object OfflineTaskMapper {

  fun mapToTaskModel(record: AudioRecordInfo): OfflineTaskData {
    return OfflineTaskData(
      objectId = record.objectId!!,
      language = record.language,
      translateLanguage = record.translateLanguage,
      taskId = record.offlineTask!!,
      isStarted = record.offlineStarted
    )
  }
}