package com.aispeech.tablet.feature.meeting.domain

import android.util.Log
import com.aispeech.aibase.AILog
import com.aispeech.hybridspeech.TranscriptionResult
import com.aispeech.tablet.core.meeting.domain.NoteRecordingDataManager
import com.aispeech.tablet.core.meeting.entity.AsrDataType
import com.aispeech.tablet.core.meeting.entity.ConversationRow2
import com.aispeech.tablet.core.meeting.entity.RuntimeDataItemV2
import com.aispeech.tablet.core.meeting.entity.RuntimeDataMixItem
import com.aispeech.tablet.core.meeting.utils.removeEmphasisTags
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.update
import javax.inject.Inject

/**
 * 混合语音实时转换管理器
 * 参考原 RealtimeTransformManager 的实现，适配新的 TranscriptionResult 结构
 * 处理来自 HybridSpeechService 的转写结果，转换为实时数据流
 */
class HybridRealtimeTransformManager @Inject constructor(
  private val dataManager: NoteRecordingDataManager
) {

  companion object {
    private const val TAG = "HybridRealtimeTransformManager"
    private const val SPEAKER_PREFIX = "说话人"
    private const val DEFAULT_SPEAKER_ID = 1
  }

  // 时间戳偏移量，用于调整 SpeakerInfo 中的时间
  private var _currentTimeWeight = 0

  // 当前正在处理的原文行
  private val _currentLine = MutableStateFlow<RuntimeDataItemV2?>(null)

  // 当前正在处理的翻译行，不一定完全跟 [_currentLine] 匹配
  private val _currentTranslateLine = MutableStateFlow<RuntimeDataItemV2?>(null)

  /**
   * 结合当前原文行和翻译行，生成一个混合数据项 StateFlow。
   * 仅当原文和翻译的时间戳匹配时，才会包含翻译内容。
   */
  val currentLineMix = combine(
    flow = _currentLine,
    flow2 = _currentTranslateLine
  ) { sourceLine, translateLine ->
    sourceLine?.let { source ->
      // 仅当时间戳相同时才认为翻译有效
      val useTranslate = source.ts == translateLine?.ts
      val currentTranslate = if (useTranslate) translateLine else null

      RuntimeDataMixItem(
        id = source.id,
        translateId = currentTranslate?.id,
        ts = source.ts,
        contentId = dataManager.contentId,
        speaker = ConversationRow2.SpeakerInfo(
          name = source.speaker,
          time = _currentTimeWeight + source.ts.toLong(),
          useSpeaker = false,
        ),
        middle = source.middle,
        text = source.text,
        translateList = currentTranslate?.text ?: emptyList(),
        language = source.language,
        translateLanguage = currentTranslate?.language,
        markdownTags = source.markdownTags
      )
    }
  }

  /**
   * 移除当前原文行中最新的中间结果。
   * 用于在收到新的中间结果或最终结果时进行修正。
   */
  private fun removeLatestMiddle() {
    _currentLine.update { current ->
      current?.takeIf { it.middle.isNotEmpty() }?.copy(middle = current.middle.dropLast(1)) ?: current
    }
  }

  /**
   * 追加中间识别结果到当前原文行。
   *
   * @param data 中间结果数据。
   * @param language 当前语言。
   */
  fun incrementDataMiddle(data: TranscriptionResult.IntermediateResult, language: String) {
    removeLatestMiddle()

    val value = data.`var`.removeEmphasisTags()
    if (value.isEmpty()) return

    _currentLine.update { current ->
      current?.copy(middle = current.middle + value)
        ?: dataManager.buildNewTranscription(
          type = AsrDataType.SOURCE,
          middle = listOf(value),
          language = language
        )
    }
  }

  /**
   * 追加最终识别结果（原文或译文）到对应的行。
   * 如果是新行 (lineFeed=true)，则先保存旧行（如果存在），再创建新行。
   *
   * @param data 最终结果数据。
   * @param language 当前语言。
   */
  suspend fun incrementDataText(data: TranscriptionResult.ProcessingTextResult, language: String) {
    val isTranslate = false // 暂时只处理原文，后续可扩展
    val targetFlow = if (isTranslate) _currentTranslateLine else _currentLine

    val cleanedText = data.text.removeEmphasisTags()
    if (cleanedText.isEmpty()) return

    // 仅在处理原文时移除中间结果
    if (!isTranslate) {
      removeLatestMiddle()
    }

    val newItem = ConversationRow2.ContentItem(
      begin = data.begin.toInt(),
      end = data.end.toInt(),
      spoken = false,
      text = cleanedText,
      updated = false
    )
    val speakerName = formatSpeakerName(data.role)

    if (data.lineFeed == 1) {
      // 换行逻辑：保存当前行（如果存在），然后创建并发出新行
      targetFlow.value?.let { current ->
        dataManager.upsert2(current)
      }

      val newParagraph = dataManager.buildNewTranscription(
        type = AsrDataType.SOURCE,
        speaker = speakerName,
        timeStamp = data.begin.toDouble(),
        language = language,
        text = listOf(newItem)
      )
      targetFlow.emit(newParagraph)
    } else {
      // 不换行逻辑：更新当前行或创建新行
      targetFlow.update { current ->
        current?.copy(
          ts = if (current.text.isEmpty()) data.begin.toDouble() else current.ts,
          middle = emptyList(),
          text = current.text + newItem
        ) ?: dataManager.buildNewTranscription(
          type = AsrDataType.SOURCE,
          speaker = speakerName,
          timeStamp = data.begin.toDouble(),
          language = language,
          text = listOf(newItem)
        )
      }
    }
  }

  /**
   * 追加最终文本结果。
   */
  suspend fun incrementDataText(data: TranscriptionResult.FinalTextResult, language: String) {
    val isTranslate = false
    val targetFlow = if (isTranslate) _currentTranslateLine else _currentLine

    val cleanedText = data.text.removeEmphasisTags()
    if (cleanedText.isEmpty()) return

    // 移除中间结果
    if (!isTranslate) {
      removeLatestMiddle()
    }

    val newItem = ConversationRow2.ContentItem(
      begin = data.begin.toInt(),
      end = data.end.toInt(),
      spoken = false,
      text = cleanedText,
      updated = false
    )
    val speakerName = formatSpeakerName(data.role)

    // 换行逻辑：保存当前行（如果存在），然后创建新行
    targetFlow.value?.let { current ->
      dataManager.upsert2(current)
    }

    val newParagraph = dataManager.buildNewTranscription(
      type = AsrDataType.SOURCE,
      speaker = speakerName,
      timeStamp = data.begin.toDouble(),
      language = language,
      text = listOf(newItem)
    )
    targetFlow.emit(newParagraph)
  }

  /**
   * 将当前原文行最后一个中间结果转换为最终文本项
   */
  fun toText(endTime: Int) {
    Log.i(TAG, "toText: $endTime")
    val current = _currentLine.value ?: return
    val lastMiddle = current.middle.lastOrNull()?.takeIf { it.isNotBlank() } ?: return

    val startTime = current.text.lastOrNull()?.end ?: current.ts.toInt()
    val item = ConversationRow2.ContentItem(startTime, endTime, false, lastMiddle, false)

    _currentLine.update {
      it?.copy(
        middle = it.middle.dropLast(1),
        text = it.text + item
      )
    }
  }

  /**
   * 保存当前正在处理的原文行和翻译行，并清空内存状态
   */
  suspend fun saveAndCleanCurrentLine() {
    AILog.d(TAG, "saveAndCleanCurrentLine: Saving current lines...")
    _currentLine.value?.let { current ->
      AILog.i(TAG, "Saving source line: ${current.text.lastOrNull()?.text?.take(20)}...")
      dataManager.upsert2(current)
    }
    _currentTranslateLine.value?.let { translate ->
      AILog.i(TAG, "Saving translate line: ${translate.text.lastOrNull()?.text?.take(20)}...")
      dataManager.upsert2(translate)
    }
    // 清空状态
    clean()
  }

  /**
   * 更新时间戳偏移量
   */
  fun updateTimeWeight(weight: Int) {
    Log.i(TAG, "updateTimeWeight: $weight")
    _currentTimeWeight = weight
  }

  /**
   * 在当前原文行的所有文本项中查找并替换文本
   */
  fun replaceCurrentDataListText(searchText: String, replaceText: String): Int {
    var count = 0
    _currentLine.update { current ->
      current?.copy(
        text = current.text.map { item ->
          val occurrences = item.text.split(searchText).size - 1
          count += occurrences
          if (occurrences > 0) {
            item.copy(text = item.text.replace(searchText, replaceText))
          } else {
            item
          }
        }
      )
    }
    Log.i(TAG, "替换 '$searchText' 为 '$replaceText' 共 $count 次")
    return count
  }

  /**
   * 清空当前处理的原文行和翻译行状态
   */
  suspend fun clean() {
    Log.i(TAG, "清理当前行状态")
    _currentLine.value = null
    _currentTranslateLine.value = null
  }

  /**
   * 获取当前原文行的数据
   */
  fun getCurrentLine(): RuntimeDataItemV2? = _currentLine.value

  /**
   * 更新当前原文行的文本项列表
   */
  fun updateCurrentDataListText(newItems: List<ConversationRow2.ContentItem>, max: Int) {
    Log.d(TAG, "更新当前行文本项，保留时间戳 >= $max 的项")
    _currentLine.update { current ->
      val retainedItems = current?.text?.filter { it.begin >= max } ?: emptyList()
      current?.copy(text = newItems + retainedItems)
    }
  }

  /**
   * 格式化说话人名称
   */
  private fun formatSpeakerName(speakerId: Int?): String {
    return "$SPEAKER_PREFIX${speakerId ?: DEFAULT_SPEAKER_ID}"
  }

}