package com.aispeech.tablet.feature.meeting.domain

import com.aispeech.aibase.AILog
import com.aispeech.hybridspeech.TranscriptionResult
import com.aispeech.tablet.core.model.entity.api.ai.AiRealtimeAgendaResponse
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import javax.inject.Inject

class HybridRecordAgentDataManager @Inject constructor() {

  companion object {
    const val TAG = "HybridRecordAgentDataManager"
  }

  private val _agendaList = MutableStateFlow<List<AiRealtimeAgendaResponse>>(emptyList())
  val allAgendaList = _agendaList.asStateFlow()

  suspend fun initWithAgentList(list: List<AiRealtimeAgendaResponse>) {
    _agendaList.emit(list)
  }

  suspend fun insertAgendaData(data: TranscriptionResult.AgendaResult) {
    val response = data.toAiRealtimeAgendaResponse()
    val oldData = _agendaList.value.toMutableList()
    if (oldData.isEmpty()) {
      _agendaList.emit(oldData + response)
    } else {
      val last = oldData.last()
      if (last.seq == response.seq) { //同一个seq，替换状态
        oldData[oldData.size - 1] = response
        _agendaList.emit(oldData)
      } else {
        _agendaList.emit(oldData + response)
      }
    }
  }

  suspend fun cleanData() {
    AILog.i(TAG, "cleanData: ");
    _agendaList.emit(emptyList())
  }

}

fun TranscriptionResult.AgendaResult.toAiRealtimeAgendaResponse(): AiRealtimeAgendaResponse {
  return AiRealtimeAgendaResponse(
    seq = this.seq,
    timestamp = this.timestamp,
    begin = this.begin.toInt(),
    end = this.end.toInt(),
    text = this.text ?: "",
    status = this.status,
    isLast = this.isLast
  )
}