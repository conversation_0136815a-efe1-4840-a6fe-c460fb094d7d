package com.aispeech.tablet.feature.meeting.utils

import com.aispeech.aibase.AILog
import java.util.concurrent.atomic.AtomicLong

class ThrottledLogger(private val intervalMs: Long = 5000) {
  private val lastLogTime = AtomicLong(0)

  fun log(message: String) {
    val currentTime = System.currentTimeMillis()
    if (currentTime - lastLogTime.get() >= intervalMs) {
      synchronized(this) {
        // 再次检查，以防在等待锁的过程中其他线程已经打印了日志
        if (currentTime - lastLogTime.get() >= intervalMs) {
          AILog.d("ThrottledLogger", message)
          lastLogTime.set(currentTime)
        }
      }
    }
  }
}