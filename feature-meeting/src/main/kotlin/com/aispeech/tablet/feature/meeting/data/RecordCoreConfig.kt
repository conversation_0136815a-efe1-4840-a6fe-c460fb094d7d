package com.aispeech.tablet.feature.meeting.data

import com.aispeech.tablet.feature.meeting.model.LanguageMenuItem
import kotlinx.coroutines.flow.StateFlow

/**
 * 简化的录音启动参数
 * 只保留必要的参数，去掉复杂的转换逻辑
 */
data class RecordStartParams(
  val noteId: String,
  val pageFlow: StateFlow<String>,
  val audioBasePath: String,
  val language: LanguageMenuItem.Content,
  val userId: Long,
  val useOnlineMode: Boolean = true,
  val enableTranslation: Boolean = true,
  val onMaxTimeFinished: () -> Unit = {}
) {

  /**
   * 验证参数完整性
   */
  fun validate(): List<String> {
    val errors = mutableListOf<String>()

    if (noteId.isBlank()) errors.add("noteId 不能为空")
    if (userId <= 0) errors.add("userId 必须大于 0")
    if (audioBasePath.isBlank()) errors.add("audioBasePath 不能为空")

    return errors
  }
}