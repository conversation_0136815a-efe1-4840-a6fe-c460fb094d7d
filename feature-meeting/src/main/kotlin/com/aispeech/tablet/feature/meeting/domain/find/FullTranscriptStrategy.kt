package com.aispeech.tablet.feature.meeting.domain.find

import android.util.Log
import com.aispeech.aibase.AILog
import com.aispeech.tablet.core.db.dao.NoteTranscriptionDao
import com.aispeech.tablet.core.db.use
import com.aispeech.tablet.core.meeting.domain.NoteMeetingDataManager
import com.aispeech.tablet.core.meeting.entity.ConversationItem
import com.aispeech.tablet.core.meeting.entity.ConversationMixRow
import com.aispeech.tablet.feature.meeting.data.MatchPosition
import com.aispeech.tablet.feature.meeting.domain.NoteConfigurationRepository
import com.aispeech.tablet.feature.meeting.utils.SearchUtils.findMatchesInText
import com.aispeech.tablet.feature.meeting.viewmodel.filterBySpeakersAndRecordInfo
import com.aispeech.tablet.lib.markdown.view.MarkdownRenderer
import com.blankj.utilcode.util.Utils
import dagger.hilt.android.scopes.ActivityRetainedScoped
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.withContext
import javax.inject.Inject

@ActivityRetainedScoped
class FullTranscriptStrategy @Inject constructor(
  private val dataManager: NoteMeetingDataManager,
  private val noteConfigurationRepository: NoteConfigurationRepository,
) : FindStrategy {

  private var currentQuery: String = ""
  private var currentNoteId: String = ""

  private var dataSnapshot: List<ConversationItem> = emptyList() // 数据快照
  private var snapshotMatchingIds: List<Long> = emptyList()       // 基于快照的匹配ID
  private val snapshotMatchesCache = mutableMapOf<Long, List<MatchPosition>>() // 基于快照的单项匹配缓存

  override fun getLogPrefix(): String = "[Full]"
  override fun updateDataSourceForTarget(targetLocation: Int) {
    AILog.d(TAG, "${getLogPrefix()} updateDataSourceForTarget: $targetLocation")
  }

  override fun resetDataSource() {
    AILog.d(TAG, "${getLogPrefix()} resetDataSource")
  }

  private suspend fun getFilterDataSnapshot(): List<ConversationItem> {
    val configuration = noteConfigurationRepository.getConfigurationForNote(currentNoteId)

    val selectedSpeakers = configuration?.selectedSpeakers?.toSet() ?: emptySet()
    val selectAllIntent = configuration?.isAllSpeakersSelectedIntent ?: true
    val recordInfoMap = dataManager.recordInfo.value.associateBy { it.foreignId!! }

    return dataManager.targetRows.first().filterBySpeakersAndRecordInfo(
      selectedSpeakers = selectedSpeakers,
      selectAllIntent = selectAllIntent,
      recordInfoMap = recordInfoMap
    )
  }

  override suspend fun search(query: String, noteId: String): SearchResultSummary {
    resetSearchInternal()

    if (query.isBlank()) {
      dataSnapshot = emptyList()
      return SearchResultSummary(0, 0)
    }

    currentQuery = query
    currentNoteId = noteId
    dataSnapshot = getFilterDataSnapshot()
    AILog.i(TAG, "${getLogPrefix()} 数据快照获取完毕，包含 ${dataSnapshot.size} 项。")

    val initialDbMatchingIds = withContext(Dispatchers.IO) {
      NoteTranscriptionDao.use().getTpListsContainingQuery(noteId, query, true).map { it.id }
    }

    Log.d(TAG, "search before $initialDbMatchingIds")

    snapshotMatchingIds = initialDbMatchingIds.filter { id ->
      val itemInSnapshot = dataSnapshot.find { (it as? ConversationMixRow)?.sourceId == id }
      val textContent =
        (itemInSnapshot as? ConversationMixRow)?.let { getItemTextContent(it) } ?: ""
      Log.d(TAG, "textContent: $textContent")
      itemInSnapshot != null && textContent.contains(query, ignoreCase = true)
    }
    AILog.i(TAG, "${getLogPrefix()} 筛选后，快照中实际匹配的 ID 数量: ${snapshotMatchingIds.size}")

    var totalMatches = 0
    snapshotMatchingIds.forEach { id ->
      totalMatches += getItemMatchesInternal(id, query).size
    }
    AILog.i(TAG, "${getLogPrefix()} 基于快照计算总匹配数: $totalMatches")

    return SearchResultSummary(snapshotMatchingIds.size, totalMatches)
  }

  override suspend fun refreshSearchAndUpdateResults(): SearchResultSummary {
    if (currentQuery.isBlank()) {
      AILog.w(TAG, "${getLogPrefix()} refresh: 当前无查询词，无法刷新。")
      resetSearchInternal()
      return SearchResultSummary(0, 0)
    }
    AILog.i(TAG, "${getLogPrefix()} refresh: 使用查询词 '$currentQuery' 刷新结果。")
    return search(currentQuery, currentNoteId)
  }

  override fun resetSearch() {
    resetSearchInternal()
  }

  override fun getResultCount(): Int = snapshotMatchingIds.size

  override suspend fun getResultItemDetails(
    searchResultIndex: Int,
    query: String
  ): SearchResultItemDetails? {
    val itemId = snapshotMatchingIds.getOrNull(searchResultIndex) ?: return null

    val index = dataSnapshot.indexOfFirst {
      (it as? ConversationMixRow)?.sourceId == itemId
    }
    val displayLocation = if (index != -1) index else {
      AILog.w(
        TAG,
        "${getLogPrefix()} 警告: Item ID $itemId 在 snapshotMatchingIds 中，但在 dataSnapshot 中未找到!"
      )
      null
    }

    val matches = getItemMatchesInternal(itemId, query)
    return SearchResultItemDetails(itemId, displayLocation, matches)
  }

  override suspend fun getActivePlayPosition(
    segmentIndex: Int,
    matchPosition: Int
  ): PlaySeekParams? {
    AILog.i(TAG, "getActivePlayPosition: segmentIndex=$segmentIndex, matchPosition=$matchPosition")
    val itemId = snapshotMatchingIds.getOrNull(segmentIndex) ?: return null

    val itemInSnapshot =
      dataSnapshot.find { (it as? ConversationMixRow)?.sourceId == itemId }
    if (itemInSnapshot == null) {
      AILog.w(TAG, "getActivePlayPosition: No matching item found in snapshot for ID: $itemId")
      return null
    }

    val selectRow = (itemInSnapshot as? ConversationMixRow)
    if (selectRow == null) {
      AILog.w(TAG, "getActivePlayPosition: Item is not ConversationMixRow type")
      return null
    }

    val index = findContentItemIndexFromClickPosition(selectRow, matchPosition)
    AILog.i(TAG, "getActivePlayPosition: Found content index: $index")

    if (index == -1) {
      return null
    }

    return PlaySeekParams(
      id = selectRow.contentId,
      begin = selectRow.items[index].begin
    )
  }


  private fun findContentItemIndexFromClickPosition(
    conversationMixRow: ConversationMixRow,
    clickPosition: Int
  ): Int {
    if (clickPosition < 0) return -1

    var offset = 0
    return conversationMixRow.items.indexOfFirst { item ->
      val parseText = MarkdownRenderer.getInstance(Utils.getApp()).toSpannableStringBuilder(item.text)
      if (parseText.isEmpty()) return@indexOfFirst false

      val range = offset until (offset + parseText.length)
      offset += parseText.length
      clickPosition in range
    }
  }

  private fun getItemMatchesInternal(itemId: Long, query: String): List<MatchPosition> {
    return snapshotMatchesCache[itemId] ?: run {
      val itemInSnapshot = dataSnapshot.find { (it as? ConversationMixRow)?.sourceId == itemId }
      val parsedContent = itemInSnapshot?.let { getItemTextContent(it as ConversationMixRow) } ?: ""

      val matches = findMatchesInText(parsedContent, query)
      snapshotMatchesCache[itemId] = matches
      matches
    }
  }

  private fun getItemTextContent(item: ConversationMixRow): String {
    return MarkdownRenderer.getInstance(Utils.getApp()).toSpannableStringBuilder(
      item.items.joinToString("") { it.text }
    ).toString()
  }

  private fun resetSearchInternal() {
    currentQuery = ""
    currentNoteId = ""
    dataSnapshot = emptyList()
    snapshotMatchingIds = emptyList()
    snapshotMatchesCache.clear()
  }


  companion object {
    const val TAG = "FullTranscriptStrategy"
  }
}