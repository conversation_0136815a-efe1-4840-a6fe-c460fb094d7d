package com.aispeech.tablet.feature.meeting.domain.find

import com.aispeech.aibase.AILog
import com.aispeech.tablet.core.db.dao.NoteTranscriptionDao
import com.aispeech.tablet.core.db.use
import com.aispeech.tablet.feature.meeting.data.MatchPosition
import com.aispeech.tablet.feature.meeting.utils.SearchUtils.findMatchesInText
import com.aispeech.tablet.lib.markdown.view.MarkdownRenderer
import com.blankj.utilcode.util.Utils
import dagger.hilt.android.scopes.ActivityRetainedScoped
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.SharedFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.withContext
import javax.inject.Inject

@ActivityRetainedScoped
class PagedTranscriptStrategy @Inject constructor(): FindStrategy {

  private var currentQuery: String = ""
  private var currentNoteId: String = ""

  private var matchingIds: List<Long> = emptyList()
  private val matchesCache = mutableMapOf<Long, List<MatchPosition>>() // 缓存单项匹配结果

  private val _pagingTargetRequests = MutableSharedFlow<Int?>(replay = 0, extraBufferCapacity = 1)
  val pagingTargetRequests: SharedFlow<Int?> = _pagingTargetRequests.asSharedFlow()

  override fun updateDataSourceForTarget(targetLocation: Int) {
    AILog.i(TAG, "${getLogPrefix()} 发出更新 Paging 目标位置请求: $targetLocation")
    _pagingTargetRequests.tryEmit(targetLocation)
  }

  override fun resetDataSource() {
    AILog.i(TAG, "${getLogPrefix()} 发出重置 Paging 目标位置请求")
    _pagingTargetRequests.tryEmit(null)
  }

  override suspend fun search(query: String, noteId: String): SearchResultSummary {
    resetSearchInternal()

    currentQuery = query
    currentNoteId = noteId

    if (query.isBlank()) return SearchResultSummary(0, 0)

    matchingIds = withContext(Dispatchers.IO) {
      NoteTranscriptionDao.use().getTpListsContainingQuery(noteId, query, true).map { it.id }
    }
    AILog.i(TAG, "${getLogPrefix()} 找到 ${matchingIds.size} 个匹配段落 ID")

    var totalMatches = 0
    matchingIds.forEach { id ->
      totalMatches += getItemMatchesInternal(id, query).size // 复用内部方法以利用缓存
    }
    AILog.i(TAG, "${getLogPrefix()} 计算总匹配数: $totalMatches")

    return SearchResultSummary(matchingIds.size, totalMatches)
  }

  override suspend fun refreshSearchAndUpdateResults(): SearchResultSummary {
    if (currentQuery.isBlank()) {
      AILog.w(TAG, "${getLogPrefix()} refresh: 当前无查询词，无法刷新。")
      resetSearchInternal()
      return SearchResultSummary(0, 0)
    }
    AILog.i(TAG, "${getLogPrefix()} refresh: 使用查询词 '$currentQuery' 刷新结果。")
    return search(currentQuery, currentNoteId)
  }

  override fun resetSearch() {
    resetSearchInternal()
  }

  override fun getResultCount(): Int = matchingIds.size

  override suspend fun getResultItemDetails(
    searchResultIndex: Int,
    query: String
  ): SearchResultItemDetails? {
    val itemId = matchingIds.getOrNull(searchResultIndex) ?: return null

    val displayLocation = withContext(Dispatchers.IO) {
      NoteTranscriptionDao.use().getPositionOfItemId(currentNoteId, itemId)
    }
    val matches = getItemMatchesInternal(itemId, currentQuery)
    return SearchResultItemDetails(itemId, displayLocation, matches)
  }

  override suspend fun getActivePlayPosition(
    segmentIndex: Int,
    matchPosition: Int
  ): PlaySeekParams? = null

  /**
   * 重制搜索内容
   */
  private fun resetSearchInternal() {
    AILog.w(TAG, "${getLogPrefix()} resetSearchInternal: $currentQuery")
    currentQuery = ""
    currentNoteId = ""
    matchingIds = emptyList()
    matchesCache.clear()
  }

  private suspend fun getItemMatchesInternal(itemId: Long, query: String): List<MatchPosition> {
    return matchesCache[itemId] ?: run {
      val segment = withContext(Dispatchers.IO) { NoteTranscriptionDao.use().getOne(itemId) }
      val matches = segment?.let {
        val textContent = it.tpList.joinToString("") { tp -> tp.text }
        val parsedContent = MarkdownRenderer.getInstance(Utils.getApp()).toSpannableStringBuilder(textContent).toString()
        findMatchesInText(parsedContent, query)

      } ?: emptyList()
      matchesCache[itemId] = matches // 存入缓存
      matches
    }
  }

  override fun getLogPrefix(): String = "[Paging]"

  companion object {
    const val TAG = "PagedTranscriptStrategy"
  }
}