package com.aispeech.tablet.feature.meeting.utils

import android.util.Log
import androidx.compose.foundation.lazy.LazyListState
import com.aispeech.aibase.AILog
import com.aispeech.tablet.core.model.entity.ai.AINoteItemData
import com.aispeech.tablet.feature.meeting.viewmodel.DataSourceMode
import com.aispeech.tablet.lib.markdown.editor.helper.LineOffsetQuery
import kotlinx.coroutines.CancellationException
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.isActive
import kotlinx.coroutines.launch

/**
 * 高亮区域的可见性状态
 */
enum class HighlightVisibility {
  FULLY_VISIBLE,           // 完全可见
  TOP_CLIPPED,            // 顶部被裁剪
  BOTTOM_CLIPPED,         // 底部被裁剪
  BOTH_CLIPPED,           // 顶部和底部都被裁剪
  NOT_VISIBLE             // 完全不可见
}

/**
 * 滚动策略
 */
private data class ScrollStrategy(
  val targetOffset: Int,           // 目标滚动偏移量
  val reason: String              // 滚动原因（用于调试）
)

class HighlightScrollHelper(
  private val scrollState: LazyListState,
  private val noteItemsProvider: () -> List<AINoteItemData>,
  private val currentSearchPositionProvider: () -> Map<Any, Pair<Int, Int>>?,
  private val paragraphQueryProvider: () -> Map<String, LineOffsetQuery>,
  private val coroutineScope: CoroutineScope,
  private val onScrollTargetConsumed: () -> Unit,
  private val getCurrentMode: () -> DataSourceMode
) {

  companion object {
    private const val AI_LIST_TAG = "HighlightScrollHelper"
    private const val DEFAULT_DESIRED_TOP_SPACING_PX = 50
    private const val QUERY_MAX_ATTEMPTS = 30
    private const val QUERY_RETRY_DELAY_MS = 50L
    private const val INITIAL_SCROLL_DELAY_MS = 200L
    private const val EDGE_THRESHOLD_PX = 80
  }

  private var scrollJob: Job? = null


  /**
   * 等待指定键的 [LineOffsetQuery] 对象准备就绪。
   * 在指定的最大尝试次数内轮询查询对象是否准备好，如果超时则返回 null。
   *
   * @param key 用于查找 [LineOffsetQuery] 的键值。
   * @return 准备就绪的 [LineOffsetQuery] 对象，如果超时或未找到则返回 null。
   */
  private suspend fun waitForQuery(key: String): LineOffsetQuery? {
    repeat(QUERY_MAX_ATTEMPTS) {
      paragraphQueryProvider()[key]?.let { query ->
        if (query.isReady) return query
      }
      delay(QUERY_RETRY_DELAY_MS)
    }
    return null
  }


  /**
   * 滚动到指定的目标项和高亮位置。
   * 如果当前模式不是 [DataSourceMode.AiSummaryView]，则直接调用回调并返回。
   * 滚动过程包括初始滚动和精确调整，确保高亮位置可见。
   *
   * @param targetIndex 目标项的索引。
   */
  fun scrollToTarget(targetIndex: Int) {
    if (getCurrentMode() != DataSourceMode.AiSummaryView) {
      onScrollTargetConsumed()
      return
    }

    scrollJob?.cancel()
    scrollJob = coroutineScope.launch {
      try {
        val noteItems = noteItemsProvider()
        if (targetIndex !in noteItems.indices) return@launch

        val key = noteItems[targetIndex].compositeKey
        val position = currentSearchPositionProvider()?.get(key as Any) ?: return@launch

        val (startOffset, endOffset) = position

        // 1. 检查当前可见性状态
        val visibility = checkHighlightVisibility(targetIndex, startOffset, endOffset, "scroll")
        if (visibility == HighlightVisibility.FULLY_VISIBLE) {
          AILog.d(AI_LIST_TAG, "[Summary] Highlight already fully visible")
          return@launch
        }

        // 2. 滚动到目标项
        scrollState.animateScrollToItem(targetIndex)

      } catch (e: CancellationException) {
        throw e
      } catch (e: Exception) {
        AILog.e(AI_LIST_TAG, "[Summary] Error during scroll to target", e)
      } finally {
        if (coroutineContext.isActive) {
          onScrollTargetConsumed()
        }
      }
    }
  }

  /**
   * 在位置变化时检查并调整可见性。
   * 检查当前可见项中的高亮位置是否需要调整滚动位置以确保可见性。
   * 如果需要调整，则执行动画滚动到合适的位置。
   */
  fun checkAndAdjustVisibilityOnPositionChange() {
    scrollJob?.cancel()
    scrollJob = coroutineScope.launch {
      try {
        delay(100L) // 等待UI更新

        val layoutInfo = scrollState.layoutInfo
        val noteItems = noteItemsProvider()
        val currentSearchPos = currentSearchPositionProvider()

        // 查找需要调整的项目
        for (itemInfo in layoutInfo.visibleItemsInfo) {
          val index = itemInfo.index
          if (index !in noteItems.indices) continue

          val key = noteItems[index].compositeKey
          val position = currentSearchPos?.get(key) ?: continue
          val (startOffset, endOffset) = position

          val visibility = checkHighlightVisibility(index, startOffset, endOffset, "check")

          // 只有在不完全可见时才需要调整
          if (visibility != HighlightVisibility.FULLY_VISIBLE && visibility != null) {
            val strategy = calculateScrollStrategy(index, startOffset, endOffset, visibility)
            if (strategy != null) {
              AILog.d(AI_LIST_TAG, "[Summary] Auto-adjusting visibility: ${strategy.reason}, Target Offset: ${strategy.targetOffset}")
              scrollState.animateScrollToItem(index, scrollOffset = strategy.targetOffset)
              delay(INITIAL_SCROLL_DELAY_MS)
              break
            }
          }
        }
      } catch (e: CancellationException) {
        throw e
      } catch (e: Exception) {
        AILog.e(AI_LIST_TAG, "[Summary] Error during position change check", e)
      }
    }
  }

  /**
   * 检查高亮区域的可见性状态
   */
  private suspend fun checkHighlightVisibility(
    targetIndex: Int,
    startOffset: Int,
    endOffset: Int,
    suffix: String = ""
  ): HighlightVisibility? {
    val layoutInfo = scrollState.layoutInfo
    val targetItemInfo = layoutInfo.visibleItemsInfo.find { it.index == targetIndex }
    if (targetItemInfo == null) {
      Log.d(AI_LIST_TAG, "$suffix targetInfo is null")
      return null
    }

    val noteItems = noteItemsProvider()
    if (targetIndex !in noteItems.indices) {
      Log.d(AI_LIST_TAG, "$suffix targetIndex not Found")
      return null
    }

    val key = noteItems[targetIndex].compositeKey
    val query = waitForQuery(key)
    if (query == null) {
      Log.d(AI_LIST_TAG, "$suffix query is null")
      return null
    }
    val bounds = query.getHighlightBounds(startOffset, endOffset) ?: return null

    val highlightTopAbsolute = targetItemInfo.offset + bounds.topY
    val highlightBottomAbsolute = targetItemInfo.offset + bounds.bottomY
    val viewportHeight = layoutInfo.viewportEndOffset - layoutInfo.viewportStartOffset

    return when {
      highlightTopAbsolute >= 0 && highlightBottomAbsolute <= viewportHeight ->
        HighlightVisibility.FULLY_VISIBLE
      highlightTopAbsolute < 0 && highlightBottomAbsolute > viewportHeight ->
        HighlightVisibility.BOTH_CLIPPED
      highlightTopAbsolute < 0 && highlightBottomAbsolute <= viewportHeight ->
        HighlightVisibility.TOP_CLIPPED
      highlightTopAbsolute >= 0 && highlightBottomAbsolute > viewportHeight ->
        HighlightVisibility.BOTTOM_CLIPPED
      else ->
        HighlightVisibility.NOT_VISIBLE
    }
  }


  /**
   * 根据高亮区域的可见性状态计算最佳滚动策略
   */
  private suspend fun calculateScrollStrategy(
    targetIndex: Int,
    startOffset: Int,
    endOffset: Int,
    visibility: HighlightVisibility
  ): ScrollStrategy? {
    val layoutInfo = scrollState.layoutInfo

    val noteItems = noteItemsProvider()
    if (targetIndex !in noteItems.indices) return null

    val key = noteItems[targetIndex].compositeKey
    val query = waitForQuery(key) ?: return null
    val bounds = query.getHighlightBounds(startOffset, endOffset) ?: return null

    val viewportHeight = layoutInfo.viewportEndOffset - layoutInfo.viewportStartOffset
    val safeTopMargin = DEFAULT_DESIRED_TOP_SPACING_PX
    val safeBottomMargin = EDGE_THRESHOLD_PX

    return when (visibility) {
      HighlightVisibility.FULLY_VISIBLE -> null // 不需要滚动

      HighlightVisibility.TOP_CLIPPED -> {
        val targetOffset = bounds.topY - safeTopMargin
        ScrollStrategy(
          targetOffset = targetOffset.coerceAtLeast(0),
          reason = "Top clipped - bringing top to safe margin ($safeTopMargin px)"
        )
      }

      HighlightVisibility.BOTTOM_CLIPPED -> {
        // 底部被裁剪，优先考虑是否能通过小幅调整显示完整内容
        val availableSpace = viewportHeight - safeTopMargin - safeBottomMargin

        if (bounds.height <= availableSpace) {
          val targetOffset = bounds.bottomY - (viewportHeight - safeBottomMargin)
          ScrollStrategy(
            targetOffset = targetOffset.coerceAtLeast(0),
            reason = "Bottom clipped - bringing bottom to safe margin (${viewportHeight - safeBottomMargin} px)"
          )
        } else {
          val targetOffset = bounds.topY - safeTopMargin
          ScrollStrategy(
            targetOffset = targetOffset.coerceAtLeast(0),
            reason = "Bottom clipped but content too tall (${bounds.height}px > ${availableSpace}px) - showing top"
          )
        }
      }

      HighlightVisibility.BOTH_CLIPPED -> {
        val targetOffset = bounds.centerY - (viewportHeight / 2)
        ScrollStrategy(
          targetOffset = targetOffset.coerceAtLeast(0),
          reason = "Both ends clipped - centering content at ${viewportHeight / 2} px"
        )
      }

      HighlightVisibility.NOT_VISIBLE -> {
        val targetOffset = bounds.topY - safeTopMargin
        ScrollStrategy(
          targetOffset = targetOffset.coerceAtLeast(0),
          reason = "Not visible - showing top at safe margin ($safeTopMargin px)"
        )
      }
    }
  }



  /**
   * 取消正在进行的滚动操作。
   * 如果有正在执行的滚动任务，则取消该任务。
   */
  fun cancelOngoingScroll() {
    scrollJob?.cancel()
  }
}
