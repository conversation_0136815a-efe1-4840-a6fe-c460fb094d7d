package com.aispeech.tablet.feature.meeting.model

import com.aispeech.tablet.core.res.TabletStringsUtils

data class AudioFileData(
  val checked: Boolean = true,
  val progress: Float = 0f,
  val isPlaying: Boolean = false,
  val duration: Int,
  val fileSize: Long,
  val filePath: String,
  val objectId: String,
  val fileSizeInMb: String?,
  val name: String = TabletStringsUtils.getString(com.aispeech.tablet.core.res.R.string.audiofiledata_1739176974424_0),
  val audioDeleted: <PERSON><PERSON><PERSON>,
)