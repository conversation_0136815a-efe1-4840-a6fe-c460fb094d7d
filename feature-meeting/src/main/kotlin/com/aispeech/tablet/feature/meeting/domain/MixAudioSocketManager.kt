package com.aispeech.tablet.feature.meeting.domain

import com.aispeech.aibase.AILog
import com.aispeech.aimeeting.api.MeetingConfig
import com.aispeech.aimeeting.api.entities.AudioConfig
import com.aispeech.aimeeting.api.file.CacheFileManager
import com.aispeech.aimeeting.api.sockets.AudioAsrSocketManager
import com.aispeech.aimeeting.api.sockets.AudioAsrV2SocketManager
import com.aispeech.aimeeting.api.sockets.WebSocketState
import com.aispeech.aimeeting.data.entity.RecordVoiceInfo
import com.aispeech.aimeeting.data.repository.RecordRepository
import com.aispeech.aimeeting.extensions.takeOrTimeoutEq
import com.aispeech.lib_ktx.onFailure
import com.aispeech.lib_ktx.onSuccess
import com.aispeech.tablet.core.meeting.data.RealtimeTransformManager
import com.aispeech.tablet.core.meeting.data.RecordAgentDataManager
import com.aispeech.tablet.core.meeting.data.repository.NoteTranscriptionRepository
import com.aispeech.tablet.core.meeting.domain.NoteRecordingDataManager
import com.aispeech.tablet.core.meeting.entity.AsrItemDataV3
import com.aispeech.tablet.core.meeting.entity.ConversationRow2
import com.aispeech.tablet.core.meeting.entity.RuntimeDataMixItem
import com.aispeech.tablet.core.model.entity.TranscriptionMarkdownTag
import com.aispeech.tablet.core.model.entity.audio.CreateRecordResponse
import com.aispeech.tablet.core.network.entity.ResultX
import com.aispeech.tablet.feature.ai.domain.NoteAudioAiTaskManager
import com.aispeech.tablet.feature.ai.viewmodel.NoteAgentAiManager
import com.aispeech.tablet.feature.meeting.model.LanguageMenuItem
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.cancel
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.filterNotNull
import kotlinx.coroutines.launch
import java.util.UUID
import java.util.concurrent.ConcurrentLinkedQueue
import java.util.concurrent.TimeoutException
import java.util.concurrent.atomic.AtomicInteger
import javax.inject.Inject
import kotlin.coroutines.cancellation.CancellationException
import kotlin.math.abs
import kotlin.system.measureTimeMillis

class MixAudioSocketManager @Inject constructor(
  private val audioConfig: AudioConfig = AudioConfig(),
  private val recordRepository: RecordRepository,
  private val agentAiManager: NoteAgentAiManager,
  private val dataManager: NoteRecordingDataManager,
  private val socketV2Manager: AudioAsrV2SocketManager,
  private val realtimeTransformManager: RealtimeTransformManager,
  private val recordAgentDataManager: RecordAgentDataManager,
  private val transcriptionRepository: NoteTranscriptionRepository,
  private val aiTaskManager: NoteAudioAiTaskManager
) {

  // dispatcher
  private var shareJob: Job = Job()
  private var scope = CoroutineScope(Dispatchers.IO + shareJob)

  //job
  private var msgJob: Job? = null
  private var stateJob: Job? = null
  private var socketJob: Job? = null
  private var retryDelayJob: Job? = null

  // state
  @Volatile
  private var isReady = false
  private var ignoreBefore: Boolean = false

  @Volatile
  private var stopped = false

  // retry
  private val retryAttemptCount = AtomicInteger(0)

  // cache
  private var cacheId: Long? = null
  private var audioType: String? = null
  private var recordDuration: Long = 0L
  private var cacheFileManager: CacheFileManager? = null
  private var latestLanguage: LanguageMenuItem.Content? = null
  private var realtimeAgenda = 0
  private var onCacheBuffer: ((buffer: ByteArray)->Unit)? = null
  private var dataQueue = ConcurrentLinkedQueue<ByteArray>()

  var noteId: String? = null

  // foreign
  val stateFlow = socketV2Manager.stateFlow()
  val currentLine = realtimeTransformManager.currentLineMix
  val pagingItemFlow = transcriptionRepository.getTranscriptionPairStream()
  fun pagingItemFlowV2(cacheScope: CoroutineScope, targetKey: Int? = null) =
    transcriptionRepository.getTranscriptionPairStreamV2(cacheScope = cacheScope, targetKey = targetKey)

  val realtimeAgendaItems = recordAgentDataManager.allAgendaList

  val socketStateFlow = socketV2Manager.socketQualityFlow

  // socket stop flag
  val socketStopped get() = stopped

  suspend fun stopRecording() {
    transcriptionRepository.stopRecording()
    realtimeTransformManager.updateTimeWeight(transcriptionRepository.currentTimeWeight)
  }

  /**
   * 更新 id
   */
  suspend fun updateNoteId(noteId: String) {
    this.noteId = noteId
    transcriptionRepository.setNoteId(noteId)
    realtimeTransformManager.updateTimeWeight(transcriptionRepository.currentTimeWeight)
  }

  /**
   * 绑定状态的监听变化
   */
  private fun bindStateJob() {
    stateJob?.cancel()
    stateJob = scope.launch {
      socketV2Manager.stateFlow().collect { state ->
        when (state) {
          is WebSocketState.DISCONNECTED -> {
            AILog.w(TAG, "WebSocket disconnected: ${state.value}, code: ${state.code}.")
            if (isReady) {
              AILog.i(TAG, "Setting isReady to false due to disconnection.")
              isReady = false
            }
            if (!stopped) {
              // 取消可能正在进行的连接尝试或上一个重试延迟
              cancelCurrentConnectionAttempts("Disconnected state received")
              // 调度重试
              scheduleRetryOrGiveUp("Disconnected state received")
            } else {
              AILog.i(TAG, "Ignoring disconnection event as manager is stopped.")
            }
          }

          else -> Unit
        }
      }
    }
  }

  fun isReady() = isReady

  /**
   * 绑定 OT 数据
   */
  private fun bindOtMessage() {
    msgJob?.cancel()
    msgJob = scope.launch {
      val language = latestLanguage?.source?.value ?: "cn"

      socketV2Manager.messageFlow()
        .filterNotNull()
        .collect { message ->
          when (val data = message.data) {
            is AsrItemDataV3.AsrMiddleItemDataV3 -> {
              realtimeTransformManager.incrementDataMiddle(data, language)
            }

            is AsrItemDataV3.AsrTextItemDataV3 -> {
              realtimeTransformManager.incrementDataText(data, language)
              transcriptionRepository.notifyNewDataAvailable()
            }

            is AsrItemDataV3.AsrTextItems -> {
              noteId?.let { id ->
                realtimeTransformManager.initWithTextData(id, data, language)
              }
            }

            is AsrItemDataV3.AsrAgendaItemData -> {
              recordAgentDataManager.insertAgendaData(data)
            }

            else -> AILog.d(TAG, "Unhandled message type: $data")
          }

          if (message.data != null) {
            dataManager.saveRecordUpdate()
          }
        }
    }
  }

  /**
   * 计算音频时长
   * [cacheCount] 已经收到的数据个数
   */
  private fun calcAudioDuration(recordVoiceInfo: RecordVoiceInfo?): Long {
    var duration: Long = 0
    if (MeetingConfig.isPcm() || MeetingConfig.isMp3()) {
      val cacheCount = if (ignoreBefore) {
        getLocalCacheCount()
      } else if (recordVoiceInfo == null) 0 else recordVoiceInfo?.sendAllSuccessCount

      duration = cacheCount!! * audioConfig.bufferDuration
    } else {
      duration = if (ignoreBefore) {
        val cacheCount = getLocalCacheCount()
        val localDuration = cacheCount!! * audioConfig.bufferDuration
        localDuration
      } else {
        //ogg有页的概念，有些是有头，有些纯数据，无法根据数量来计算出来时长
        if (recordVoiceInfo == null) 0 else recordVoiceInfo?.sendAllSuccessDuration ?: 0
      }
    }
    AILog.i(TAG, "calcAudioDuration, duration = $duration, ignoreBefore = $ignoreBefore, info = $recordVoiceInfo")
    return duration
  }

  private fun getLocalCacheCount(): Long {
    return cacheFileManager?.getCacheCount()?.toLong() ?: 0L
  }

  /**
   * 获取服务端时长
   */
  private suspend fun getServiceReceiveData(response: CreateRecordResponse, userId: Long): RecordVoiceInfo? {
    try {
      val voiceInfo = recordRepository.recordVoiceInfo(response.recordId, userId)
      if (voiceInfo is ResultX.Success) {
        return voiceInfo.data!!
      }
    } catch (e: Exception) {
      AILog.i(TAG, "getServiceReceiveCount-exception: $e")
      return null
    }
    return null
  }

  /**
   * 根据发送文件缓存数据
   */
  private suspend fun sendFileCacheData(count: Long, duration: Long) {
    AILog.i(TAG, "sendFileCacheData s-index: $count, s-duration: $duration")
    val executionTime = measureTimeMillis {
      //pcm | mp3 能根据index计算出来offset, ogg需要根据duration来计算pcm offset
      if (MeetingConfig.isPcm() || MeetingConfig.isMp3()) {
        cacheFileManager?.readByOffsetIndex(count)?.collect {
          //断点续传的缓存数据，拿到外部取去处理，是encode还是send
          onCacheBuffer?.invoke(it)
        }
      } else {
        //ogg需要根据duration来计算pcm offset
        cacheFileManager?.readByDuration(duration)?.collect {
          //断点续传的缓存数据，拿到外部去处理，是encode还是send
          onCacheBuffer?.invoke(it)
        }
      }
    }
    AILog.i(TAG, "sendFileCacheData-time: $executionTime ")
  }

  private fun updateRecordResponse(recordResponse: CreateRecordResponse) {
    recordRepository.updateRecordResponse(recordResponse)
  }

  fun updateRecordDuration(duration: Long) {
    recordDuration = duration
  }

  fun setCacheFileManager(fileManager: CacheFileManager) {
    this.cacheFileManager = fileManager
  }

  fun prepare() {
    socketV2Manager.prepare()
  }

  /**
   * 创建链接
   */
  private fun createSocket(
    userId: Long,
    firstStart: Boolean,
    recordResponse: CreateRecordResponse,
  ) {

    AILog.i(TAG, "createSocket-stopped: $stopped, firstStart: $firstStart")
    if (stopped) return
    printCount = 0
    dataQueue.clear()
    socketJob?.cancel()
    socketJob = scope.launch {
      if (isReady) {
        AILog.i(TAG, "Setting isReady to false at the beginning of createSocket.")
        isReady = false
      }

      runCatching {

        val recvVoiceInfo = if (firstStart) null else getServiceReceiveData(recordResponse, userId)
          ?: throw Exception("getServiceReceiveCount is null")

        val (recordId, objectId) = recordResponse
        val audioDuration = calcAudioDuration(recvVoiceInfo)
        val language = latestLanguage!!.source.value
        val translate = latestLanguage?.getTarget()

        AILog.i(
          TAG,
          "createSocket-cloud-voice-info: $recvVoiceInfo, duration: $audioDuration, audioType:$audioType, firstStart = $firstStart, realtimeAgenda:$realtimeAgenda"
        )

        // 建立 websocket
        socketV2Manager.connectWith(
          userId = userId,
          recordId = recordId,
          objectId = objectId,
          duration = audioDuration,
          language = language,
          translate = translate,
          audioType = audioType,
          realtimeAgenda = realtimeAgenda,
        )

        // 等待业务状态准备好
        socketV2Manager.stateFlow()
          .takeOrTimeoutEq(state = WebSocketState.CONNECTED, timeoutMillis = CONNECT_TIMEOUT_MS)
          ?: throw TimeoutException("超时未返回")

        AILog.i(TAG, "WebSocket CONNECTED state received. Proceeding with post-connection setup.")

        retryAttemptCount.set(0) // 连接成功，重置计数器
        bindOtMessage()
        bindStateJob()

        // 获取 Agent
        agentAiManager.start(recordId, 0).onSuccess {
          it?.let { recordAgentDataManager.initWithAgentList(it) }
        }.onFailure {
          AILog.i(TAG, "createSocket-getAgent: $it")
        }

        // 发送缓存数据
        if (!ignoreBefore) {
          sendFileCacheData(recvVoiceInfo?.sendAllSuccessCount ?: 0, recvVoiceInfo?.sendAllSuccessDuration ?: 0)
        }

        isReady = true

      }.onFailure { exception ->
        isReady = false
        if (exception is CancellationException) {
          AILog.i(TAG, "createSocket cancelled: ${exception.message}")
          // Job 被取消，不需要手动调度重试，除非是特定类型的取消需要处理
        } else {
          AILog.e(TAG, "createSocket attempt failed: $exception")
          // 连接失败，调度下一次重试或放弃
          scheduleRetryOrGiveUp("createSocket failure: ${exception.message}")
        }
      }
    }
  }

  /**
   * 检查重试次数，如果未达上限则调度延迟重试，否则放弃。
   */
  private fun scheduleRetryOrGiveUp(reason: String) {
    // 取消上一个可能存在的延迟任务
    retryDelayJob?.cancel("Scheduling new retry or giving up")

    val currentAttempt = retryAttemptCount.get() // 读取当前尝试次数

    if (currentAttempt < MAX_RETRY_ATTEMPTS) {
      val nextAttempt = retryAttemptCount.incrementAndGet()
      if (nextAttempt > MAX_RETRY_ATTEMPTS) {
        AILog.w(TAG, "Max retry attempts reached concurrently. Giving up.")
        retryAttemptCount.set(0) // 重置计数器
        return
      }

      AILog.i(TAG, "Scheduling retry attempt $nextAttempt/$MAX_RETRY_ATTEMPTS due to: $reason. Delaying ${SOCKET_INTERVAL_TIME}ms.")
      retryDelayJob = scope.launch { // 启动延迟任务
        try {
          delay(SOCKET_INTERVAL_TIME)
          // 延迟后检查是否已停止或已就绪
          if (stopped || isReady) {
            AILog.i(TAG, "Retry attempt $nextAttempt aborted after delay, state changed (stopped=$stopped, isReady=$isReady).")
            return@launch
          }
          AILog.i(TAG, "Executing retry attempt $nextAttempt.")

          val userId = cacheId
          val response = recordRepository.getRecordResponse()
          if (userId == null || response == null) {
            AILog.e(TAG, "Cannot retry, missing userId or recordResponse.")
            retryAttemptCount.set(0) // Reset if retry cannot proceed
            return@launch
          }
          createSocket(userId, false, response)
        } catch (e: CancellationException) {
          AILog.i(TAG, "Retry delay cancelled.")
        }
      }
    } else {
      AILog.e(TAG, "Max retry attempts ($MAX_RETRY_ATTEMPTS) reached. Giving up. Last reason: $reason")
      retryAttemptCount.set(0)
    }
  }

  /**
   * 取消当前正在进行的连接尝试 (socketJob) 和重试延迟 (retryDelayJob)。
   */
  private fun cancelCurrentConnectionAttempts(reason: String) {
    val cause = CancellationException(reason)
    // 取消可能正在进行的连接和缓存发送
    if (socketJob?.isActive == true) {
      AILog.i(TAG, "Cancelling active socketJob: $reason")
      socketJob?.cancel(cause)
    }
    // 取消可能正在等待的重试延迟
    if (retryDelayJob?.isActive == true) {
      AILog.i(TAG, "Cancelling active retryDelayJob: $reason")
      retryDelayJob?.cancel(cause)
    }
  }

  fun updateIgnoreBefore() {
    ignoreBefore = true
    AILog.i(TAG, "updateIgnoreBefore")
  }

  private var printCount = 0
  fun send(byteArray: ByteArray, ignoreUpdateQuality: Boolean = false) {
    if (printCount++ % 50 == 0) {
      AILog.i(TAG, "send: ${byteArray.size}, ready: $isReady, opus: ${MeetingConfig.isOggOpus()}, queue:${dataQueue.size}")
    }

    if (isReady.not()) {
      if (MeetingConfig.isOggOpus()) { //ogg opus不能丢帧，这里未ready的情况下不能直接忽略
        dataQueue.add(byteArray)
      }
      return
    }

    if (dataQueue.isNotEmpty()) {
      val iterator = dataQueue.iterator()
      while (iterator.hasNext()) {
        val buff = iterator.next()
        iterator.remove()
        socketV2Manager.send(buff, ignoreUpdateQuality)
      }
    }
    socketV2Manager.send(byteArray, ignoreUpdateQuality)
  }

  fun connect(firstStart: Boolean = false) {
    if (stopped) {
      AILog.w(TAG, "Connect called but manager is stopped.")
      return
    }
    if (isReady || socketJob?.isActive == true) {
      AILog.i(TAG, "Connect call ignored, already ready or connection attempt in progress.")
      return
    }

    AILog.i(TAG, "Manual connect called with firstStart=$firstStart. Resetting retry count.")
    retryDelayJob?.cancel("Manual connect initiated")
    retryAttemptCount.set(0)

    val userId = cacheId
    val response = recordRepository.getRecordResponse()
    if (userId == null || response == null) {
      AILog.e(TAG, "Cannot connect, missing userId or recordResponse.")
      return
    }
    createSocket(userId, firstStart, response)
  }

  /**
   * 开始
   * @param onCacheBuffer 缓存数据回调，断点续传的音频
   */
  fun start(
    language: LanguageMenuItem.Content,
    userId: Long,
    audioType: String,
    realtimeAgenda: Int,
    onCacheBuffer: (buffer: ByteArray) -> Unit,
  ): CreateRecordResponse {

    stopped = false
    isReady = false

    cancelWithInitJob() // 清理 job 和基础状态
    retryAttemptCount.set(0) // 重置重试计数

    this.onCacheBuffer = onCacheBuffer
    ignoreBefore = false
    latestLanguage = language
    this.audioType = audioType
    this.realtimeAgenda = realtimeAgenda
    cacheId = userId
    dataQueue.clear()

    val recordId = System.currentTimeMillis()
    val objectId = "obj_${UUID.randomUUID()}"

    val response = CreateRecordResponse(recordId, objectId)
    updateRecordResponse(response)

    return response
  }

  /**
   * 准备历史数据
   */
  suspend fun prepareHistoryData() {
    realtimeTransformManager.clean()
    recordAgentDataManager.cleanData()
    updateHistoryData()
    transcriptionRepository.startRecording()
  }

  /**
   * 更新历史数据
   */
  suspend fun updateHistoryData() {
//    val allRecords = dataManager.getAvailableRecords()
//    val items = dataManager.getOtherContentList(allRecords)
//    asrMessageTransformManager.loadOtherContentData(items, allRecords)
    transcriptionRepository.notifyNewDataAvailable()
  }

  /**
   * 更新历史数据
   */
  fun replaceCurrentDataListText(searchText: String, replaceText: String): Int {
    val count = realtimeTransformManager.replaceCurrentDataListText(searchText, replaceText)
    transcriptionRepository.notifyNewDataAvailable()
    return count
  }

  suspend fun disconnect(
    focus: Boolean,
    manual: Boolean = false,
    timeout: Long = DISCONNECT_TIMEOUT_MS
  ): WebSocketState {

    AILog.i(TAG, "Disconnect requested. focus: $focus, manual: $manual, timeout:$timeout")

    if (isReady) {
      AILog.i(TAG, "Setting isReady to false due to disconnect request.")
      isReady = false
    }

    // 取消所有正在进行的连接/重试任务
    cancelCurrentConnectionAttempts("Disconnect requested")
    retryAttemptCount.set(0)
    stateJob?.cancel("Disconnect requested")

    // 优先结束掉 state 的状态变化，后续 disconnect 会重新出发重连的逻辑
    val recordEnd: Int = recordDuration.toDouble().toInt()
    AILog.i(
      TAG,
      "disconnect prepare. focus: $focus, manual: $manual, timeout:$timeout, recordEnd: $recordEnd"
    )

    var disconnectResult = socketV2Manager.disconnect(focus, timeout = timeout)
    socketJob?.cancel()

    //简单修正需不需要重新转写
    if (disconnectResult.code != AudioAsrSocketManager.SOCKET_DISCONNECTED_CODE9) {
      val lastData = realtimeTransformManager.getCurrentLine()
      val lastEnd: Int = (lastData?.text?.lastOrNull()?.end ?: -1).toInt()
      val diff = abs(recordEnd - lastEnd)
      AILog.i(
        TAG,
        "try fix disconnect result. diff: $recordEnd - $lastEnd = $diff, lastData = $lastData"
      )
      if (recordEnd < 60000 || (lastEnd != -1 && diff <= 10000)) { //低于60秒的音频，不支持重新转写（意义不大）；超过60秒的，如果绝对差<=10秒，说明没有丢什么文本，也认为可以不需要重新转写
        disconnectResult =
          WebSocketState.DISCONNECTED(AudioAsrSocketManager.SOCKET_DISCONNECTED_CODE9)
      }
    }

    // 手动结束的话，处理一下数据
    if (manual) {
      realtimeTransformManager.toText(recordEnd)
    }

    return disconnectResult
  }

  /**
   * 最终结束调用
   * disconnect 可能会调用多次，防止最后一段插入多次
   * 目的是为了插入最后一段话
   */
  suspend fun release() {
    AILog.i(TAG, "Releasing MixAudioSocketManager resources.")
    if (stopped) {
      AILog.w(TAG, "Release called but already stopped.")
      return
    }
    stopped = true

    cancelWithInitJob()
    retryAttemptCount.set(0)
    isReady = false

    // clean state
    recordDuration = 0L
    cacheId = null
    noteId = null
    latestLanguage = null
    audioType = null
    onCacheBuffer = null
    dataQueue.clear()

    runCatching {
      realtimeTransformManager.saveAndCleanCurrentLine()
      recordAgentDataManager.cleanData()
    }.onFailure {
      AILog.e(TAG, "Error during data manager cleanup in release: ${it.message}")
    }

    AILog.i(TAG, "released.")
  }

  /**
   * 取消并且重新初始化 job
   */
  private fun cancelWithInitJob() {
    AILog.i(TAG, "Cancelling all jobs. Clear state")

    val cause = CancellationException("Parent job cancelled by cancelWithInitJob")
    shareJob.cancel(cause) // 会取消所有子 Coroutine (msgJob, stateJob, socketJob)

    // 创建新的 Job 和 Scope
    shareJob = Job()
    scope = CoroutineScope(Dispatchers.IO + shareJob)

    // 重置 Job 引用
    msgJob = null
    stateJob = null
    socketJob = null
    retryDelayJob = null
    dataQueue.clear()
  }

  suspend fun markdownContentUpdate(
    index: Int,
    tagList: List<TranscriptionMarkdownTag>
  ) = {}

  suspend fun editSpeaker(
    id: Long,
    speaker: String,
    newSpeaker: String,
    syncAll: Boolean,
  ): Int {
    return transcriptionRepository.replaceSpeakerName(
      id = id,
      speaker = speaker,
      newSpeaker = newSpeaker,
      syncAll = syncAll
    ).also {
      transcriptionRepository.notifyNewDataAvailable()
    }
  }

  @Deprecated("过期，不在使用")
  suspend fun editSelection(
    replaceText: String,
    replaceAll: Boolean,
    item: RuntimeDataMixItem? = null,
    matchText: String? = null,

  ): Int {

    if (noteId == null || matchText == null) {
      return 0
    }

    if (replaceAll) {
//      return aiTaskManager.replaceAllTranscriptionContent(
//        noteId = noteId!!,
//        replaceText = replaceText,
//        searchText = matchText
//      )
    }

    if (item == null) {
      return 0
    }

    return when (item.id) {
      0L -> {
        realtimeTransformManager.replaceCurrentDataListText(
          matchText,
          replaceText
        )
        0
      }

      else -> transcriptionRepository.replaceCurrentDataListText(
        id = item.id,
        searchText = matchText,
        replaceText = replaceText
      ).also {
        transcriptionRepository.notifyNewDataAvailable()
      }
    }
  }

  suspend fun onAiTextTestClick() {
    recordAgentDataManager.onAiTextTestClick()
  }

  fun updateCurrentDataListText(
    newItems: List<ConversationRow2.ContentItem>,
    max: Int
  ) {
    realtimeTransformManager.updateCurrentDataListText(newItems, max)
  }

  fun notifyNewDataAvailable() {
    transcriptionRepository.notifyNewDataAvailable()
  }

  companion object {
    private const val TAG = "MixAudioSocketManager"
    const val SOCKET_INTERVAL_TIME = 10_000L // 固定重试间隔 10 秒
    const val MAX_RETRY_ATTEMPTS = 30 // 最大重试次数
    private const val CONNECT_TIMEOUT_MS = 5000L
    private const val DISCONNECT_TIMEOUT_MS = 1500L
  }
}