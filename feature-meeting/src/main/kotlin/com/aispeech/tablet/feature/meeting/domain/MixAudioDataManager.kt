package com.aispeech.tablet.feature.meeting.domain

import com.aispeech.aimeeting.api.audio.processors.AudioDurationProcessor
import com.aispeech.aimeeting.api.audio.processors.AudioIntensityProcessor
import com.aispeech.aimeeting.api.entities.AudioConfig
import com.aispeech.aimeeting.data.repository.impl.AudioRepositoryImpl
import javax.inject.Inject

class MixAudioDataManager @Inject constructor(
  private val audioConfig: AudioConfig,
  private val durationProcessor: AudioDurationProcessor,
  private val intensityProcessor: AudioIntensityProcessor,
) {

  companion object {
    const val TAG = "MixAudioDataManager"
  }

  private val audioRepository by lazy {
    AudioRepositoryImpl(
      audioConfig, processors = listOf(
        durationProcessor,
//        intensityProcessor
      )
    )
  }

  fun audioType() = audioRepository.typeExtension()


  fun start() = audioRepository.start()

  fun pause() {
    audioRepository.pause()
  }

  fun resume() {
    audioRepository.resume()
  }
  fun stop(finish: () -> Unit = {}) {
    audioRepository.stop(finish)
    durationProcessor.reset()
    intensityProcessor.reset()
  }

  fun getDuration() = durationProcessor.getDuration()
  fun getStateFlow() = audioRepository.getStateFlow()
  fun getMp3DataFlow() = audioRepository.getMp3DataFlow()
  fun getPCMDataFlow() = audioRepository.getPCMDataFlow()
  fun getDurationFlow() = durationProcessor.getDurationFlow()
  fun getIntensityFlow() = intensityProcessor.getIntensityFlow()
}