package com.aispeech.tablet.feature.meeting.viewmodel

import android.util.Log
import androidx.lifecycle.Observer
import androidx.lifecycle.SavedStateHandle
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import androidx.paging.PagingData
import androidx.paging.cachedIn
import com.aispeech.aibase.AILog
import com.aispeech.lib_ktx.FlowKtxScope
import com.aispeech.lib_ktx.emit
import com.aispeech.lib_ktx.stateFlowOf
import com.aispeech.tablet.core.common.Tablet
import com.aispeech.tablet.core.common.UserConfig
import com.aispeech.tablet.core.common.showShortToast
import com.aispeech.tablet.core.meeting.data.mapper.RuntimeDataItemV2Mapper.toRuntimeData
import com.aispeech.tablet.core.meeting.data.models.MeetingEvent
import com.aispeech.tablet.core.meeting.data.models.RecordTab2
import com.aispeech.tablet.core.meeting.data.repository.MeetingShareEventRepository
import com.aispeech.tablet.core.meeting.data.repository.RecordPreferenceRepository
import com.aispeech.tablet.core.meeting.data.repository.TranscriptionReplaceInfoRepository
import com.aispeech.tablet.core.meeting.domain.MarkdownParseUseCase
import com.aispeech.tablet.core.meeting.domain.NoteMeetingDataManager
import com.aispeech.tablet.core.meeting.domain.NoteRecordingDataManager
import com.aispeech.tablet.core.meeting.domain.TranscriptionEditLogUseCase
import com.aispeech.tablet.core.meeting.domain.UpdateTranscriptionRevisionUseCase
import com.aispeech.tablet.core.meeting.entity.ConversationDivider
import com.aispeech.tablet.core.meeting.entity.ConversationItem
import com.aispeech.tablet.core.meeting.entity.ConversationMixRow
import com.aispeech.tablet.core.meeting.entity.ConversationRow2
import com.aispeech.tablet.core.meeting.entity.RecordingMarkResult
import com.aispeech.tablet.core.meeting.entity.RuntimeBaseData
import com.aispeech.tablet.core.meeting.entity.RuntimeDataMixItem
import com.aispeech.tablet.core.meeting.model.RealtimeMeetingState
import com.aispeech.tablet.core.model.entity.NoteConfigurationEntity
import com.aispeech.tablet.core.model.entity.NoteTranscriptionEntity
import com.aispeech.tablet.core.model.entity.NoteWithConfiguration
import com.aispeech.tablet.core.model.entity.TranscriptionMarkdownTag
import com.aispeech.tablet.core.model.entity.TranscriptionReplaceInfoEntity
import com.aispeech.tablet.core.model.entity.TranscriptionType
import com.aispeech.tablet.core.model.entity.api.response.DomainModelItem
import com.aispeech.tablet.core.model.entity.audio.AudioRecordInfo
import com.aispeech.tablet.core.model.entity.audio.RecordState
import com.aispeech.tablet.core.res.R
import com.aispeech.tablet.core.res.TabletStringsUtils
import com.aispeech.tablet.feature.meeting.data.MatchPosition
import com.aispeech.tablet.feature.meeting.data.RecordEndNotification
import com.aispeech.tablet.feature.meeting.data.SearchMode
import com.aispeech.tablet.feature.meeting.data.SearchState
import com.aispeech.tablet.feature.meeting.data.SearchStatus
import com.aispeech.tablet.feature.meeting.domain.NoteAsrConfigRepository
import com.aispeech.tablet.feature.meeting.domain.NoteConfigurationRepository
import com.aispeech.tablet.feature.meeting.domain.enums.EnsureStopStatus
import com.aispeech.tablet.feature.meeting.domain.find.AiSummaryStrategy
import com.aispeech.tablet.feature.meeting.domain.find.FindStrategy
import com.aispeech.tablet.feature.meeting.domain.find.FullTranscriptStrategy
import com.aispeech.tablet.feature.meeting.domain.find.PagedTranscriptStrategy
import com.aispeech.tablet.feature.meeting.entry.RecordModel
import com.aispeech.tablet.feature.meeting.model.LanguageMenuItem
import com.aispeech.tablet.feature.meeting.utils.RecordAnalyseUtils
import com.aispeech.tablet.lib.markdown.editor.data.SelectionEvent
import com.aispeech.tablet.lib.markdown.editor.data.TextRange
import com.aispeech.tablet.preferences.LOCAL_USER_ID
import com.aispeech.tablet.preferences.Preferences
import com.blankj.utilcode.util.ActivityUtils
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.collections.immutable.ImmutableList
import kotlinx.collections.immutable.persistentListOf
import kotlinx.collections.immutable.toImmutableList
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.FlowPreview
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.debounce
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.flatMapLatest
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.flow.stateIn
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.isActive
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import org.jsoup.Jsoup
import java.time.Duration
import java.time.Instant
import javax.inject.Inject
import kotlin.coroutines.cancellation.CancellationException
import kotlin.time.measureTime

class MarkNotRecordingException :
  Exception(TabletStringsUtils.getString(com.aispeech.tablet.core.res.R.string.noterecordviewmodel_1739179192198_0))

class RecordingPauseMarkerException :
  Exception(TabletStringsUtils.getString(com.aispeech.tablet.core.res.R.string.noterecordviewmodel_1739179192198_1))

enum class DataSourceMode {
  PagedTranscript, // Paging 数据
  FullTranscript, // 完整列表
  AiSummaryView // Ai笔记
}


@OptIn(FlowPreview::class)
@HiltViewModel
class NoteRecordViewModel @Inject constructor(
  private val savedStateHandle: SavedStateHandle,
  private val dataManager: NoteMeetingDataManager,
  private val recordProvider: NoteRecordProvider,
  private val shareEventRepository: MeetingShareEventRepository,
  private val recordingDataManager: NoteRecordingDataManager,
  private val asrConfigRepository: NoteAsrConfigRepository,
  private val markdownParseUseCase: MarkdownParseUseCase,
  private val asrEditLogUseCase: TranscriptionEditLogUseCase,
  private val preferenceRepository: RecordPreferenceRepository,
  private val updateTranscriptionRevisionUseCase: UpdateTranscriptionRevisionUseCase,
  private val transcriptionReplaceInfoRepository: TranscriptionReplaceInfoRepository,
  private val pagedTranscriptStrategy: PagedTranscriptStrategy,
  private val fullTranscriptStrategy: FullTranscriptStrategy,
  private val aiSummaryStrategy: AiSummaryStrategy,
  private val noteConfigurationRepository: NoteConfigurationRepository,
) : ViewModel(), FlowKtxScope {

  private var updateEventJob: Job? = null

  val selectedTab: StateFlow<RecordTab2> = stateFlowOf(RecordTab2.AI)

  /**
   * 选择的语言
   */
  val selectLanguage = recordProvider.selectLanguage

  // outer
//  val runtimeMessages = recordProvider.runtimeMessages
  val currentLine = recordProvider.currentLine
  val realtimeAgendaMessages = recordProvider.realtimeAgendaMessages
  val pagingItems = recordProvider.pagingItemFlow.cachedIn(viewModelScope)

  val currentRows = dataManager.targetRows
  val recordModel: StateFlow<RecordModel> = stateFlowOf(RecordModel.Playing)
  val connectivity = recordProvider.connectivity
  val endNotificationFlow: StateFlow<RecordEndNotification> = recordProvider.endNotificationFlow

  val updateMarkTime = recordProvider.updateMarkTime
  val recordingState = recordProvider.recordingState

  val recordDuration = recordProvider.recordDuration.stateIn(
    scope = viewModelScope,
    started = SharingStarted.WhileSubscribed(5000L),
    initialValue = 0L
  )

  private val recordInfoFlow = dataManager.recordInfo

  val socketQualityFlow = recordProvider.socketQualityFlow
  val recordShareEvents = shareEventRepository.events

  val configState: StateFlow<ImmutableList<DomainModelItem>> = stateFlowOf(persistentListOf())
  val speakerListFlow: StateFlow<List<String>> = stateFlowOf(preferenceRepository.getSpeakerList())

  val realtimeMeetingState = combine(
    flow = recordingState,
    flow2 = recordInfoFlow,
    transform = { state, list ->
      RealtimeMeetingState(
        state = state,
        recordId = recordProvider.recordingInfo?.recordId,
        list = list,
      )
    }
  ).stateIn(
    scope = viewModelScope,
    started = SharingStarted.Lazily,
    initialValue = RealtimeMeetingState(),
  )


    // late init
  private lateinit var noteId: String
  private lateinit var pageFlow: StateFlow<String>
  private lateinit var basePath: String

  val recordingNoteId: String?
    get() = recordProvider.recordingNoteId
  val recordingInfo: AudioRecordInfo?
    get() = recordProvider.recordingInfo

  private val _hideSpeaker = MutableStateFlow(Preferences.hideSpeaker.value ?: false)
  val hideSpeaker = _hideSpeaker.asStateFlow()
  private val _hideTimestamp = MutableStateFlow(Preferences.hideTimestamp.value ?: false)
  val hideTimestamp = _hideTimestamp.asStateFlow()
  private val hideSpeakerObserver = Observer<Boolean> { newValue ->
    _hideSpeaker.value = newValue
  }
  private val hideTimestampObserver = Observer<Boolean> { newValue ->
    _hideTimestamp.value = newValue
  }

  fun changeTab(tab: RecordTab2) {
    viewModelScope.launch {
      selectedTab.emit(tab)
      clearSearch("TabChanged")
    }
  }

  // ---------------------------- FILTER SPEAKER ----------------------------

  // [TODO] 后续的 viewModel 改用 savedStateHandle 的方式来获取 Intent 数据
  val newNoteId: String = savedStateHandle.get<String>("noteId")
    ?: throw IllegalArgumentException("Note ID not found in SavedStateHandle")

  val noteWithConfiguration: StateFlow<NoteWithConfiguration?> =
    noteConfigurationRepository.getNoteWithConfiguration(newNoteId)
      .stateIn(viewModelScope, SharingStarted.WhileSubscribed(5000), null)

  private val noteConfigurationFlow: Flow<NoteConfigurationEntity?> =
    noteConfigurationRepository.getConfigurationForNoteFlow(newNoteId)

  val selectedSpeakers: StateFlow<ImmutableList<String>> =
    noteConfigurationFlow
      .map { (it?.selectedSpeakers ?: emptyList()).toImmutableList() }
      .stateIn(
        scope =  viewModelScope,
        started =  SharingStarted.WhileSubscribed(5000),
        initialValue = persistentListOf())

  val availableSpeakers: StateFlow<List<String>> =
    noteConfigurationRepository.getAvailableSpeakersForNoteFlow(newNoteId)
      .stateIn(
        scope = viewModelScope,
        started = SharingStarted.WhileSubscribed(5000),
        initialValue = emptyList()
      )

  val isAllSpeakersEffectivelySelected: StateFlow<Boolean> =
    combine(selectedSpeakers, availableSpeakers) { selected, available ->
      available.isNotEmpty() && selected.containsAll(available)
    }.stateIn(viewModelScope, SharingStarted.WhileSubscribed(5000), false)


  private val persistedSelectAllIntent: StateFlow<Boolean> =
    noteConfigurationFlow
      .map { it?.isAllSpeakersSelectedIntent ?: true }
      .stateIn(viewModelScope, SharingStarted.WhileSubscribed(5000), false)

  private val recordInfoMapFlow: Flow<Map<String, AudioRecordInfo>> = recordInfoFlow
    .map { recordList -> recordList.associateBy { it.foreignId!! } }

  val filteredConversationRows: StateFlow<List<ConversationItem>> = combine(
    currentRows,
    selectedSpeakers.map { it.toSet() },
    persistedSelectAllIntent,
    recordInfoMapFlow
  ) { rawRows, selected, selectAllIntent, recordInfoMap ->
    rawRows.filterBySpeakersAndRecordInfo(selected, recordInfoMap, selectAllIntent)
  }.stateIn(
    scope = viewModelScope,
    started = SharingStarted.WhileSubscribed(5000),
    initialValue = emptyList()
  )


  /**
   * 用户手动勾选/取消勾选某个说话人。
   */
  fun onSpeakerSelectionChanged(speakerName: String, isChecked: Boolean) {
    AILog.i(TAG, "onSpeakerSelectionChanged name: $speakerName, isChecked: $isChecked")
    viewModelScope.launch {
      val currentSelected = selectedSpeakers.value.toMutableList()
      var newIntentState = persistedSelectAllIntent.value

      // 至少选择一个说话人
      if (currentSelected.size == 1 && !isChecked) {
        showShortToast(TabletStringsUtils.getString(R.string.filter_speaker_must_select_one_speaker))
        return@launch
      }

      if (isChecked) {
        if (!currentSelected.contains(speakerName)) {
          currentSelected.add(speakerName)
        }
        // 检查是否因为这次勾选达到了全选状态
        if (availableSpeakers.value.isNotEmpty() && currentSelected.containsAll(availableSpeakers.value)) {
          newIntentState = true
        }
      } else {
        currentSelected.remove(speakerName)
        newIntentState = false
      }

      noteConfigurationRepository.updateSpeakerSelectionAndIntent(
        newNoteId,
        currentSelected.distinct(),
        newIntentState
      )
    }
  }

  /**
   * 用户点击全选按钮。此按钮只能用于激活全选。
   */
  fun onSelectAllSpeakersClicked() {
    AILog.i(TAG, "onSelectAllSpeakersClicked: ")
    viewModelScope.launch {
      if (!persistedSelectAllIntent.value || !isAllSpeakersEffectivelySelected.value) {
        val allAvailable = availableSpeakers.value.toList()
        noteConfigurationRepository.updateSpeakerSelectionAndIntent(
          newNoteId,
          allAvailable,
          true
        )
      }
    }
  }

  /**
   * 清理当前笔记所有已选择的说话人。
   */
  fun clearAllSelectedSpeakers() {
    AILog.i(TAG, "clearAllSelectedSpeakers: ")
    viewModelScope.launch {
      if (selectedSpeakers.value.isEmpty() && !persistedSelectAllIntent.value) {
        return@launch
      }

      noteConfigurationRepository.updateSpeakerSelectionAndIntent(
        noteId = newNoteId,
        newSelectedSpeakers = emptyList(),
        newIntentState = false
      )
    }
  }


  /**
   * 判断是否应该在重命名时移除旧的说话人
   * @param oldName 旧的说话人名称
   * @return 如果旧名称在当前行中只出现一次，则返回true
   */
  private fun shouldRemoveOldSpeakerOnRename(oldName: String): Boolean {
    return currentRows.value.count {
      (it as? ConversationMixRow)?.speaker?.name == oldName
    } <= 1
  }

  /**
   * 处理修改一个已选说话人名称的请求
   * @param oldName 要被修改的旧说话人名称
   * @param newName 新的说话人名称
   * @param replaceAll 是否替换全部出现的旧名称
   */
  fun onRenameSelectedSpeaker(oldName: String, newName: String, replaceAll: Boolean) {
    viewModelScope.launch {
      // 如果新旧名称相同，无需操作
      if (oldName == newName) {
        AILog.i(TAG, "重命名操作取消：新旧名称相同 ('$oldName')。")
        return@launch
      }

      val currentSelectedSnapshot = selectedSpeakers.value
      val currentAvailableSnapshot = availableSpeakers.value

      // 如果旧名称不在已选列表中
      if (!currentSelectedSnapshot.contains(oldName)) {
        AILog.w(TAG, "无法重命名 '$oldName'：未在已选说话人中找到。将尝试添加 '$newName'（如果尚未选择）。")

        // 如果新名称也不在已选列表中，则添加
        if (!currentSelectedSnapshot.contains(newName)) {
          val updatedSpeakers = currentSelectedSnapshot.toMutableList().apply { add(newName) }
          val allAvailableSelected = currentAvailableSnapshot.isNotEmpty() &&
            updatedSpeakers.containsAll(currentAvailableSnapshot)

          noteConfigurationRepository.updateSpeakerSelectionAndIntent(
            noteId,
            updatedSpeakers.distinct(),
            allAvailableSelected
          )
        } else {
          AILog.i(TAG, "重命名操作取消：旧名称 '$oldName' 未被选择，且新名称 '$newName' 已在选择列表中。")
        }
        return@launch
      }

      // 处理正常的重命名情况
      val updatedSpeakers = currentSelectedSnapshot.toMutableList()

      // 确定是否应该移除旧名称
      if (replaceAll || shouldRemoveOldSpeakerOnRename(oldName)) {
        updatedSpeakers.remove(oldName)
      }

      // 添加新名称（如果尚未存在）
      if (!updatedSpeakers.contains(newName)) {
        updatedSpeakers.add(newName)
      }

      // 去重并更新选择意图
      val finalSpeakers = updatedSpeakers.distinct()
      val allAvailableSelected = currentAvailableSnapshot.isNotEmpty() &&
        finalSpeakers.containsAll(currentAvailableSnapshot)

      // 更新存储库
      noteConfigurationRepository.updateSpeakerSelectionAndIntent(
        noteId,
        finalSpeakers,
        allAvailableSelected
      )
    }
  }

  private var initialSpeakerPerformed = false


  init {
    val localUser = UserConfig.userId
    startCollectingUpdateEvents()
    // 主线程注册Observer避免协程作用域
    Preferences.hideSpeaker.observeForever(hideSpeakerObserver)
    Preferences.hideTimestamp.observeForever(hideTimestampObserver)
    viewModelScope.launch {
      launch {
        UserConfig.userChangeFlow.collect { userId ->
          AILog.i(TAG, "userChangeFlow collect $userId")
          if (userId == LOCAL_USER_ID) {
            pause(true)

            if (localUser != userId) {
              AILog.i(TAG, "user changed from $localUser  to  $userId, so exit note!!!!")
              if (ActivityUtils.getTopActivity().localClassName.contains("NoteEditActivity")) {
                ActivityUtils.getTopActivity().finish()
              }
            }
          }
        }
      }

      launch {
        recordingState.collectLatest {
          AILog.i(TAG, "recordingState: $it")
          when (it) {
            RecordState.RECORDING -> {
              recordModel.emit(RecordModel.Recording)
              notifyRecordStateChanged(RECORD_STATE_RECORDING)
            }

            RecordState.STOPPING, RecordState.STOPPED -> {
              notifyRecordStateChanged(RECORD_STATE_STOP)
            }

            else -> {
              if (it is RecordState.PAUSED) {
                notifyRecordStateChanged(RECORD_STATE_PAUSE)
              } else if (it is RecordState.IDLE) {
                notifyRecordStateChanged(RECORD_STATE_INIT)
              }
            }
          }
        }
      }

      launch {
        pagedTranscriptStrategy.pagingTargetRequests.onEach { requestedPosition ->
          if (_currentMode.value == DataSourceMode.PagedTranscript) {
            if (_targetPosition.value != requestedPosition) {
              _targetPosition.value = requestedPosition
            }
          }
        }.stateIn(scope = viewModelScope)
      }

      launch {

        combine(
          availableSpeakers,
          persistedSelectAllIntent,
          selectedSpeakers
        ) { avSpeakers, intentActive, currentSelected ->
          Triple(avSpeakers, intentActive, currentSelected)
        }
          .debounce(200)
          .collectLatest { (avSpeakers, intentActive, currentSelected) ->
            if (intentActive && avSpeakers.isNotEmpty()) {
              val newSpeakersToAdd = avSpeakers.filterNot { currentSelected.contains(it) }
              if (newSpeakersToAdd.isNotEmpty()) {
                val updatedSelection = (currentSelected + newSpeakersToAdd).distinct()
                noteConfigurationRepository.updateSpeakerSelectionAndIntent(
                  newNoteId,
                  updatedSelection,
                  true
                )
              }
            }
          }
      }
    }

    initAsrConfig()
  }


  // ---------------------------- FIND AND REPLACE ----------------------------

  /**
   * 更新搜索的数据源
   */
  private fun updateSearchSource() {
    val strategy = when (selectedTab.value) {
      RecordTab2.AINote -> DataSourceMode.AiSummaryView
      RecordTab2.Transform -> when (recordModel.value) {
        RecordModel.Recording -> DataSourceMode.PagedTranscript
        RecordModel.Playing -> DataSourceMode.FullTranscript
      }
      else -> _currentMode.value
    }
    AILog.i(TAG, "Find Change to strategy: $strategy")
    _currentMode.value = strategy
  }


  private val currentStrategy: FindStrategy
    get() = when (_currentMode.value) {
      DataSourceMode.PagedTranscript -> pagedTranscriptStrategy
      DataSourceMode.FullTranscript -> fullTranscriptStrategy
      DataSourceMode.AiSummaryView -> aiSummaryStrategy
    }

  private val _currentMode = MutableStateFlow(DataSourceMode.AiSummaryView)
  val currentMode: StateFlow<DataSourceMode> = _currentMode.asStateFlow()

  private val _searchState = MutableStateFlow(SearchState())
  val searchState = _searchState.asStateFlow()

  val pendingScrollTarget = searchState.map { it.pendingScrollTarget }.distinctUntilChanged().stateIn(
    viewModelScope, SharingStarted.WhileSubscribed(5000), null
  )
  val searchMode = searchState.map { it.mode }.distinctUntilChanged().stateIn(
    viewModelScope, SharingStarted.WhileSubscribed(5000), null,
  )

  val currentSearchPosition = searchState
    .map { state ->
      val itemIdentifier = state.highlightedItemIdentifier
      val match = state.matchesInCurrentSegment.getOrNull(state.currentMatchIndex)
      if (itemIdentifier != null && match != null) {
        mapOf(itemIdentifier to Pair(match.start, match.end))
      } else {
        null
      }
    }
    .distinctUntilChanged()
    .stateIn(
      scope = viewModelScope,
      started = SharingStarted.WhileSubscribed(5000),
      initialValue = null
    )

  val currentPlaySeekState = searchState
    .map { state ->
      val match = state.matchesInCurrentSegment.getOrNull(state.currentMatchIndex)
      if (match != null) {
        currentStrategy.getActivePlayPosition(state.currentSegmentIndex, match.start)
      } else {
        null
      }
    }
    .distinctUntilChanged()
    .stateIn(
      scope = viewModelScope,
      started = SharingStarted.WhileSubscribed(5000),
      initialValue = null,
    )


  private val _targetPosition = MutableStateFlow<Int?>(null)

  private val _replaceCurrentMatch = MutableSharedFlow<SelectionEvent.Replace>(replay = 1)
  val replaceCurrentMatch = _replaceCurrentMatch.asSharedFlow()

  fun updateCurrentMatch(replace: SelectionEvent.Replace) {
    _replaceCurrentMatch.tryEmit(replace)
  }

  @OptIn(FlowPreview::class)
  val pagingItemsV2: StateFlow<PagingData<RuntimeDataMixItem>> = _targetPosition
    .debounce(50)
    .flatMapLatest { position ->
      Log.d(TAG, "[Paging] Loading with targetPosition: $position")
      recordProvider.pagingItemFlowV2(
        cacheScope = viewModelScope,
        targetKey = position,
      )
    }
    .stateIn(
      viewModelScope,
      SharingStarted.Lazily,
      PagingData.empty()
    )

  private var searchJob: Job? = null
  private var navigationJob: Job? = null
  private var searchDebounceJob: Job? = null

  /**
   * 执行搜索。
   * @param query 搜索关键词
   */
  fun performSearch(query: String) {
    AILog.i(TAG, "performSearch: '$query'")
    searchJob?.cancel()
    navigationJob?.cancel()

    val trimmedQuery = query.trim()
    currentStrategy.resetSearch()
    currentStrategy.resetDataSource()

    _searchState.update {
      it.copy(
        query = trimmedQuery,
        isActive = trimmedQuery.isNotEmpty(),
        status = if (trimmedQuery.isEmpty()) SearchStatus.Idle else SearchStatus.Searching,
        totalSegments = 0,
        totalMatches = 0,
        currentSegmentIndex = -1,
        currentMatchIndex = 0,
        globalMatchIndex = 0,
        highlightedItemIdentifier = null,
        matchesInCurrentSegment = emptyList(),
        pendingScrollTarget = null
      )
    }

    if (trimmedQuery.isEmpty()) return

    searchJob = viewModelScope.launch {
      try {
        AILog.i(TAG, "Finding matches for query: '$trimmedQuery'")

        val searchResultSummary = currentStrategy.search(trimmedQuery, noteId)
        _searchState.update {
          it.copy(
            totalSegments = searchResultSummary.itemCount,
            totalMatches = searchResultSummary.totalMatches,
          )
        }

        if (searchResultSummary.itemCount > 0) {
          val details = currentStrategy.getResultItemDetails(0, trimmedQuery)

          if (details?.displayLocation != null && details.matches.isNotEmpty()) {
            val (firstItemIdentifier, firstItemLocation, firstSegmentMatches) = details
            navigateToResult(
              targetLocation = firstItemLocation!!,
              targetSegmentIndex = 0,
              targetMatchIndex = 0,
              matchPositions = firstSegmentMatches,
              globalMatchIndex = 0,
              itemIdentifier = firstItemIdentifier
            )
          } else {
            _searchState.update { it.copy(status = SearchStatus.Error) }
            AILog.e(TAG, "策略 ${currentStrategy.getLogPrefix()} 无法定位或获取第一个结果的匹配 (ID: ${details?.identifier})")
          }
        } else {
          _searchState.update { it.copy(status = SearchStatus.NotFound) }
          AILog.i(TAG, "策略 ${currentStrategy.getLogPrefix()} 未找到 '$trimmedQuery' 的结果。")
        }

      } catch (e: Exception) {
        _searchState.update { it.copy(status = SearchStatus.Error) }
        AILog.e(TAG, "Search error for query: '$trimmedQuery'", e)
      }
    }
  }

  fun refreshSearchOnChange() {
    viewModelScope.launch {
      if (!_searchState.value.isActive || _searchState.value.query.isBlank()) {
        AILog.i(TAG, "refreshSearchResultsDueToExternalChange: Search not active or no query, skipping refresh.")
        return@launch
      }

      AILog.i(TAG, "External change detected, refreshing search results for query: '${_searchState.value.query}'")
      triggerSearchRefreshInternal()
    }
  }

  private suspend fun triggerSearchRefreshInternal() {
    searchJob?.cancel()
    _searchState.update { it.copy(
      status = SearchStatus.Searching,
      highlightedItemIdentifier = null
    ) }

    val currentQuery = _searchState.value.query
    val previousId = _searchState.value.highlightedItemIdentifier
    val previousIndex = _searchState.value.currentSegmentIndex

    try {
      AILog.d(TAG, "triggerSearchRefreshInternal: Refreshing query '$currentQuery'")

      val refreshSummary = currentStrategy.refreshSearchAndUpdateResults()
      val newTotalSegments = refreshSummary.itemCount
      val newTotalMatches = refreshSummary.totalMatches

      var segmentIndex = -1
      var itemIdentifier: Any? = null
      var matchesInSegment: List<MatchPosition> = emptyList()

      // 处理搜索结果
      if (newTotalSegments > 0) {
        // 尝试恢复之前的选中项
        if (previousId != null && previousIndex in 0 until newTotalSegments) {
          val itemDetails = currentStrategy.getResultItemDetails(previousIndex, currentQuery)

          if (itemDetails?.identifier == previousId &&
            itemDetails.displayLocation != null &&
            itemDetails.matches.isNotEmpty()
          ) {
            segmentIndex = previousIndex
            itemIdentifier = itemDetails.identifier
            matchesInSegment = itemDetails.matches
            AILog.i(TAG, "Restored previous item at index $previousIndex")
          }
        }

        // 如果未能恢复之前的项，选择第一个结果
        if (segmentIndex == -1) {
          val firstDetails = currentStrategy.getResultItemDetails(0, currentQuery)
          if (firstDetails?.displayLocation != null && firstDetails.matches.isNotEmpty()) {
            segmentIndex = 0
            itemIdentifier = firstDetails.identifier
            matchesInSegment = firstDetails.matches
            AILog.i(TAG, "Selected first item as fallback")
          } else {
            AILog.e(TAG, "Failed to get details for the first item")
          }
        }
      } else {
        AILog.i(TAG, "No results found for query '$currentQuery'")
      }

      // 更新搜索状态
      _searchState.update {
        it.copy(
          status = when {
            newTotalSegments == 0 -> SearchStatus.NotFound
            segmentIndex != -1 -> SearchStatus.Found
            else -> SearchStatus.Error
          },
          totalSegments = newTotalSegments,
          totalMatches = newTotalMatches,
          currentSegmentIndex = segmentIndex,
          currentMatchIndex = 0,
          globalMatchIndex = if (segmentIndex != -1) segmentIndex else 0,
          highlightedItemIdentifier = itemIdentifier,
          matchesInCurrentSegment = matchesInSegment,
        )
      }

    } catch (e: CancellationException) {
      AILog.i(TAG, "Search refresh cancelled for query: '$currentQuery'")
      throw e
    } catch (e: Exception) {
      AILog.e(TAG, "Error during search refresh for query: '$currentQuery'", e)
      _searchState.update {
        it.copy(
          status = SearchStatus.Error,
          totalSegments = 0,
          totalMatches = 0,
          currentSegmentIndex = -1,
          highlightedItemIdentifier = null,
          matchesInCurrentSegment = emptyList(),
          pendingScrollTarget = null
        )
      }
    }
  }


  /**
   * 导航到下一个搜索结果。
   */
  fun findNextGlobal() = handleNavigation(isNext = true)


  /**
   * 导航到上一个搜索结果。
   */
  fun findPreviousGlobal() = handleNavigation(isNext = false)


  private fun handleNavigation(isNext: Boolean) {
    navigationJob?.cancel()

    val direction = if (isNext) "下一个" else "上一个"
    AILog.d(TAG, "导航到 $direction 结果，使用策略: ${currentStrategy.getLogPrefix()}")

    val currentState = _searchState.value
    val totalResultItems = currentStrategy.getResultCount() // 从当前策略获取总结果项数量

    // 检查是否有可导航的结果
    if (totalResultItems == 0) {
      AILog.w(TAG, "导航失败：策略 ${currentStrategy.getLogPrefix()} 中没有搜索结果。")
      return
    }

    val currentSegmentIndex = currentState.currentSegmentIndex
    val currentMatchIndex = currentState.currentMatchIndex
    val matchesInCurrentSegment = currentState.matchesInCurrentSegment

    if (currentSegmentIndex == -1 && totalResultItems > 0) {
      AILog.i(TAG, "当前未选中任何项，尝试导航到第一个/最后一个结果。")
      val targetInitialSegmentIndex = if (isNext) 0 else totalResultItems - 1
      navigateToSpecificResultItem(targetInitialSegmentIndex, isNext, currentState.query)
      return
    }

    if (isNext) {
      if (currentMatchIndex < matchesInCurrentSegment.size - 1) {
        val nextMatchIndex = currentMatchIndex + 1
        val nextGlobalIndex = currentState.globalMatchIndex + 1
        AILog.i(TAG, "在当前项 (结果索引 $currentSegmentIndex) 内移动到下一个匹配 (项内索引 $nextMatchIndex)")
        _searchState.update {
          it.copy(
            currentMatchIndex = nextMatchIndex,
            globalMatchIndex = nextGlobalIndex,
            status = SearchStatus.Found //
          )
        }
        return
      }
    } else {
      if (currentMatchIndex > 0) {
        val prevMatchIndex = currentMatchIndex - 1
        val prevGlobalIndex = currentState.globalMatchIndex - 1
        AILog.i(TAG, "在当前项 (结果索引 $currentSegmentIndex) 内移动到上一个匹配 (项内索引 $prevMatchIndex)")
        _searchState.update {
          it.copy(
            currentMatchIndex = prevMatchIndex,
            globalMatchIndex = prevGlobalIndex,
            status = SearchStatus.Found
          )
        }
        return
      }
    }

    val targetSegmentIndex = if (isNext) currentSegmentIndex + 1 else currentSegmentIndex - 1

    if (targetSegmentIndex < 0) {
      AILog.i(TAG, "已到达第一个搜索结果。")
      showShortToast("已是第一个")
      return
    }
    if (targetSegmentIndex >= totalResultItems) {
      AILog.i(TAG, "已到达最后一个搜索结果。")
      showShortToast("已是最后一个")
      return
    }

    navigateToSpecificResultItem(targetSegmentIndex, isNext, currentState.query)
  }

  /**
   * 辅助函数，用于导航到策略结果列表中的特定索引项。
   * @param targetSegmentIndex 要导航到的项在策略结果列表中的索引。
   * @param isNavigatingNext true 如果是向下导航，false 如果是向上导航 (影响在该项内选择第一个还是最后一个匹配)。
   * @param currentQuery 当前的搜索关键词。
   */
  private fun navigateToSpecificResultItem(targetSegmentIndex: Int, isNavigatingNext: Boolean, currentQuery: String) {
    _searchState.update { it.copy(status = SearchStatus.Searching) }
    AILog.i(TAG, "准备加载策略结果索引: $targetSegmentIndex (导航方向: ${if(isNavigatingNext) "下一个" else "上一个"})")

    navigationJob = viewModelScope.launch {
      try {
        val details = currentStrategy.getResultItemDetails(targetSegmentIndex, currentQuery)

        if (details?.displayLocation != null) {
          val (targetItemIdentifier, targetLocation, targetSegmentMatches) = details
          if (targetSegmentMatches.isNotEmpty()) {
            val targetMatchIndexInSegment = if (isNavigatingNext) {
              0
            } else {
              targetSegmentMatches.size - 1
            }

            val currentGlobalIndex = _searchState.value.globalMatchIndex
            val newSimplifiedGlobalIndex = if (isNavigatingNext) {
              currentGlobalIndex + 1
            } else {
              currentGlobalIndex - 1
            }

            navigateToResult(
              targetLocation = targetLocation!!,
              targetSegmentIndex = targetSegmentIndex,
              targetMatchIndex = targetMatchIndexInSegment,
              matchPositions = targetSegmentMatches,
              globalMatchIndex = newSimplifiedGlobalIndex,
              itemIdentifier = targetItemIdentifier
            )
          } else {
            _searchState.update { it.copy(status = SearchStatus.Error) }
            AILog.w(TAG, "策略 ${currentStrategy.getLogPrefix()} 导航错误: Item (ID: $targetItemIdentifier) 无匹配项 for query '$currentQuery'")
          }
        } else {
          _searchState.update { it.copy(status = SearchStatus.Error) }
          AILog.w(TAG, "策略 ${currentStrategy.getLogPrefix()} 导航失败: 无法获取目标项信息 details: $details")
        }
      } catch (e: Exception) {
        if (isActive) {
          _searchState.update { it.copy(status = SearchStatus.Error) }
          AILog.e(TAG, "策略 ${currentStrategy.getLogPrefix()} 导航到结果索引 $targetSegmentIndex 时发生错误", e)
        } else {
          AILog.w(TAG, "导航到结果索引 $targetSegmentIndex 的任务被取消")
        }
      }
    }
  }

  /**
   * 更新 ViewModel 状态以导航到特定位置。
   */
  private fun navigateToResult(
    targetLocation: Int,
    targetSegmentIndex: Int,
    targetMatchIndex: Int,
    matchPositions: List<MatchPosition>,
    globalMatchIndex: Int,
    itemIdentifier: Any? = null,
  ) {
    AILog.i(
      TAG,
      "navigateToResult: " +
        "targetLocation=$targetLocation, " +
        "resultIndex=$targetSegmentIndex, " +
        "matchInItemIndex=$targetMatchIndex, " +
        "globalMatchIndex=$globalMatchIndex, " +
        "itemIdentifier='$itemIdentifier'"
    )

    _searchState.update {
      it.copy(
        currentSegmentIndex = targetSegmentIndex,
        currentMatchIndex = targetMatchIndex,
        status = SearchStatus.Found,
        globalMatchIndex = globalMatchIndex,
        highlightedItemIdentifier = itemIdentifier,
        matchesInCurrentSegment = matchPositions,
        pendingScrollTarget = itemIdentifier to targetLocation
      )
    }

    if (_currentMode.value == DataSourceMode.PagedTranscript) {
      currentStrategy.updateDataSourceForTarget(targetLocation)
    }
  }


  /**
   * 清楚替换文本的内容
   */
  fun clearReplaceSearch() {
    AILog.i(TAG, "clearReplaceSearch ")
    _searchState.update {
      it.copy(replaceText = "")
    }
  }

  /**
   * 清除搜索状态
   */
  fun clearSearch(reason: String = "unknown") {
    AILog.i(TAG, "clearSearch $reason")
    searchJob?.cancel()
    navigationJob?.cancel()

    currentStrategy.resetSearch()
    currentStrategy.resetDataSource()

    _searchState.update {
      it.copy(
        query = "",
        replaceText = "",
        status = SearchStatus.Idle,
        syncLinkedData = false,
        totalSegments = 0,
        totalMatches = 0,
        globalMatchIndex = 0,
        currentSegmentIndex = -1,
        currentMatchIndex = 0,
        highlightedItemIdentifier = null,
        matchesInCurrentSegment = emptyList(),
        pendingScrollTarget = null,
      )
    }
  }

  /**
   * 激活搜索模式
   */
  fun activateSearch() {
    AILog.i(TAG, "activateSearch: ")
    _searchState.update {
      it.copy(
        mode = SearchMode.FindOnly,
        isActive = true
      )
    }

    updateSearchSource()
  }

  /**
   * 切换到替换模式
   */
  fun switchToReplaceMode() {
    AILog.i(TAG, "switchToReplaceMode: ")
    _searchState.update { it.copy(mode = SearchMode.FindAndReplace) }
  }

  /**
   * 更新搜索查询并执行搜索
   * @param newQuery 新的搜索查询
   * @param autoSearch 是否自动执行搜索（默认为true）
   */
  fun onQueryChanged(newQuery: String, autoSearch: Boolean = true) {
    // 更新查询文本
    _searchState.update { it.copy(query = newQuery) }

    if (autoSearch) {
      searchDebounceJob?.cancel()
      searchDebounceJob = viewModelScope.launch {
        delay(300)
        performSearch(newQuery)
      }
    }
  }

  /**
   * 更新替换文本
   */
  fun onReplaceTextChanged(newText: String) {
    _searchState.update { it.copy(replaceText = newText) }
  }

  /**
   * 停用搜索
   */
  fun deactivateSearch() {
    clearSearch("Deactivated")
    _searchState.update {
      it.copy(
        mode = SearchMode.Idle,
        isActive = false,
        query = "",
        replaceText = ""
      )
    }
  }

  /**
   * 消费滚动目标
   */
  fun consumeScrollTarget() {
    if (_searchState.value.pendingScrollTarget != null) {
      _searchState.update { it.copy(pendingScrollTarget = null) }
    }
  }

  /**
   * 切换替换同步的状态
   */
  fun toggleReplaceSyncLinkState(value: Boolean) {
    _searchState.update { it.copy(syncLinkedData = value) }
  }


  /**
   * 开始收集记录更新事件
   */
  private fun startCollectingUpdateEvents() {
    updateEventJob = viewModelScope.launch {
      recordingDataManager.updateEvent.collectLatest {
        dataManager.updateRecordEvent(it)
      }
    }
  }

  fun isSameNote(): Boolean {
    return recordingNoteId == noteId
  }

  /**
   * 开始会议
   */
  fun start(language: LanguageMenuItem.Content) {
    AILog.i(TAG, "start")
    viewModelScope.launch {
      runCatching {
        recordModel.emit(RecordModel.Recording)
        recordProvider.startWith(
          noteId = noteId,
          pageFlow = pageFlow,
          audioBasePath = basePath,
          language = language,
          useOnlineMode = !(Preferences.useOfflineMode.value ?: false),
          onCreated = {
            _currentMode.value = DataSourceMode.PagedTranscript
            shareEventRepository.send(MeetingEvent.MeetingCreated(it))
            RecordAnalyseUtils.reportOnStart(recordInfoFlow.value.size)
          },
          onMaxTimeFinished = this@NoteRecordViewModel::onMeetingMaxTimeFinished
        )
      }.onFailure {
        AILog.i(TAG, "start-error: ${it.message}")
      }
    }
  }

  suspend fun removeLastPoint(objectId: String? = null) {
    AILog.i(TAG, "removeLastPoint-objectId: $objectId")
    if (objectId == null) {
      recordProvider.removeLastPoint()
    } else {
      dataManager.updateAllWithDb {
        if (this.objectId == objectId) {
          val markPoints = markPoints.dropLast(1)
          val update = copy(markPoints = markPoints)
          update
        } else {
          this
        }
      }
    }
  }

  /**
   * 暂停
   *
   */
  fun pause(manual: Boolean = true) {
    AILog.i(TAG, "pause: ")
    viewModelScope.launch {
      recordProvider.pause(manual)
    }
  }

  /**
   * 返回暂停
   * 忽略其他笔记返回暂停的情况
   */
  fun backPause(currentNote: String) {
    AILog.i(
      TAG,
      "backPause, currentNoteId: $currentNote, recordNoteId: ${recordProvider.recordingNoteId}"
    )
    if (currentNote == recordProvider.recordingNoteId) {
      viewModelScope.launch {
        recordProvider.backPause()
        RecordAnalyseUtils.reportAutoPaused()
      }
    }
  }

  /**
   * 继续
   */
  fun resume() {
    AILog.i(TAG, "resume-state: ${recordingState.value}")
    viewModelScope.launch {
      recordProvider.resume()
    }
  }

  /**
   * 从任务栈里杀掉时
   * 如果使用viewModelScope
   * 会导致过程无法完整执行 因此使用全局的workscope
   */
  fun pauseOnKill() {
    AILog.i(TAG, "simple pause")
    Tablet.workScope.launch {
      recordProvider.backPause()
    }
  }

  /**
   * 结束
   */
  fun stop(
    withBack: Boolean = false,
    focus: Boolean = false,
    timeout: Boolean = false,
    reason: String = "manual"
  ) {
    AILog.i(
      TAG,
      "focus: $focus, timeout: $timeout, state: ${recordingState.value}, reason: $reason"
    )
    viewModelScope.launch {
      runCatching {
        recordProvider.stop(
          withBack = withBack,
          focus = focus,
          timeout = timeout,
          reason = reason
        ) {
          _currentMode.value = DataSourceMode.FullTranscript
          viewModelScope.launch {
            shareEventRepository.send(MeetingEvent.MeetingStopped)
          }
        }
      }.onFailure {
        AILog.i(TAG, "stop-error: ${it.message}")
      }

    }
  }

  /**
   * 标记
   */
  fun markPoint(): RecordingMarkResult {
    AILog.i(TAG, "markPoint-state: ${recordingState.value}")
    return recordProvider.markPoint()
  }

  /**
   * 强制结束重新开始
   */
  fun focusStopAndStart(
    language: LanguageMenuItem.Content,
    useOfflineMode: Boolean = false,
  ) {
    AILog.i(TAG, "focusStopAndStart: $language")
    viewModelScope.launch {
      recordProvider.focusStopAndStart(
        language = language,
        audioBasePath = basePath,
        useOfflineMode = useOfflineMode,
        onCreated = {
          viewModelScope.launch {
            shareEventRepository.send(MeetingEvent.MeetingCreated(it))
          }
        },
        onStopped = {
          viewModelScope.launch {
            shareEventRepository.send(MeetingEvent.MeetingStopped)
          }
        },
        onMaxTimeFinished = this@NoteRecordViewModel::onMeetingMaxTimeFinished
      )
    }
  }

  /**
   * 修改当前的音频
   */
  fun updateTranscriptionRows() {
    AILog.i(TAG, "updateTranscriptionRows")
    viewModelScope.launch {
      dataManager.updateAllData()
    }
  }

  /**
   * 切换到录音模式
   */
  fun toRecordModel(language: LanguageMenuItem.Content) {
    viewModelScope.launch {
      recordModel.emit(RecordModel.Recording)
      start(language)

      // 继续录音，默认全选
      onSelectAllSpeakersClicked()
    }
  }

  fun initPlaying() {
    viewModelScope.launch {
      val info = recordInfoFlow.value
      val size = info.size
      val isMeeting = recordProvider.isMeeting()

      AILog.i(
        TAG,
        "initPlaying, recordModel = ${recordModel.value}, isMeeting= $isMeeting, size = $size, info = $info"
      )

      if (size > 0 && isMeeting.not()) {
        recordModel.emit(RecordModel.Playing)
        updateTranscriptionRows()
      }
    }
  }

  /**
   * 恢复录音
   *
   * # warning
   * 这里要重新绑定超时结束函数
   * 会议没结束，点进来的会重新创建 viewmodel
   * 但是结束的闭包捕获的已经关闭的 viewmodel，所以这里需要重新 rebind 下
   */
  fun resumeRecording() {
    AILog.i(TAG, "resumeRecording: ")
    viewModelScope.launch {
      recordProvider.rebindOnMaxTimeFinished {
        onMeetingMaxTimeFinished()
      }
      recordModel.emit(RecordModel.Recording)
      _currentMode.value = DataSourceMode.PagedTranscript
    }
  }

  /**
   * 超时结束回调
   */
  private fun onMeetingMaxTimeFinished() {
    AILog.i(TAG, "onMeetingMaxTimeFinished: ${viewModelScope.isActive}")
    viewModelScope.launch {
      shareEventRepository.send(MeetingEvent.MeetingStopped)
    }
  }

  fun showDialog() {
    viewModelScope.launch {
      endNotificationFlow.emit(RecordEndNotification.Confirm.CaseClosed)
    }
  }

  /**
   * 检查是否有正在录音的其他笔记
   * 如果不是当前的笔记在录音，则结束上一个笔记的录音
   */
  fun ensureStopAnotherMeeting(
    noteId: String,
    completed: suspend (status: EnsureStopStatus) -> Unit
  ) {
    AILog.i(TAG, "ensureStop-noteId: $noteId, state: ${recordProvider.recordingState.value}")

    val recordPausedTime = Preferences.recordPausedTime
    val isMeeting = recordProvider.recordingState.value.isMeeting()
    AILog.i(TAG, "ensureStop-isMeeting: $isMeeting, pausedTime :$recordPausedTime")

    if (!isMeeting) {
      if (recordPausedTime != 0L) {
        Preferences.recordPausedTime = 0L
      }
      recordProvider.clearLastNoteState()
      viewModelScope.launch { completed.invoke(EnsureStopStatus.IDLE) }
      return
    }

    // 不是当前的笔记在录音，则结束，否则恢复状态
    if (recordingNoteId != noteId) {
      viewModelScope.launch {
        recordProvider.clearLastNoteState()
        recordProvider.stop(reason = "openNewNote", focus = true) {
          AILog.i(TAG, "ensureStop recording, from another noteId: $recordingNoteId")
          viewModelScope.launch { completed.invoke(EnsureStopStatus.STOPPED_ANOTHER_MEETING) }
        }
      }
    } else {
      if (recordPausedTime != 0L && recordPausedTime.isMoreThanFiveDays()) {
        viewModelScope.launch {
          recordProvider.stop(reason = "pauseTimeout", focus = true) {
            AILog.i(TAG, "ensureStop recording, from pauseTimeout: $recordPausedTime")
            recordProvider.clearLastNoteState()
            viewModelScope.launch { completed.invoke(EnsureStopStatus.IDLE) }
            _currentMode.value = DataSourceMode.FullTranscript
          }
        }
      } else {
        viewModelScope.launch { completed.invoke(EnsureStopStatus.RESUME_CURRENT_MEETING) }
      }
    }
  }

  /**
   * 初始化
   */
  fun initWith(noteId: String, pageFlow: StateFlow<String>, audioBasePath: String) {
    AILog.i(TAG, "init: $noteId");
    this.noteId = noteId
    this.pageFlow = pageFlow
    this.basePath = audioBasePath
  }

  /**
   * 更新会议过程中的历史数据
   * 主要用来处理文件转写完成，历史数据未自动更新的问题
   */
  private suspend fun updateMeetingHistoryDataScope() {
    recordProvider.updateHistoryData()
  }

  fun updateMeetingHistoryData() {
    viewModelScope.launch {
      updateMeetingHistoryDataScope()
    }
  }

  fun resetRecordState() = recordProvider.resetRecordState()

  fun setBackground(isBackground: Boolean) {
    Preferences.isRecordBackground = isBackground
  }

  private fun notifyRecordStateChanged(state: Int) {
    AILog.i(TAG, "record state change $state")
    Preferences.recordState = state
  }

  /**
   * 获取已经选择模型配置
   */
  fun getSelectAsrConfig() = preferenceRepository.selectAsrConfig

  /**
   * 更新选择的转写模型
   */
  fun updateAsrConfig(modalId: String) {
    preferenceRepository.selectAsrConfig = modalId
  }

  /**
   * 转写模型配置
   */
  private fun initAsrConfig() {
    viewModelScope.launch {
      runCatching {
        val result = asrConfigRepository.asrConfig()
        val newDomainModelItem = DomainModelItem(
          "-1",
          TabletStringsUtils.getString(com.aispeech.tablet.core.res.R.string.noterecordviewmodel_1739179192199_2)
        )
        val updatedDomainModelList: List<DomainModelItem> =
          listOf(newDomainModelItem) + result.domainModel
        configState.emit(updatedDomainModelList.toImmutableList())
      }.onFailure {
        val newDomainModelItem = DomainModelItem(
          "-1",
          TabletStringsUtils.getString(com.aispeech.tablet.core.res.R.string.noterecordviewmodel_1739179192199_2)
        )
        val updatedDomainModelList = listOf(newDomainModelItem)
        configState.emit(updatedDomainModelList)
        AILog.i(TAG, "getAsrConfig error ${it.message}")
      }
    }
  }

  private fun editLog(
    matchText: String,
    replaceText: String,
  ) {
    viewModelScope.launch {
      asrEditLogUseCase.asrEditLog(
        noteUid = noteId,
        matchText = matchText,
        replaceText = replaceText
      )
    }
  }

  fun addSpeaker(value: String) {
    if (value.isBlank()) return
    viewModelScope.launch {
      preferenceRepository.addSpeaker(value)
      speakerListFlow.emit(preferenceRepository.getSpeakerList())
    }
  }

  fun clearAllSpeaker() {
    viewModelScope.launch {
      preferenceRepository.clearAllSpeaker()
      speakerListFlow.emit(emptyList())
    }
  }

  fun editSpeaker(
    id: Long,
    speaker: String,
    newSpeaker: String,
    syncAll: Boolean = false,
    onChanged: (number: Int) -> Unit
  ) {
    AILog.i(TAG, "editSpeaker-speaker: $speaker, newSpeaker: $newSpeaker, syncAll: $syncAll")
    addSpeaker(newSpeaker)

    // 处理说话人筛选的更新
    onRenameSelectedSpeaker(
      oldName = speaker,
      newName = newSpeaker,
      replaceAll = syncAll
    )

    viewModelScope.launch {
      val changedCounter = if (recordingState.value.isMeeting()) {
        recordProvider.editSpeaker(
          id = id,
          speaker = speaker,
          newSpeaker = newSpeaker,
          syncAll = syncAll
        )
      } else {
        dataManager.editSpeaker(
          id = id,
          speaker = speaker,
          newSpeaker = newSpeaker,
          syncAll = syncAll
        )
      }

      AILog.i(TAG, "editSpeaker-changedCounter: $changedCounter")

      if (changedCounter == 1 && !syncAll) {
        showShortToast(TabletStringsUtils.getString(com.aispeech.tablet.core.res.R.string.noterecordviewmodel_1739179192199_3))
      } else if (changedCounter > 0) {
        showShortToast(
          TabletStringsUtils.getString(com.aispeech.tablet.core.res.R.string.chataidatamanager_1739156714036_4) + "$changedCounter" + TabletStringsUtils.getString(
            com.aispeech.tablet.core.res.R.string.a_replacement
          )
        )
      }

      onChanged.invoke(changedCounter)

      RecordAnalyseUtils.reportEditSpeaker(noteId, syncAll)
    }
  }

  fun updateModelId() {
    AILog.i(TAG, "updateModelId")
    viewModelScope.launch {
      recordProvider.updateModelId()
    }
  }


  /* fun markdownContentUpdate(
     index: Int,
     text: String,
     recording: Boolean = false
   ) {
     AILog.d(TAG, "markdownContentUpdate-text: $text")
     val tagsList = markdownParseUseCase.analyzeMarkdown(text).also {
       AILog.d(TAG, "markdownContent-parsed: $it: ")
     }

     if (recording) {
       viewModelScope.launch {
         recordProvider.markdownContentUpdate(index, tagsList)
       }
     } else {
       val row = currentRows.value.getOrNull(index) as? ConversationMixRow
       if (row != null) {
         viewModelScope.launch {
           dataManager.updateTagsList(
             id = row.sourceId,
             tagList = tagsList
           )
         }
       }
     }
   }*/

  private suspend fun processTextUpdate(
    item: ConversationRow2.ContentItem,
    updatedText: String?,
    contentId: String
  ): ConversationRow2.ContentItem {
    return if (updatedText != null && updatedText != item.text) {
      if (recordingInfo?.foreignId == contentId) {
        updateTranscriptionRevisionUseCase.invoke(item, contentId, updatedText)
      }
      item.copy(text = updatedText)
    } else {
      item.copy(text = updatedText ?: item.text)
    }
  }

  suspend fun deleteAiTextData() {
    dataManager.deleteAllTranscriptions()
  }

  fun debugQueryDataSize() {
    viewModelScope.launch {
      AILog.d(TAG, "debugQueryDataSize: ${dataManager.getAllTranscriptionCount()}")
    }
  }

  data class ItemWithTags(
    val item: ConversationRow2.ContentItem,
    val tags: List<TranscriptionMarkdownTag>
  ) {
    override fun toString(): String {
      return "ItemWithTags(text: ${item.text}, tags: $tags)"
    }
  }

  fun generateCompleteText(item: RuntimeDataMixItem) = buildString {
    item.text.forEach { append(it.text) }
    item.middle.forEach { append(it) }
  }

//  fun generateMarkdownContent(
//    selectBegin: Int? = null,
//    items: List<ConversationRow2.ContentItem>,
//    tagList: List<TranscriptionMarkdownTag>
//  ): String {
//
//    var currentPosition = 0
//    val itemsWithTags = items.map { item ->
//      val itemStart = currentPosition
//      val itemEnd = itemStart + item.text.length
//      val itemTags = tagList.filter { tag ->
//        (tag.start < itemEnd && tag.end > itemStart) // 标签与当前item有重叠
//      }.map { tag ->
//        tag.copy(
//          start = maxOf(0, tag.start - itemStart),
//          end = minOf(item.text.length, tag.end - itemStart)
//        )
//      }
//      currentPosition = itemEnd
//      ItemWithTags(item, itemTags)
//    }
//    AILog.d(TAG, "itemsWIthTags: $itemsWithTags")
//
//    val result = StringBuilder()
//    itemsWithTags.forEach { (item, tags) ->
//      val itemContent = if (item.begin == selectBegin) {
//        "<font color=\"#006AF8\">${
//          markdownGenerateUseCase.generateMarkdown(
//            item.text,
//            tags
//          )
//        }</font>"
//      } else {
//        markdownGenerateUseCase.generateMarkdown(item.text, tags)
//      }
//      result.append(itemContent)
//    }
//
//    AILog.d(TAG, "generate: $result")
//    return result.toString()
//  }

  /**
   * 比如是不是超过了5天
   */
  private fun Long.isMoreThanFiveDays(currentTime: Long = System.currentTimeMillis()): Boolean {
    val instant1 = Instant.ofEpochMilli(this)
    val instant2 = Instant.ofEpochMilli(currentTime)

    val duration = Duration.between(instant1, instant2)
    return duration.toDays() > 5
  }

  /*************************************************编辑相关处理*************************************************/

  /**
   * 替换当前实时录音原文的文本
   */
  fun replaceCurrentDataListText(searchText: String, replaceText: String): Int {
    //editLog(searchText, replaceText)
    return recordProvider.replaceCurrentDataListText(searchText, replaceText)
  }

  /**
   * 录音原文-全文编辑-更新
   */
  fun updateMarkdownContents(map: Map<Long, String>) {
    viewModelScope.launch {
      measureTime {
        map.forEach { (sourceId, html) ->
          updateMarkdownContent(sourceId, html)
        }
      }.also {
        AILog.d(TAG, "updateMarkdownContents: [use time:$it]")
      }

    }
  }

  /**
   * 更新录音原文的一句话
   */
  fun updateRecordSentence(
    sourceItem: RuntimeDataMixItem,
    timeRange: IntRange,
    newText: String
  ) {
    AILog.i(
      TAG,
      "updateRecordSentence: [${sourceItem.id}, timeRange = $timeRange, newText = $newText]"
    )
    viewModelScope.launch {
      var update = false
      if (sourceItem.id == 0L) {
        findCurrentLineUpdateData(sourceItem.contentId, timeRange.first).let {
          AILog.d(TAG, "current = ${it?.javaClass?.simpleName}")
          when (it) {
            is NoteTranscriptionEntity -> {
              val runtimeItem = it.toRuntimeData()
              update = dataManager.updateRecordSentence(
                sourceItem.contentId,
                it.id,
                it.getRelatedTranslateId(),
                runtimeItem.text,
                timeRange.first,
                newText
              )
            }

            is RuntimeDataMixItem -> {
              recordProvider.updateCurrentDataListText(sourceItem.text.map { item ->
                if (item.begin == timeRange.first) {
                  dataManager.upsertRevisionItem(sourceItem.contentId, item, newText)
                  item.copy(text = newText)
                } else item
              })
            }

            else -> {}
          }
        }
      } else {
        update = dataManager.updateRecordSentence(
          sourceItem.contentId,
          sourceItem.id,
          sourceItem.translateId,
          sourceItem.text,
          timeRange.first,
          newText
        )
      }
      if (update) {
        updateMeetingHistoryDataScope()
      }
    }
  }

  fun updateRecordSentence(
    sourceItem: ConversationMixRow,
    timeRange: IntRange,
    newText: String
  ) {
    AILog.i(
      TAG,
      "updateRecordSentence: [${sourceItem.sourceId}, timeRange = $timeRange, newText = $newText]"
    )
    viewModelScope.launch {
      if (sourceItem.speaker.useSpeaker || dataManager.hasEntity(sourceItem.sourceId)) {
        dataManager.updateRecordSentence(
          sourceItem.contentId,
          sourceItem.sourceId,
          sourceItem.translateId,
          sourceItem.items,
          timeRange.first,
          newText
        )
      } else {
        dataManager.replaceRecordSentence(sourceItem.contentId, timeRange, newText)
      }
    }
  }

  private suspend fun findCurrentLineUpdateData(
    contentId: String,
    begin: Int
  ): Any? {
    AILog.i(TAG, "findCurrentLineUpdateData: [contentId = $contentId,begin = $begin]")
    return currentLine.first().let { cur ->
      //判断当前行是否存在该begin
      val contains = cur?.text?.any { item -> item.begin == begin } ?: false
      if (!contains) {
        dataManager.getNoteTranscriptionContentByBegin(contentId, begin).firstOrNull()
      } else {
        cur
      }
    }
  }

  private suspend fun updateMarkdownContent(sourceId: Long, html: String) =
    withContext(Dispatchers.IO) {
      runCatching {
        val rangeList = Jsoup.parse(html).select("range").map {
          val begin = it.attr("begin").toIntOrNull() ?: 0
          val end = it.attr("end").toIntOrNull() ?: 0
          TextRange(begin, end, it.html().replace("\n", ""))
        }
        currentRows.value.firstOrNull { it is ConversationMixRow && it.sourceId == sourceId }?.let {
          updateMarkdownContent(it, rangeList)
        }
      }.onFailure {
        AILog.e(TAG, "updateMarkdownContent failed: ${it.message} [html = $html]")
      }
    }

  /**
   * 更新录音原文
   */
  fun updateMarkdownContent(rowItem: ConversationItem, textRangeList: List<TextRange>) {
    AILog.i(TAG, "updateMarkdownContent: [rowItem = $rowItem, textRangeList = $textRangeList]")
    if (rowItem !is ConversationMixRow) return
    viewModelScope.launch {
      updateNoteTranscriptionParts(
        rowItem.sourceId,
        rowItem.translateId,
        rowItem.contentId,
        rowItem.items,
        textRangeList
      )

      refreshSearchOnChange()
    }
  }

  private suspend fun NoteTranscriptionEntity.getRelatedTranslateId(): Long? {
    return dataManager.getTranscriptionEntity(this.noteContentOwnerId, this.timeStamp, TranscriptionType.TRANSLATE)?.id
  }

  /**
   * 更新实时录音原文
   */
  fun updateRecordingMarkdownContent(
    rowItem: RuntimeBaseData,
    textRangeList: List<TextRange>,
    maxUpdateTime: Int? = null
  ) {
    AILog.i(
      TAG,
      "updateRecordingMarkdownContent: [rowItem = $rowItem, \n textRangeList = $textRangeList]"
    )
    if (rowItem !is RuntimeDataMixItem) return
    viewModelScope.launch {
      if (rowItem.id == 0L) {
        val begin = rowItem.text.maxOfOrNull { it.begin } ?: textRangeList.maxOfOrNull { it.begin }
        val duration = rowItem.text.maxOfOrNull { it.end }
        val current = findCurrentLineUpdateData(rowItem.contentId, begin ?: -1)
        if (null == current) {
          AILog.e(TAG, "not find current: [begin = $begin]")
        }
        when (current) {
          is NoteTranscriptionEntity -> {
            val runtimeItem = current.toRuntimeData()
            val appendList = maxUpdateTime?.let {
              runtimeItem.text.filter { it.begin >= maxUpdateTime }
                .map { TextRange(it.begin, it.end, it.text) }
            } ?: emptyList()

            updateNoteTranscriptionParts(
              current.id,
              current.getRelatedTranslateId(),
              current.noteContentOwnerId,
              runtimeItem.text,
              textRangeList + appendList,
            )
            recordProvider.notifyNewDataAvailable()
          }

          is RuntimeDataMixItem -> {
            val rangeMap = textRangeList.associate { it.begin to it.text }
            val newItems =
              current.text.filter { rangeMap.containsKey(it.begin) }
                .map {
                  val updateText = rangeMap[it.begin]
                  // if (updateText != it.text && !it.spoken) {
                  //   dataManager.upsertRevisionItem(rowItem.contentId, it, updateText ?: "")
                  // }
                  it.copy(text = updateText ?: it.text)
                }
            recordProvider.updateCurrentDataListText(newItems, duration ?: 0)
          }
        }
      } else {
        updateNoteTranscriptionParts(rowItem.id, rowItem.translateId, rowItem.contentId, rowItem.text, textRangeList)
        recordProvider.notifyNewDataAvailable()
        refreshSearchOnChange()
      }
    }
  }

  /**
   * 更新录音原文内容
   * @param sourceId 录音原文id
   * @param items 录音原文原始内容
   * @param replaceList 需要替换的文本范围
   */
  private suspend fun updateNoteTranscriptionParts(
    sourceId: Long,
    translateId: Long? = null,
    contentId: String,
    items: List<ConversationRow2.ContentItem>,
    replaceList: List<TextRange>,
  ) {
    AILog.i(TAG, "开始更新sourceId=$sourceId, 待更新条目数: ${replaceList.size}")
    val rangeMap = replaceList.associate { it.begin to it.text }
    val newItems = items.filter { rangeMap.containsKey(it.begin) }.map {
      val updateText = rangeMap[it.begin]
      /*if (updateText != it.text && !it.spoken) {
        dataManager.upsertRevisionItem(contentId, it, updateText ?: "")
      }*/
      it.copy(text = updateText ?: it.text)
    }
    if (newItems.isEmpty()) {
      AILog.w(TAG, "⚠️ 即将删除数据 sourceId=$sourceId")
      dataManager.deleteConversationRow2Data(sourceId, translateId)
    } else {
      AILog.i(TAG, "更新后条目数: ${newItems.size}, 示例文本: ${newItems.first().text.take(15)}...")
      dataManager.updateConversationRow2Data(sourceId, newItems)
    }
  }

  fun addReplaceInfo(
    oldText: String,
    newText: String,
    timeRange: IntRange? = null,
    curContentId: String? = null
  ) {
    AILog.i(TAG, "addReplaceInfo contentId = ${recordingInfo?.foreignId}")
    val contentId = curContentId ?: recordingInfo?.foreignId ?: return
    val mTimeRange = timeRange ?: IntRange(0, recordDuration.value.toInt())
    val data = TranscriptionReplaceInfoEntity(
      oldText = oldText,
      newText = newText,
      transcriptionOwnerId = contentId,
      begin = mTimeRange.first.toLong(),
      end = mTimeRange.last.toLong(),
      createTime = System.currentTimeMillis()
    )
    viewModelScope.launch {
      transcriptionReplaceInfoRepository.upsert(data).also {
        AILog.i(
          TAG,
          "addReplaceInfo: [id = $it, oldText = $oldText, newText = $newText, timeRange = $mTimeRange]"
        )
      }
      var objectId = recordingInfo?.objectId
      if (timeRange != null && contentId != curContentId) {
        objectId = getObjectId(contentId)
      }
      asrEditLogUseCase.asrEditLog(
        noteUid = noteId,
        matchText = oldText,
        replaceText = newText,
        begin = mTimeRange.first,
        end = mTimeRange.last,
        objectId = objectId
      )
    }
  }

  private fun getObjectId(contentId: String): String? {
    return recordInfoFlow.value.firstOrNull { it.foreignId == contentId }?.objectId
  }

  override fun onCleared() {
    super.onCleared()
    AILog.i(TAG, "onCleared: []")
    updateEventJob?.cancel()
    recordProvider.rebindOnMaxTimeFinished(null)
    Preferences.hideSpeaker.removeObserver(hideSpeakerObserver)
    Preferences.hideTimestamp.removeObserver(hideTimestampObserver)
  }

  companion object {
    const val TAG = "NoteRecordViewModel"
    const val RECORD_STATE_INIT = 0
    const val RECORD_STATE_RECORDING = 1
    const val RECORD_STATE_PAUSE = 2
    const val RECORD_STATE_STOP = 3
  }
}


fun List<ConversationItem>.filterBySpeakersAndRecordInfo(
  selectedSpeakers: Set<String>,
  recordInfoMap: Map<String, AudioRecordInfo>,
  selectAllIntent: Boolean
): List<ConversationItem> {
  // 如果选择全部或没有选择任何说话者，直接返回原始行
  if (selectAllIntent || selectedSpeakers.isEmpty()) {
    return this
  }

  // 有选择的说话者时进行过滤
  return this.asSequence()
    .filter { item ->
      when (item) {
        is ConversationMixRow -> selectedSpeakers.contains(item.speaker.name) &&
          recordInfoMap[item.contentId]?.offlineCompleted == true
        is ConversationDivider -> true  // 先保留所有分隔符，后续处理
        else -> false
      }
    }
    .fold(mutableListOf<ConversationItem>()) { result, item ->
      // 处理分隔符逻辑：避免连续分隔符或开头就是分隔符
      if (item is ConversationDivider) {
        if (result.isNotEmpty() && result.last() !is ConversationDivider) {
          result.add(item)
        }
      } else {
        result.add(item)
      }
      result
    }
    .let {
      // 移除末尾的分隔符
      if (it.lastOrNull() is ConversationDivider) {
        it.apply { removeLast() }
      } else {
        it
      }
    }
}
