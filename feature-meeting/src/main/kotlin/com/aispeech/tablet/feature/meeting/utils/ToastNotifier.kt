package com.aispeech.tablet.feature.meeting.utils

import android.content.Context
import android.widget.Toast
import com.aispeech.tablet.core.common.showShortToast
import dagger.hilt.android.qualifiers.ApplicationContext
import dagger.hilt.android.scopes.ViewModelScoped
import javax.inject.Inject

@ViewModelScoped
class ToastNotifier @Inject constructor(
  @ApplicationContext private val context: Context
) {
  fun showToast(message: String) {
    showShortToast(message)
  }
}