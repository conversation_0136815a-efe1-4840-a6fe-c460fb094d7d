package com.aispeech.tablet.feature.meeting.data

enum class SearchStatus { Idle, Searching, Found, NotFound, Error }

data class MatchPosition(
  val start: Int,
  val end: Int
)

data class SearchState(
  val query: String = "",
  val replaceText: String = "",
  val isActive: Boolean = false,
  val syncLinkedData: Boolean = false,
  val mode: SearchMode = SearchMode.Idle,
  val status: SearchStatus = SearchStatus.Idle,
  val totalSegments: Int = 0,                // 匹配的段落总数
  val totalMatches: Int = 0,                 // 所有匹配项的总数
  val currentSegmentIndex: Int = -1,         // 当前段落索引
  val currentMatchIndex: Int = 0,            // 当前段内匹配索引
  val globalMatchIndex: Int = 0,             // 当前匹配在所有匹配中的全局索引
  val highlightedItemIdentifier: Any? = null,       // 当前高亮的段落ID
  val matchesInCurrentSegment: List<MatchPosition> = emptyList(), // 当前段落中的所有匹配位置
  val pendingScrollTarget: Pair<Any?, Int>? = null,
)

enum class SearchMode {
  Idle,
  FindOnly,
  FindAndReplace
}

