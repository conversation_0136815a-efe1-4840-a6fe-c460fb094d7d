package com.aispeech.tablet.feature.meeting.di

//
//@Module
//@InstallIn(ViewModelComponent::class)
//object NoteModule {
//
//  @Provides
//  fun provideNoteRecordDbManager(infoAdapter: JsonAdapter<RecordInfoItem>): NoteRecordDbManager {
//    return NoteRecordDbManager(infoAdapter)
//  }
//
//  @Provides
//  fun provideNoteOfflineTransManager(
//    repository: OfflineTranscribeRepository,
//    detailManager: RecordDetailManager,
//    recordDbManager: NoteRecordDbManager,
//  ): NoteOfflineTransManager {
//    return NoteOfflineTransManager(repository, detailManager, recordDbManager)
//  }
//
//  @Provides
//  fun provideNotePlayManager(
//    recordDbManager: NoteRecordDbManager,
//    infoAdapter: JsonAdapter<RecordInfoItem>,
//    noteOfflineTransManager: NoteOfflineTransManager,
//  ): NotePlayManager {
//    return NotePlayManager(
//      recordDbManager,
//      infoAdapter,
//      noteOfflineTransManager
//    )
//  }
//
//
//}