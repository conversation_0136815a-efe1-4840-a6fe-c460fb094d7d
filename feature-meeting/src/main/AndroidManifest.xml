<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android">

    <uses-permission android:name="android.permission.INTERNET" /> <!-- Permissions options for the `storage` group -->
    <uses-permission android:name="android.permission.MODIFY_PHONE_STATE" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" /> <!-- Permissions options for the `microphone` or `speech` group -->
    <uses-permission android:name="android.permission.RECORD_AUDIO" /> <!-- Permissions options for the `bluetooth` group -->
    <uses-permission android:name="android.permission.BLUETOOTH" />
    <uses-permission android:name="android.permission.BLUETOOTH_ADMIN" />
    <uses-permission android:name="android.permission.BLUETOOTH_CONNECT" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />

    <application>
        <activity
            android:name="com.aispeech.aimeeting.ui.FileUploadActivity"
            android:exported="false"
            android:theme="@style/Theme.Aimttablet" />
        <activity
            android:name="com.aispeech.aimeeting.ui.RecordDetailActivity"
            android:exported="false"
            android:label="@string/title_activity_record_detail"
            android:theme="@style/Theme.Aimttablet" />
        <activity
            android:name="com.aispeech.aimeeting.ui.RecordListActivity"
            android:exported="false"
            android:label="@string/title_activity_record_list"
            android:theme="@style/Theme.Aimttablet" />
        <activity
            android:name="com.aispeech.aimeeting.ui.RuntimeActivity"
            android:exported="false"
            android:label="@string/title_activity_runtime"
            android:theme="@style/Theme.Aimttablet" />

        <service
            android:name=".core.aiasrlib.service.AsrServiceAbility"
            android:exported="true"
            android:foregroundServiceType="microphone|phoneCall"
            android:permission="android.permission.FOREGROUND_SERVICE" />
    </application>

</manifest>