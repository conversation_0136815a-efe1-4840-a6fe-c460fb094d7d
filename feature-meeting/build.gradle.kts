/*
 * Copyright (C) 2023 AISPEECH. All rights reserved.
 *
 * This software is the confidential and proprietary information of AI Speech Co., Ltd..
 * Unauthorized copying, modification, publication, or use of this software, either
 * in part or in whole, is strictly prohibited without the prior written consent of AISPEE<PERSON>.
 *
 * For authorization inquiries, please contact AISPEECH at www.aispeech.com.
 */
plugins {
  id("aispeech.android.library")
  id("aispeech.android.library.compose")
  id("aispeech.android.hilt")
  id("aispeech.android.feature")
  id("aispeech.spotless")
  id("com.google.devtools.ksp")
}

android {
  namespace = "com.aispeech.tablet.feature.meeting"

  defaultConfig {
    testInstrumentationRunner = "androidx.test.runner.AndroidJUnitRunner"
  }
//
//  packaging {
//    jniLibs.excludes.add("lib/arm64-v8a/liblame-lib.so")
//    jniLibs.excludes.add("lib/armeabi-v7a/liblame-lib.so")
//  }
}


dependencies {
  ksp(libs.moshi.codegen)

  implementation(libs.androidx.appcompat)
  implementation(libs.androidx.core.ktx)
  implementation(libs.androidx.lifecycle.runtimeCompose)
  implementation(libs.androidx.lifecycle.viewModelCompose)
  implementation(libs.lazy.scroll.bar)
  implementation(project(":lib-markdowneditor"))
  implementation(project(":service-hybridspeech-common"))

//
//  implementation(libs.retrofit.core)
//  implementation(libs.moshi)
//  implementation(libs.okhttp.logging)
//  implementation(libs.retrofit.converter.moshi)
//  implementation(libs.retrofit.converter.scalars)

  compileOnly(libs.okhttp.logging)
  compileOnly(project(":core-common"))
  compileOnly(project(":core-model"))
  compileOnly(project(":core-db"))
  compileOnly(project(":core-network"))
  compileOnly(project(":core-meeting"))
  compileOnly(project(":core-preferences"))
  compileOnly(project(":core-designsystem"))
  implementation(project(":core-res"))
  compileOnly(project(":lib-connectivity"))
  compileOnly(project(":lib-notesync"))
  compileOnly(project(":lib-zip"))
  compileOnly(project(":lib-analyse"))
  compileOnly(project(":lib-clipboard"))
  compileOnly(project(":lib-meeting"))
  compileOnly(project(":lib-system"))
  compileOnly(project(":feature-ai"))
  compileOnly(project(":feature-login"))
  compileOnly(project(":feature-export"))
  compileOnly(project(":lib-oss"))
  compileOnly(project(":lib-duilite"))
  compileOnly(libs.coil.compose)
  compileOnly(libs.androidx.local.broadcast)
  implementation(libs.androidx.media3.exoplayer)
  implementation(libs.androidx.paging.compose)
  implementation(project(":lib-downloadmanager"))
  testImplementation(libs.junit4)
  testImplementation(project(":core-model"))
  testImplementation(project(":core-network"))
  testImplementation(project(":core-meeting"))
  androidTestImplementation(libs.androidx.test.core)
  androidTestImplementation(libs.androidx.test.runner)
  detektPlugins(libs.detekt.rules.compose)
}