# 离线引擎异步初始化功能

## 概述

为了提升用户体验，离线ASR引擎现在支持异步初始化。这意味着：

1. **非阻塞启动**：`OfflineOrchestrator.start()` 方法不再需要等待引擎初始化完成
2. **数据缓存**：初始化期间接收到的PCM数据会被自动缓存
3. **自动处理**：初始化完成后，缓存的数据会被自动发送给引擎处理

## 主要改进

### 之前的行为
```kotlin
// 旧的同步初始化方式
orchestrator.setConfig(modelPath)  // 阻塞直到初始化完成
orchestrator.start(pcmDataFlow)    // 只有在初始化完成后才能启动
```

### 现在的行为
```kotlin
// 新的异步初始化方式
orchestrator.setConfig(modelPath)  // 立即返回，后台异步初始化
orchestrator.start(pcmDataFlow)    // 可以立即启动，数据会被缓存
```

## 技术实现

### OfflineEngine 改进

1. **新增异步初始化方法**：
   ```kotlin
   fun initializeAsync(): Boolean
   ```

2. **PCM数据缓存机制**：
   ```kotlin
   private val initializationPcmBuffer = mutableListOf<ByteArray>()
   private val initializationBufferLock = Mutex()
   ```

3. **智能数据处理**：
   ```kotlin
   fun processPcmData(pcmData: ByteArray) {
       if (initializationInProgress) {
           // 缓存数据
           cacheData(pcmData)
       } else {
           // 直接处理
           processDirectly(pcmData)
       }
   }
   ```

### OfflineOrchestrator 改进

1. **支持初始化期间启动**：
   ```kotlin
   fun start(pcmDataFlow: SharedFlow<ByteArray>): Boolean {
       // 检查是否已配置（而不是是否已初始化）
       if (!initializationInProgress && !isEngineInitialized) {
           return false
       }
       // 可以在初始化期间启动
   }
   ```

2. **自动引擎启动**：
   ```kotlin
   // 监听初始化完成事件
   if (!wasInitialized && isEngineInitialized && isRunning) {
       offlineEngine.start() // 自动启动引擎
   }
   ```

## 使用方式

### 基本用法

```kotlin
val orchestrator = OfflineOrchestrator(context)
val pcmDataFlow = MutableSharedFlow<ByteArray>()

// 1. 设置配置（异步初始化开始）
orchestrator.setConfig("/path/to/model")

// 2. 立即启动处理（不需要等待初始化）
orchestrator.start(pcmDataFlow)

// 3. 开始发送音频数据（会被自动缓存或处理）
pcmDataFlow.emit(pcmData)
```

### 状态监控

```kotlin
// 检查是否可以启动
if (orchestrator.canStart()) {
    println("Engine is ready")
} else {
    println("Engine is still initializing")
}

// 监听转写结果
orchestrator.transcriptionResultFlow.collect { result ->
    println("Result: $result")
}
```

## 优势

1. **更好的用户体验**：用户不需要等待引擎初始化就可以开始录音
2. **无数据丢失**：初始化期间的音频数据不会丢失
3. **向后兼容**：保留了原有的 `initialize()` 方法
4. **线程安全**：使用 Mutex 确保缓存操作的线程安全

## 注意事项

1. **内存使用**：初始化期间会缓存PCM数据，需要注意内存使用
2. **初始化失败**：如果初始化失败，缓存的数据会被清空
3. **资源释放**：调用 `release()` 时会自动清理缓存和取消初始化任务

## 性能考虑

- **缓存大小**：建议控制初始化时间，避免缓存过多数据
- **内存管理**：缓存使用 `ByteArray.copyOf()` 确保数据安全
- **协程管理**：初始化任务在独立的协程中运行，不影响主线程

## 兼容性

- **向后兼容**：原有的 `initialize()` 方法仍然可用（标记为 @Deprecated）
- **API一致性**：外部接口保持不变，只是内部实现改为异步
- **错误处理**：保持原有的错误处理机制
