/**
 * 使用 Kotlin 协程替代 Handler 的示例
 * 
 * 这个示例展示了如何将基于 Handler 的超时机制重构为使用 Kotlin 协程的方式
 */

import kotlinx.coroutines.*
import java.util.concurrent.atomic.AtomicBoolean

class ServiceBindingManager {
    
    companion object {
        private const val BINDING_TIMEOUT = 5000L // 5秒超时
        private const val MAX_RETRY_COUNT = 3 // 最大重试次数
    }
    
    // 协程作用域用于管理异步操作
    private val serviceScope = CoroutineScope(SupervisorJob() + Dispatchers.Main)
    
    // 重试相关变量
    private val bindingFlag = AtomicBoolean(false)
    private var bindingRetryCount = MAX_RETRY_COUNT
    private var bindingTimeoutJob: Job? = null
    
    /**
     * 原来使用 Handler 的方式（不推荐）
     */
    /*
    private val handler = object : Handler(Looper.getMainLooper()) {
        override fun handleMessage(msg: Message) {
            when (msg.what) {
                MSG_BINDING_SERVICE_TIMEOUT -> {
                    Log.w(TAG, "Service binding timeout, retrying... (remaining: $bindingRetryCount)")
                    bindingFlag.set(false)
                    if (bindingRetryCount > 0) {
                        tryBindService()
                    } else {
                        Log.e(TAG, "Service binding failed after all retries")
                        _connectionState.value = false
                    }
                }
            }
        }
    }
    */
    
    /**
     * 使用 Kotlin 协程的方式（推荐）
     */
    private fun tryBindService(): Boolean {
        if (bindingFlag.getAndSet(true)) {
            println("Service binding already in progress")
            return false
        }

        // 取消之前的超时任务
        bindingTimeoutJob?.cancel()

        // 启动超时协程 - 替代 Handler 的超时消息
        bindingTimeoutJob = serviceScope.launch {
            delay(BINDING_TIMEOUT) // 替代 handler.sendEmptyMessageDelayed()
            
            // 超时处理 - 替代 handleMessage()
            println("Service binding timeout, retrying... (remaining: $bindingRetryCount)")
            bindingFlag.set(false)
            if (bindingRetryCount > 0) {
                tryBindService()
            } else {
                println("Service binding failed after all retries")
                // _connectionState.value = false
            }
        }

        return try {
            // 模拟服务绑定逻辑
            val bound = performServiceBinding()
            if (!bound) {
                println("bindService returned false")
                bindingFlag.set(false)
                bindingTimeoutJob?.cancel()
                bindingTimeoutJob = null
            }
            bindingRetryCount--
            bound
        } catch (e: Exception) {
            println("Error binding service: ${e.message}")
            bindingFlag.set(false)
            bindingTimeoutJob?.cancel()
            bindingTimeoutJob = null
            false
        }
    }
    
    /**
     * 服务连接成功时调用
     */
    private fun onServiceConnected() {
        println("Service connected")
        
        // 取消超时任务 - 替代 handler.removeMessages()
        bindingTimeoutJob?.cancel()
        bindingTimeoutJob = null
        
        bindingFlag.set(false)
        bindingRetryCount = MAX_RETRY_COUNT // 重置重试计数
        
        // 处理连接成功的逻辑
    }
    
    /**
     * 服务断开连接时调用
     */
    private fun onServiceDisconnected() {
        println("Service disconnected")
        bindingFlag.set(false)
        
        // 取消任何正在进行的超时任务
        bindingTimeoutJob?.cancel()
        bindingTimeoutJob = null
    }
    
    /**
     * 断开连接
     */
    fun disconnect() {
        // 清理超时任务和重试状态 - 替代 handler.removeMessages()
        bindingTimeoutJob?.cancel()
        bindingTimeoutJob = null
        bindingFlag.set(false)
        bindingRetryCount = MAX_RETRY_COUNT
        
        // 其他断开连接的逻辑
    }
    
    /**
     * 清理资源
     */
    fun cleanup() {
        disconnect()
        serviceScope.cancel() // 取消所有协程
    }
    
    // 模拟服务绑定
    private fun performServiceBinding(): Boolean {
        // 这里是实际的服务绑定逻辑
        return true
    }
}

/**
 * 使用协程的优势：
 * 
 * 1. 更简洁的代码：
 *    - 不需要定义消息常量 (MSG_BINDING_SERVICE_TIMEOUT)
 *    - 不需要 Handler 和 handleMessage() 方法
 *    - 超时逻辑直接在协程中处理
 * 
 * 2. 更好的可读性：
 *    - delay() 比 sendEmptyMessageDelayed() 更直观
 *    - 超时处理逻辑就在启动超时的地方附近
 * 
 * 3. 更容易管理：
 *    - Job.cancel() 比 removeMessages() 更明确
 *    - CoroutineScope 提供了统一的生命周期管理
 * 
 * 4. 更好的错误处理：
 *    - 协程的结构化并发提供了更好的异常处理
 *    - SupervisorJob 确保一个协程的失败不会影响其他协程
 * 
 * 5. 更现代的 Kotlin 风格：
 *    - 符合 Kotlin 协程的最佳实践
 *    - 更容易与其他协程代码集成
 */

fun main() {
    val manager = ServiceBindingManager()
    
    // 模拟使用
    manager.tryBindService()
    
    // 清理资源
    manager.cleanup()
}
