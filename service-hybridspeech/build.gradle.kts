/*
 * Copyright (C) 2023 AISPEECH. All rights reserved.
 *
 * This software is the confidential and proprietary information of AI Speech Co., Ltd..
 * Unauthorized copying, modification, publication, or use of this software, either
 * in part or in whole, is strictly prohibited without the prior written consent of <PERSON><PERSON><PERSON><PERSON>.
 *
 * For authorization inquiries, please contact AISPEECH at www.aispeech.com.
 */
import com.android.build.gradle.internal.api.ApkVariantOutputImpl
import java.util.Properties

plugins {
  id("aispeech.android.application")
  id("aispeech.android.application.compose")
  id("aispeech.spotless")
}

var storeType = "0"
val keystoreProperties = Properties()
file("../app/keystore.properties").inputStream().use { inputStream ->
  keystoreProperties.load(inputStream)
}

android {
  namespace = "com.aispeech.hybridspeech"
  compileSdk = Configurations.compileSdk

  signingConfigs {
    storeType = keystoreProperties.getProperty("KEY_STORE_TYPE")
    println("=======> storeType = $storeType")
    create("aispeech") {
      keyAlias = keystoreProperties.getProperty("keyAlias")
      keyPassword = keystoreProperties.getProperty("keyPassword")
      storeFile = file(keystoreProperties.getProperty("storeFile"))
      storePassword = keystoreProperties.getProperty("storePassword")
    }
    create("system-11") {
      keyAlias = keystoreProperties.getProperty("keyAlias-system")
      keyPassword = keystoreProperties.getProperty("keyPassword-system")
      storeFile = file(keystoreProperties.getProperty("storeFile-system-11"))
      storePassword = keystoreProperties.getProperty("storePassword-system")
    }
    create("system-13") {
      keyAlias = keystoreProperties.getProperty("keyAlias-system")
      keyPassword = keystoreProperties.getProperty("keyPassword-system")
      storeFile = file(keystoreProperties.getProperty("storeFile-system-13"))
      storePassword = keystoreProperties.getProperty("storePassword-system")
    }
  }

  buildTypes {
    getByName("release") {
      signingConfig = signingConfigs.getByName(
        when (storeType) {
          "1" -> "system-11"
          "2" -> "system-13"
          else -> "aispeech"
        }
      )
    }
    getByName("debug") {
      signingConfig = signingConfigs.getByName(
        when (storeType) {
          "1" -> "system-11"
          "2" -> "system-13"
          else -> "aispeech"
        }
      )
    }
  }

  // 输出类型
  android.applicationVariants.all {
    val buildType = this.buildType.name
    outputs.all {
      if (this is ApkVariantOutputImpl) {
        this.outputFileName =
          "hybridspeech_v${defaultConfig.versionName}_${buildType}_${properties["environment"].toString()}.apk"
      }
    }
  }

  buildFeatures {
    buildConfig = true
    aidl = true
    compose = true
  }

  defaultConfig {
    manifestPlaceholders += mapOf()
    applicationId = "com.aispeech.hybridspeech"
    minSdk = Configurations.minSdk
    targetSdk = Configurations.targetSdk
    versionCode = properties["buildNumber"].toString().toInt()
    versionName = properties["appVersion"] as String? + "." + properties["buildNumber"] as String?
    buildConfigField("String", "environment", "\"${properties["environment"].toString()}\"")
    manifestPlaceholders.putAll(
      mapOf("SHARED_USER_ID" to when (keystoreProperties.getProperty("KEY_STORE_TYPE")) {
        "1", "2" -> "android.uid.system"
        else -> ""
      })
    )
    ndk {
      abiFilters.addAll(listOf("arm64-v8a", "armeabi-v7a"))
    }
    vectorDrawables {
      useSupportLibrary = true
    }
  }

  packaging {
    resources.excludes += setOf("META-INF/DEPENDENCIES", "META-INF/LICENSE", "META-INF/ASL2.0")
  }
  compileOptions {
    sourceCompatibility = JavaVersion.VERSION_1_8
    targetCompatibility = JavaVersion.VERSION_1_8
  }
  kotlinOptions {
    jvmTarget = "1.8"
  }
}

dependencies {

  implementation(fileTree(mapOf("dir" to "libs", "include" to listOf("*.jar"))))

  // core modules
  implementation(project(":core-designsystem"))
  implementation(project(":core-navigation"))
  implementation(project(":core-model"))
  implementation(project(":core-common"))
  implementation(project(":core-network"))
  implementation(project(":lib-lame"))
  implementation(project(":service-hybridspeech-common"))

  // material
  implementation(libs.androidx.appcompat)

  // compose
  implementation(libs.androidx.activity.compose)
  implementation(libs.androidx.compose.runtime)
  implementation(libs.androidx.compose.ui.tooling)
  implementation(libs.androidx.compose.ui.tooling.preview)
  implementation(libs.androidx.compose.constraintlayout)

  // jetpack
  implementation(libs.androidx.recyclerview)
  implementation(libs.androidx.ui.unit.android)
  implementation(libs.androidx.ui.text.android)
  implementation(libs.kotlinx.datetime)
  implementation(project(":lib-statusbar"))
  implementation(project(":lib-system"))

  implementation("io.ktor:ktor-client-core:2.3.11")
  implementation("io.ktor:ktor-client-cio:2.3.11")
  implementation("io.ktor:ktor-client-websockets:2.3.11")

  implementation(libs.kotlinx.coroutines.core)
  implementation(libs.kotlinx.coroutines.android)

  implementation("com.squareup.okhttp3:okhttp:3.12.12")
}