package com.aispeech.hybridspeech.asr.online

import kotlinx.coroutines.*
import kotlinx.coroutines.test.*
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.TestInstance
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.CountDownLatch
import java.util.concurrent.atomic.AtomicInteger
import java.util.concurrent.atomic.AtomicReference
import kotlin.random.Random

/**
 * Tests for concurrent state updates to ensure consistency and thread safety
 * 
 * This test suite verifies that:
 * 1. Concurrent update attempts maintain state consistency
 * 2. Compare-and-set operations work correctly under contention
 * 3. State invariants are preserved during concurrent modifications
 * 4. No race conditions occur during rapid state changes
 */
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class OnlineAsrStateConcurrencyTest {
    
    private lateinit var stateHolder: StateHolder
    
    @BeforeEach
    fun setup() {
        stateHolder = StateHolder(OrchestratorFlags.initial())
    }
    
    @Test
    fun `concurrent updates maintain state consistency`() = runTest {
        val numCoroutines = 50
        val updatesPerCoroutine = 100
        val allStatesObserved = ConcurrentHashMap<OrchestratorFlags, AtomicInteger>()
        
        // Launch multiple coroutines that perform rapid state updates
        val jobs = (1..numCoroutines).map { coroutineId ->
            launch {
                repeat(updatesPerCoroutine) { updateId ->
                    // Perform different types of state updates
                    when (updateId % 5) {
                        0 -> stateHolder.updateStateWithRetry { state ->
                            val newState = state.withRunning(true)
                            allStatesObserved.computeIfAbsent(newState) { AtomicInteger(0) }.incrementAndGet()
                            newState
                        }
                        1 -> stateHolder.updateStateWithRetry { state ->
                            val newState = state.withNetwork(Random.nextBoolean())
                            allStatesObserved.computeIfAbsent(newState) { AtomicInteger(0) }.incrementAndGet()
                            newState
                        }
                        2 -> stateHolder.updateStateWithRetry { state ->
                            val newState = state.withReadyState(OnlineAsrReadyState.values().random())
                            allStatesObserved.computeIfAbsent(newState) { AtomicInteger(0) }.incrementAndGet()
                            newState
                        }
                        3 -> stateHolder.updateStateWithRetry { state ->
                            val newState = if (state.canPause()) state.withPause(true) else state
                            allStatesObserved.computeIfAbsent(newState) { AtomicInteger(0) }.incrementAndGet()
                            newState
                        }
                        4 -> stateHolder.updateStateWithRetry { state ->
                            val newState = if (state.canResume()) state.withPause(false) else state
                            allStatesObserved.computeIfAbsent(newState) { AtomicInteger(0) }.incrementAndGet()
                            newState
                        }
                    }
                }
            }
        }
        
        // Wait for all updates to complete
        jobs.joinAll()
        
        // Verify final state is valid
        val finalState = stateHolder.current
        assertNotNull(finalState)
        
        // Verify that all observed states are valid (passed validation)
        val totalStateObservations = allStatesObserved.values.sumOf { it.get() }
        assertTrue(totalStateObservations > 0, "Should have observed state changes")
        
        // All states should have passed invariant validation since updateStateWithRetry calls validate()
        allStatesObserved.keys.forEach { state ->
            assertValidState(state)
        }
    }
    
    @Test
    fun `compare-and-set operations handle contention correctly`() = runTest {
        val numAttempts = 1000
        val successfulUpdates = AtomicInteger(0)
        val failedUpdates = AtomicInteger(0)
        
        // Launch multiple coroutines trying to update to different running states
        val jobs = (1..20).map { coroutineId ->
            launch {
                repeat(50) {
                    val success = stateHolder.updateState { state ->
                        state.withRunning(coroutineId % 2 == 0)
                    }
                    
                    if (success) {
                        successfulUpdates.incrementAndGet()
                    } else {
                        failedUpdates.incrementAndGet()
                    }
                }
            }
        }
        
        jobs.joinAll()
        
        // Should have exactly numAttempts attempts total
        assertEquals(numAttempts, successfulUpdates.get() + failedUpdates.get())
        
        // Should have some successful updates (not all should fail due to contention)
        assertTrue(successfulUpdates.get() > 0, "Should have some successful updates")
        
        // Final state should be valid
        assertValidState(stateHolder.current)
    }
    
    @Test
    fun `rapid network state changes maintain consistency`() = runTest {
        val latch = CountDownLatch(1)
        val states = mutableListOf<OrchestratorFlags>()
        
        // Start background collection of states
        val collectionJob = launch {
            latch.await()
            repeat(500) {
                states.add(stateHolder.current)
                delay(1) // Small delay to observe intermediate states
            }
        }
        
        // Rapid network state changes
        val updateJob = launch {
            latch.countDown()
            repeat(100) {
                stateHolder.updateStateWithRetry { state ->
                    state.withNetwork(it % 2 == 0)
                }
                yield() // Allow other coroutines to run
            }
        }
        
        joinAll(collectionJob, updateJob)
        
        // Verify all observed states are valid
        assertTrue(states.isNotEmpty(), "Should have observed states")
        states.forEach { state ->
            assertValidState(state)
        }
        
        // Verify network state transitions are logical
        var networkTransitions = 0
        for (i in 1 until states.size) {
            if (states[i-1].isNetworkAvailable != states[i].isNetworkAvailable) {
                networkTransitions++
            }
        }
        assertTrue(networkTransitions > 0, "Should have observed network state transitions")
    }
    
    @Test
    fun `conditional updates work correctly under contention`() = runTest {
        // Set initial running state
        stateHolder.updateStateWithRetry { it.withRunning(true) }
        
        val pauseAttempts = AtomicInteger(0)
        val pauseSuccesses = AtomicInteger(0)
        val resumeAttempts = AtomicInteger(0)
        val resumeSuccesses = AtomicInteger(0)
        
        val jobs = (1..30).map { coroutineId ->
            launch {
                repeat(50) {
                    if (coroutineId % 2 == 0) {
                        // Try to pause
                        pauseAttempts.incrementAndGet()
                        val success = stateHolder.updateStateIf(
                            predicate = { it.canPause() },
                            transform = { it.withPause(true) }
                        )
                        if (success) pauseSuccesses.incrementAndGet()
                    } else {
                        // Try to resume
                        resumeAttempts.incrementAndGet()
                        val success = stateHolder.updateStateIf(
                            predicate = { it.canResume() },
                            transform = { it.withPause(false) }
                        )
                        if (success) resumeSuccesses.incrementAndGet()
                    }
                    yield()
                }
            }
        }
        
        jobs.joinAll()
        
        // Should have attempted many operations
        assertTrue(pauseAttempts.get() > 0)
        assertTrue(resumeAttempts.get() > 0)
        
        // Should have some successes (depending on timing and state)
        assertTrue(pauseSuccesses.get() + resumeSuccesses.get() > 0, 
                  "Should have some successful conditional updates")
        
        // Final state should be valid
        assertValidState(stateHolder.current)
    }
    
    @Test
    fun `state reset during concurrent operations maintains consistency`() = runTest {
        val resetCount = AtomicInteger(0)
        val updateCount = AtomicInteger(0)
        
        val jobs = (1..20).map { coroutineId ->
            launch {
                repeat(100) {
                    if (coroutineId == 1 && it % 50 == 0) {
                        // Periodically reset state
                        stateHolder.reset()
                        resetCount.incrementAndGet()
                    } else {
                        // Regular updates
                        stateHolder.updateStateWithRetry { state ->
                            updateCount.incrementAndGet()
                            state.withNetwork(Random.nextBoolean())
                        }
                    }
                    yield()
                }
            }
        }
        
        jobs.joinAll()
        
        assertTrue(resetCount.get() > 0, "Should have performed resets")
        assertTrue(updateCount.get() > 0, "Should have performed updates")
        
        // After all operations, state should be valid
        assertValidState(stateHolder.current)
    }
    
    @Test
    fun `memory consistency during high frequency updates`() = runTest {
        val updateCounter = AtomicInteger(0)
        val lastObservedStates = Array<AtomicReference<OrchestratorFlags>>(10) { 
            AtomicReference(stateHolder.current) 
        }
        
        // High frequency updates
        val updateJob = launch {
            repeat(1000) { iteration ->
                stateHolder.updateStateWithRetry { state ->
                    updateCounter.incrementAndGet()
                    // Cycle through different ready states
                    state.withReadyState(OnlineAsrReadyState.values()[iteration % OnlineAsrReadyState.values().size])
                }
            }
        }
        
        // Multiple observers reading state
        val observerJobs = (0..9).map { observerId ->
            launch {
                repeat(500) {
                    val currentState = stateHolder.current
                    lastObservedStates[observerId].set(currentState)
                    delay(1)
                }
            }
        }
        
        joinAll(updateJob, *observerJobs.toTypedArray())
        
        // All observers should have seen valid states
        lastObservedStates.forEach { stateRef ->
            assertValidState(stateRef.get())
        }
        
        assertEquals(1000, updateCounter.get(), "All updates should have completed")
    }
    
    private fun assertValidState(state: OrchestratorFlags) {
        // Verify state invariants that should always hold
        
        // Invariant: needsResumeOnReady can be true only when readyState != READY
        if (state.needsResumeOnReady) {
            assertNotEquals(OnlineAsrReadyState.READY, state.readyState,
                           "needsResumeOnReady cannot be true when readyState is READY")
        }
        
        // Invariant: shouldRetryOnNetworkRestore should be false when network is available and connected
        if (state.shouldRetryOnNetworkRestore && state.isNetworkAvailable) {
            assertFalse(state.readyState in listOf(OnlineAsrReadyState.CONNECTED, OnlineAsrReadyState.READY),
                       "shouldRetryOnNetworkRestore should be false when network is available and connected")
        }
        
        // Invariant: cannot be paused when not running
        if (state.isPausedByUser) {
            assertTrue(state.isRunning, "Cannot be paused when not running")
        }
        
        // Invariant: readyState should be NOT_CONNECTED when not running
        if (!state.isRunning) {
            assertEquals(OnlineAsrReadyState.NOT_CONNECTED, state.readyState,
                        "readyState should be NOT_CONNECTED when not running")
        }
    }
}
