package com.aispeech.hybridspeech.asr.online

import kotlinx.coroutines.*
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.TestInstance

/**
 * Regression tests using recorded scenarios to verify state behavior
 * 
 * This test suite contains recorded scenarios that have been observed
 * in production or development, ensuring that previously identified
 * issues do not regress.
 */
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class RegressionScenariosTest {
    
    private lateinit var stateHolder: StateHolder
    
    @BeforeEach
    fun setup() {
        stateHolder = StateHolder(OrchestratorFlags.initial())
    }
    
    @Test
    fun `scenario 1 - rapid connection failures with retry attempts`() {
        // This scenario was observed when network was unstable and connection
        // attempts repeatedly failed, leading to incorrect retry flag states
        
        val states = mutableListOf<OrchestratorFlags>()
        
        // Start ASR
        stateHolder.updateStateWithRetry { it.withRunning(true) }
        states.add(stateHolder.current)
        
        // Connection attempt fails
        stateHolder.updateStateWithRetry { it.onConnectionError() }
        states.add(stateHolder.current)
        assertEquals(OnlineAsrReadyState.ERROR, stateHolder.current.readyState)
        
        // Network becomes unavailable
        stateHolder.updateStateWithRetry { it.onNetworkLost() }
        states.add(stateHolder.current)
        assertFalse(stateHolder.current.isNetworkAvailable)
        assertTrue(stateHolder.current.shouldRetryOnNetworkRestore)
        
        // Network restored
        stateHolder.updateStateWithRetry { it.onNetworkRestored() }
        states.add(stateHolder.current)
        assertTrue(stateHolder.current.isNetworkAvailable)
        assertFalse(stateHolder.current.shouldRetryOnNetworkRestore, 
                   "Retry flag should be cleared when network is restored")
        
        // Another connection attempt
        stateHolder.updateStateWithRetry { it.onConnected() }
        states.add(stateHolder.current)
        assertEquals(OnlineAsrReadyState.CONNECTED, stateHolder.current.readyState)
        
        // Verify all states were valid
        states.forEach { state ->
            assertValidState(state)
        }
    }
    
    @Test
    fun `scenario 2 - pause during connection establishment`() {
        // This scenario tests pausing while connection is being established
        
        // Start ASR
        stateHolder.updateStateWithRetry { it.withRunning(true) }
        assertTrue(stateHolder.current.isRunning)
        
        // Begin connection
        stateHolder.updateStateWithRetry { it.withReadyState(OnlineAsrReadyState.CONNECTING) }
        assertEquals(OnlineAsrReadyState.CONNECTING, stateHolder.current.readyState)
        
        // Pause while connecting
        stateHolder.updateStateWithRetry { it.withPause(true) }
        assertTrue(stateHolder.current.isPausedByUser)
        assertTrue(stateHolder.current.isRunning, "Should still be running when paused")
        
        // Connection succeeds while paused
        stateHolder.updateStateWithRetry { it.onConnected() }
        assertEquals(OnlineAsrReadyState.CONNECTED, stateHolder.current.readyState)
        assertTrue(stateHolder.current.isPausedByUser, "Should remain paused")
        
        // Resume
        stateHolder.updateStateWithRetry { it.withPause(false) }
        assertFalse(stateHolder.current.isPausedByUser)
        assertEquals(OnlineAsrReadyState.CONNECTED, stateHolder.current.readyState)
    }
    
    @Test
    fun `scenario 3 - stop during active resume session`() {
        // This scenario tests stopping ASR while a resume session is active
        
        // Start with resume needed
        stateHolder.updateStateWithRetry { 
            it.withRunning(true)
              .withReadyState(OnlineAsrReadyState.CONNECTED)
              .withResumeOnReady(true)
        }
        
        assertTrue(stateHolder.current.needsResumeOnReady)
        assertEquals(OnlineAsrReadyState.CONNECTED, stateHolder.current.readyState)
        
        // Become ready
        stateHolder.updateStateWithRetry { it.onReady() }
        assertEquals(OnlineAsrReadyState.READY, stateHolder.current.readyState)
        assertFalse(stateHolder.current.needsResumeOnReady, 
                   "Resume flag should be cleared when ready")
        
        // Stop ASR
        stateHolder.updateStateWithRetry { it.withRunning(false) }
        assertFalse(stateHolder.current.isRunning)
        assertEquals(OnlineAsrReadyState.NOT_CONNECTED, stateHolder.current.readyState)
        assertFalse(stateHolder.current.needsResumeOnReady)
        assertFalse(stateHolder.current.shouldRetryOnNetworkRestore)
        assertFalse(stateHolder.current.isPausedByUser)
    }
    
    @Test
    fun `scenario 4 - network flapping during resume transmission`() {
        // This scenario tests network flapping while resume transmission is active
        
        // Start with resume needed and ready state
        stateHolder.updateStateWithRetry { 
            it.withRunning(true)
              .withReadyState(OnlineAsrReadyState.CONNECTED)
              .withResumeOnReady(true)
        }
        
        val states = mutableListOf<OrchestratorFlags>()
        
        // Network flapping starts
        repeat(5) {
            stateHolder.updateStateWithRetry { it.onNetworkLost() }
            states.add(stateHolder.current)
            assertFalse(stateHolder.current.isNetworkAvailable)
            
            stateHolder.updateStateWithRetry { it.onNetworkRestored() }
            states.add(stateHolder.current)
            assertTrue(stateHolder.current.isNetworkAvailable)
        }
        
        // Should maintain running state throughout
        states.forEach { state ->
            assertTrue(state.isRunning, "Should remain running during network flapping")
            assertValidState(state)
        }
    }
    
    @Test
    fun `scenario 5 - concurrent pause and network state changes`() {
        // This scenario tests concurrent modifications to pause and network state
        
        runBlocking {
            stateHolder.updateStateWithRetry { it.withRunning(true) }
            
            val jobs = listOf(
                // Job 1: Rapid pause/resume
                launch {
                    repeat(10) {
                        stateHolder.updateStateWithRetry { it.withPause(true) }
                        delay(50)
                        stateHolder.updateStateWithRetry { it.withPause(false) }
                        delay(50)
                    }
                },
                // Job 2: Network flapping
                launch {
                    repeat(10) {
                        stateHolder.updateStateWithRetry { it.withNetwork(false) }
                        delay(75)
                        stateHolder.updateStateWithRetry { it.withNetwork(true) }
                        delay(75)
                    }
                }
            )
            
            jobs.joinAll()
            
            // Final state should be valid
            assertValidState(stateHolder.current)
            assertTrue(stateHolder.current.isRunning)
        }
    }
    
    @Test
    fun `scenario 6 - error recovery with retry attempts`() {
        // This scenario tests error recovery when retry attempts are made
        
        // Start and connect
        stateHolder.updateStateWithRetry { 
            it.withRunning(true)
              .onConnected()
              .onReady()
        }
        assertEquals(OnlineAsrReadyState.READY, stateHolder.current.readyState)
        
        // Connection error occurs
        stateHolder.updateStateWithRetry { it.onConnectionError() }
        assertEquals(OnlineAsrReadyState.ERROR, stateHolder.current.readyState)
        
        // Network becomes unavailable
        stateHolder.updateStateWithRetry { it.onNetworkLost() }
        assertFalse(stateHolder.current.isNetworkAvailable)
        assertTrue(stateHolder.current.shouldRetryOnNetworkRestore,
                  "Should set retry flag when network lost in error state")
        
        // Multiple network restoration attempts
        repeat(3) {
            stateHolder.updateStateWithRetry { it.onNetworkRestored() }
            assertTrue(stateHolder.current.isNetworkAvailable)
            assertFalse(stateHolder.current.shouldRetryOnNetworkRestore)
            
            // Simulate retry attempt that fails
            stateHolder.updateStateWithRetry { it.onConnectionError() }
            stateHolder.updateStateWithRetry { it.onNetworkLost() }
        }
        
        // Final successful connection
        stateHolder.updateStateWithRetry { it.onNetworkRestored() }
        stateHolder.updateStateWithRetry { 
            it.withReadyState(OnlineAsrReadyState.CONNECTING)
              .onConnected()
              .onReady()
        }
        
        assertEquals(OnlineAsrReadyState.READY, stateHolder.current.readyState)
        assertTrue(stateHolder.current.isNetworkAvailable)
        assertFalse(stateHolder.current.shouldRetryOnNetworkRestore)
    }
    
    @Test
    fun `scenario 7 - audio type change during operation`() {
        // This scenario tests changing audio type during operation
        
        // Start with Opus
        stateHolder.updateStateWithRetry { 
            it.withRunning(true)
              .withAudioType(AudioType.OGG_OPUS)
              .onConnected()
        }
        assertEquals(AudioType.OGG_OPUS, stateHolder.current.audioType)
        assertEquals(OnlineAsrReadyState.CONNECTED, stateHolder.current.readyState)
        
        // Change to MP3
        stateHolder.updateStateWithRetry { it.withAudioType(AudioType.MP3) }
        assertEquals(AudioType.MP3, stateHolder.current.audioType)
        assertTrue(stateHolder.current.isRunning, "Should remain running after audio type change")
        
        // State should remain valid
        assertValidState(stateHolder.current)
    }
    
    @Test
    fun `scenario 8 - resume state transitions with various ready states`() {
        // This scenario tests resume behavior across different ready states
        
        val baseState = OrchestratorFlags.running().withResumeOnReady(true)
        
        // Test resume flag behavior with each ready state
        OnlineAsrReadyState.values().forEach { readyState ->
            val newState = baseState.withReadyState(readyState)
            
            when (readyState) {
                OnlineAsrReadyState.NOT_CONNECTED,
                OnlineAsrReadyState.ERROR,
                OnlineAsrReadyState.CLOSED -> {
                    assertFalse(newState.needsResumeOnReady,
                               "Resume flag should be cleared for $readyState")
                }
                OnlineAsrReadyState.CONNECTING,
                OnlineAsrReadyState.CONNECTED -> {
                    assertTrue(newState.needsResumeOnReady,
                              "Resume flag should be preserved for $readyState")
                }
                OnlineAsrReadyState.READY -> {
                    assertFalse(newState.needsResumeOnReady,
                               "Resume flag should be cleared for READY due to invariant")
                }
            }
            
            assertValidState(newState)
        }
    }
    
    private fun assertValidState(state: OrchestratorFlags) {
        // Verify state invariants hold
        if (state.needsResumeOnReady) {
            assertNotEquals(OnlineAsrReadyState.READY, state.readyState,
                           "needsResumeOnReady cannot be true when readyState is READY")
        }
        
        if (state.shouldRetryOnNetworkRestore && state.isNetworkAvailable) {
            assertFalse(state.readyState in listOf(OnlineAsrReadyState.CONNECTED, OnlineAsrReadyState.READY),
                       "shouldRetryOnNetworkRestore should be false when connected and network available")
        }
        
        if (state.isPausedByUser) {
            assertTrue(state.isRunning, "Cannot be paused when not running")
        }
        
        if (!state.isRunning) {
            assertEquals(OnlineAsrReadyState.NOT_CONNECTED, state.readyState,
                        "readyState should be NOT_CONNECTED when not running")
        }
    }
}
