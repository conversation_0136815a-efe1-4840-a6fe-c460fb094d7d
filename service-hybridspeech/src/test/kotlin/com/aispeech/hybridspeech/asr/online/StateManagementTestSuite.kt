package com.aispeech.hybridspeech.asr.online

import org.junit.platform.suite.api.SelectClasses
import org.junit.platform.suite.api.Suite

/**
 * Test suite for running all state management tests together
 * 
 * This suite includes:
 * 1. OnlineAsrStateConcurrencyTest - Tests for concurrent state updates and consistency
 * 2. StateTransitionValidationTest - Tests for illegal transitions and invariant validation
 * 3. NetworkFlapPauseResumeTest - Tests for network flapping and pause/resume behavior
 * 4. RegressionScenariosTest - Regression tests with recorded scenarios
 * 5. OrchestratorIntegrationTest - Integration tests for complete orchestrator functionality
 * 
 * To run this test suite:
 * ./gradlew test --tests com.aispeech.hybridspeech.asr.online.StateManagementTestSuite
 */
@Suite
@SelectClasses(
    OnlineAsrStateConcurrencyTest::class,
    StateTransitionValidationTest::class,
    NetworkFlapPauseResumeTest::class,
    RegressionScenariosTest::class,
    OrchestratorIntegrationTest::class
)
class StateManagementTestSuite
