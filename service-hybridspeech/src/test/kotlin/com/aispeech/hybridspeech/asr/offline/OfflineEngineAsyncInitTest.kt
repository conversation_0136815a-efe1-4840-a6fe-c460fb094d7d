package com.aispeech.hybridspeech.asr.offline

import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.runBlocking
import org.junit.Test
import org.junit.Assert.*

/**
 * 测试 OfflineEngine 异步初始化和PCM数据缓存功能
 */
class OfflineEngineAsyncInitTest {

    @Test
    fun `test async initialization with PCM data caching`() = runBlocking {
        // 创建模拟的PCM数据流
        val pcmDataFlow = MutableSharedFlow<ByteArray>()
        
        // 创建OfflineOrchestrator
        val orchestrator = OfflineOrchestrator(mockContext())
        
        // 设置配置，这会启动异步初始化
        val configResult = orchestrator.setConfig("/mock/model/path")
        assertTrue("Config should be set successfully", configResult)
        
        // 立即启动orchestrator，即使引擎还在初始化
        val startResult = orchestrator.start(pcmDataFlow)
        assertTrue("Orchestrator should start even during initialization", startResult)
        
        // 发送一些PCM数据，这些数据应该被缓存
        val testPcmData1 = ByteArray(640) { it.toByte() }
        val testPcmData2 = ByteArray(640) { (it + 100).toByte() }
        
        pcmDataFlow.emit(testPcmData1)
        pcmDataFlow.emit(testPcmData2)
        
        // 等待一段时间让初始化完成
        delay(2000)
        
        // 验证引擎状态
        assertTrue("Engine should be initialized after delay", orchestrator.canStart())
        
        // 清理
        orchestrator.release()
    }
    
    @Test
    fun `test start during initialization vs after initialization`() = runBlocking {
        val pcmDataFlow = MutableSharedFlow<ByteArray>()
        val orchestrator = OfflineOrchestrator(mockContext())
        
        // 测试在初始化期间启动
        orchestrator.setConfig("/mock/model/path")
        val startDuringInit = orchestrator.start(pcmDataFlow)
        assertTrue("Should be able to start during initialization", startDuringInit)
        
        // 等待初始化完成
        delay(1000)
        
        // 测试在初始化完成后启动
        orchestrator.stop()
        val startAfterInit = orchestrator.start(pcmDataFlow)
        assertTrue("Should be able to start after initialization", startAfterInit)
        
        orchestrator.release()
    }
    
    @Test
    fun `test PCM data processing during initialization`() = runBlocking {
        val pcmDataFlow = MutableSharedFlow<ByteArray>()
        val orchestrator = OfflineOrchestrator(mockContext())
        
        // 启动配置和处理
        orchestrator.setConfig("/mock/model/path")
        orchestrator.start(pcmDataFlow)
        
        // 在初始化期间发送多个PCM数据包
        repeat(10) { index ->
            val pcmData = ByteArray(640) { (index * 10 + it).toByte() }
            pcmDataFlow.emit(pcmData)
            delay(50) // 模拟实时音频流
        }
        
        // 等待初始化完成
        delay(2000)
        
        // 继续发送数据，这些应该直接处理
        repeat(5) { index ->
            val pcmData = ByteArray(640) { (index * 20 + it).toByte() }
            pcmDataFlow.emit(pcmData)
            delay(50)
        }
        
        orchestrator.release()
    }
    
    private fun mockContext(): android.content.Context {
        // 在实际测试中，这里应该返回一个模拟的Context
        // 为了简化，这里返回null，实际使用时需要适当的模拟
        return org.mockito.Mockito.mock(android.content.Context::class.java)
    }
}
