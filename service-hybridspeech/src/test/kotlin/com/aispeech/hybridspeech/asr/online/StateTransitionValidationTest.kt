package com.aispeech.hybridspeech.asr.online

import org.junit.jupiter.api.Test
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.api.TestInstance

/**
 * Tests for illegal state transitions and validation of state invariants
 * 
 * This test suite verifies that:
 * 1. Illegal state transitions are rejected with proper exceptions
 * 2. State invariants are enforced correctly
 * 3. Validation catches inconsistent states
 * 4. Edge cases in state transitions are handled properly
 */
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class StateTransitionValidationTest {
    
    private lateinit var stateHolder: StateHolder
    
    @BeforeEach
    fun setup() {
        stateHolder = StateHolder(OrchestratorFlags.initial())
    }
    
    @Test
    fun `reject needsResumeOnReady true when readyState is READY`() {
        // This should throw an exception because needsResumeOnReady cannot be true when readyState is READY
        assertThrows<IllegalArgumentException> {
            stateHolder.setState(
                OrchestratorFlags(
                    isRunning = true,
                    needsResumeOnReady = true,
                    readyState = OnlineAsrReadyState.READY
                )
            )
        }
    }
    
    @Test
    fun `reject shouldRetryOnNetworkRestore when network available and connected`() {
        // This should throw because shouldRetryOnNetworkRestore should be false when connected
        assertThrows<IllegalArgumentException> {
            stateHolder.setState(
                OrchestratorFlags(
                    isRunning = true,
                    isNetworkAvailable = true,
                    shouldRetryOnNetworkRestore = true,
                    readyState = OnlineAsrReadyState.CONNECTED
                )
            )
        }
        
        // Same for READY state
        assertThrows<IllegalArgumentException> {
            stateHolder.setState(
                OrchestratorFlags(
                    isRunning = true,
                    isNetworkAvailable = true,
                    shouldRetryOnNetworkRestore = true,
                    readyState = OnlineAsrReadyState.READY
                )
            )
        }
    }
    
    @Test
    fun `reject paused state when not running`() {
        // Cannot be paused when not running
        assertThrows<IllegalArgumentException> {
            stateHolder.setState(
                OrchestratorFlags(
                    isRunning = false,
                    isPausedByUser = true
                )
            )
        }
    }
    
    @Test
    fun `reject non-NOT_CONNECTED readyState when not running`() {
        // readyState should be NOT_CONNECTED when not running
        OnlineAsrReadyState.values().filter { it != OnlineAsrReadyState.NOT_CONNECTED }.forEach { readyState ->
            assertThrows<IllegalArgumentException>("Should reject readyState $readyState when not running") {
                stateHolder.setState(
                    OrchestratorFlags(
                        isRunning = false,
                        readyState = readyState
                    )
                )
            }
        }
    }
    
    @Test
    fun `validate transition method enforces invariants`() {
        // Set valid initial state
        stateHolder.setState(OrchestratorFlags.running())
        
        // Try to create invalid transition
        assertThrows<IllegalArgumentException> {
            stateHolder.transition("test invalid") { currentState ->
                currentState.copy(
                    needsResumeOnReady = true,
                    readyState = OnlineAsrReadyState.READY
                )
            }
        }
    }
    
    @Test
    fun `extension functions respect invariants`() {
        val baseState = OrchestratorFlags.running()
        
        // onReady() should clear needsResumeOnReady
        val readyState = baseState.copy(needsResumeOnReady = true).onReady()
        assertEquals(OnlineAsrReadyState.READY, readyState.readyState)
        assertFalse(readyState.needsResumeOnReady, "needsResumeOnReady should be cleared when transitioning to READY")
        
        // withRunning(false) should reset multiple flags
        val stoppedState = baseState.copy(
            isPausedByUser = true,
            needsResumeOnReady = true,
            shouldRetryOnNetworkRestore = true,
            readyState = OnlineAsrReadyState.CONNECTED
        ).withRunning(false)
        
        assertFalse(stoppedState.isRunning)
        assertFalse(stoppedState.isPausedByUser)
        assertFalse(stoppedState.needsResumeOnReady)
        assertFalse(stoppedState.shouldRetryOnNetworkRestore)
        assertEquals(OnlineAsrReadyState.NOT_CONNECTED, stoppedState.readyState)
    }
    
    @Test
    fun `withPause correctly handles resume flag`() {
        val runningState = OrchestratorFlags.running().copy(needsResumeOnReady = true)
        
        // Pausing should clear needsResumeOnReady
        val pausedState = runningState.withPause(true)
        assertTrue(pausedState.isPausedByUser)
        assertFalse(pausedState.needsResumeOnReady, "needsResumeOnReady should be cleared when pausing")
        
        // Unpausing should not automatically set needsResumeOnReady
        val unpausedState = pausedState.withPause(false)
        assertFalse(unpausedState.isPausedByUser)
        assertFalse(unpausedState.needsResumeOnReady, "needsResumeOnReady should remain false when unpausing")
    }
    
    @Test
    fun `withReadyState clears resume flag for certain states`() {
        val baseState = OrchestratorFlags.running().copy(needsResumeOnReady = true)
        
        // These states should clear needsResumeOnReady
        val clearedStates = listOf(
            OnlineAsrReadyState.NOT_CONNECTED,
            OnlineAsrReadyState.ERROR,
            OnlineAsrReadyState.CLOSED
        )
        
        clearedStates.forEach { readyState ->
            val newState = baseState.withReadyState(readyState)
            assertFalse(newState.needsResumeOnReady, 
                       "needsResumeOnReady should be cleared for readyState $readyState")
        }
        
        // These states should preserve needsResumeOnReady
        val preservedStates = listOf(
            OnlineAsrReadyState.CONNECTING,
            OnlineAsrReadyState.CONNECTED,
            OnlineAsrReadyState.READY
        )
        
        preservedStates.forEach { readyState ->
            val newState = baseState.withReadyState(readyState)
            // For READY state, needsResumeOnReady should be cleared due to invariant
            if (readyState == OnlineAsrReadyState.READY) {
                assertFalse(newState.needsResumeOnReady, 
                           "needsResumeOnReady should be cleared for READY state")
            } else {
                assertTrue(newState.needsResumeOnReady, 
                          "needsResumeOnReady should be preserved for readyState $readyState")
            }
        }
    }
    
    @Test
    fun `withNetwork clears retry flag when network becomes available`() {
        val baseState = OrchestratorFlags(
            isRunning = true,
            isNetworkAvailable = false,
            shouldRetryOnNetworkRestore = true
        )
        
        // When network becomes available, retry flag should be cleared
        val networkAvailableState = baseState.withNetwork(true)
        assertTrue(networkAvailableState.isNetworkAvailable)
        assertFalse(networkAvailableState.shouldRetryOnNetworkRestore, 
                   "shouldRetryOnNetworkRestore should be cleared when network becomes available")
        
        // When network becomes unavailable, retry flag should be preserved
        val networkUnavailableState = baseState.withNetwork(false)
        assertFalse(networkUnavailableState.isNetworkAvailable)
        assertTrue(networkUnavailableState.shouldRetryOnNetworkRestore, 
                  "shouldRetryOnNetworkRestore should be preserved when network becomes unavailable")
    }
    
    @Test
    fun `onConnectionError sets retry flag correctly`() {
        // When network is unavailable and not paused, should set retry flag
        val noNetworkState = OrchestratorFlags.running().copy(
            isNetworkAvailable = false,
            isPausedByUser = false
        )
        val errorState = noNetworkState.onConnectionError()
        assertEquals(OnlineAsrReadyState.ERROR, errorState.readyState)
        assertTrue(errorState.shouldRetryOnNetworkRestore, 
                  "shouldRetryOnNetworkRestore should be set when network unavailable and not paused")
        
        // When paused, should not set retry flag
        val pausedState = OrchestratorFlags.running().copy(
            isNetworkAvailable = false,
            isPausedByUser = true
        )
        val pausedErrorState = pausedState.onConnectionError()
        assertFalse(pausedErrorState.shouldRetryOnNetworkRestore, 
                   "shouldRetryOnNetworkRestore should not be set when paused")
        
        // When network is available, should not set retry flag
        val networkAvailableState = OrchestratorFlags.running().copy(
            isNetworkAvailable = true,
            isPausedByUser = false
        )
        val availableErrorState = networkAvailableState.onConnectionError()
        assertFalse(availableErrorState.shouldRetryOnNetworkRestore, 
                   "shouldRetryOnNetworkRestore should not be set when network is available")
    }
    
    @Test
    fun `onNetworkLost sets retry flag for problematic connections`() {
        val connectedState = OrchestratorFlags.running().copy(
            readyState = OnlineAsrReadyState.CONNECTED,
            shouldRetryOnNetworkRestore = false
        )
        
        // For CONNECTED state, retry flag should be preserved (not set)
        val lostFromConnected = connectedState.onNetworkLost()
        assertFalse(lostFromConnected.isNetworkAvailable)
        assertFalse(lostFromConnected.shouldRetryOnNetworkRestore, 
                   "shouldRetryOnNetworkRestore should not be set for CONNECTED state")
        
        // For ERROR state, retry flag should be set
        val errorState = OrchestratorFlags.running().copy(
            readyState = OnlineAsrReadyState.ERROR,
            shouldRetryOnNetworkRestore = false
        )
        val lostFromError = errorState.onNetworkLost()
        assertTrue(lostFromError.shouldRetryOnNetworkRestore, 
                  "shouldRetryOnNetworkRestore should be set for ERROR state")
        
        // For NOT_CONNECTED state, retry flag should be set
        val disconnectedState = OrchestratorFlags.running().copy(
            readyState = OnlineAsrReadyState.NOT_CONNECTED,
            shouldRetryOnNetworkRestore = false
        )
        val lostFromDisconnected = disconnectedState.onNetworkLost()
        assertTrue(lostFromDisconnected.shouldRetryOnNetworkRestore, 
                  "shouldRetryOnNetworkRestore should be set for NOT_CONNECTED state")
    }
    
    @Test
    fun `conditional state queries work correctly`() {
        // Test canPause
        val runningState = OrchestratorFlags.running()
        assertTrue(runningState.canPause(), "Should be able to pause when running and not paused")
        
        val pausedState = runningState.withPause(true)
        assertFalse(pausedState.canPause(), "Should not be able to pause when already paused")
        
        val stoppedState = OrchestratorFlags.stopped()
        assertFalse(stoppedState.canPause(), "Should not be able to pause when stopped")
        
        // Test canResume
        assertTrue(pausedState.canResume(), "Should be able to resume when paused")
        assertFalse(runningState.canResume(), "Should not be able to resume when not paused")
        assertFalse(stoppedState.canResume(), "Should not be able to resume when stopped")
        
        // Test shouldAttemptReconnection
        val reconnectState = OrchestratorFlags.running().copy(
            isNetworkAvailable = true,
            isPausedByUser = false,
            shouldRetryOnNetworkRestore = true
        )
        assertTrue(reconnectState.shouldAttemptReconnection(), 
                  "Should attempt reconnection when conditions are met")
        
        // Fail cases
        assertFalse(reconnectState.copy(isRunning = false).shouldAttemptReconnection())
        assertFalse(reconnectState.copy(isNetworkAvailable = false).shouldAttemptReconnection())
        assertFalse(reconnectState.copy(isPausedByUser = true).shouldAttemptReconnection())
        assertFalse(reconnectState.copy(shouldRetryOnNetworkRestore = false).shouldAttemptReconnection())
        
        // Test shouldStartResume
        val resumeState = OrchestratorFlags.running().copy(
            isPausedByUser = false,
            readyState = OnlineAsrReadyState.READY,
            needsResumeOnReady = true
        )
        // This would violate invariant, so let's test with a valid state
        val validResumeState = OrchestratorFlags.running().copy(
            isPausedByUser = false,
            readyState = OnlineAsrReadyState.CONNECTED,
            needsResumeOnReady = true
        )
        assertFalse(validResumeState.shouldStartResume(), 
                   "Should not start resume when not in READY state")
    }
    
    @Test
    fun `sealed state interface transitions maintain invariants`() {
        // Test with sealed state interface
        val stoppedState = OnlineAsrState.Stopped()
        
        // Start transition
        val startingState = stoppedState.onStart()
        assertTrue(startingState is OnlineAsrState.Starting)
        
        // Started transition
        val runningState = startingState.onStarted()
        assertTrue(runningState is OnlineAsrState.RunningDisconnected)
        
        // Connection transitions
        val connectingState = runningState.onConnecting()
        assertTrue(connectingState is OnlineAsrState.RunningConnecting)
        
        val connectedState = connectingState.onConnected()
        assertTrue(connectedState is OnlineAsrState.RunningConnected)
        
        val readyState = connectedState.onReady()
        assertTrue(readyState is OnlineAsrState.RunningReady)
        
        // Pause/resume transitions
        val pausedState = readyState.onPause()
        assertTrue(pausedState is OnlineAsrState.RunningPaused)
        
        val resumedState = pausedState.onResume()
        assertTrue(resumedState is OnlineAsrState.RunningDisconnected)
        
        // Error transition
        val errorState = readyState.onError()
        assertTrue(errorState is OnlineAsrState.RunningError)
        
        // Stop transition
        val finalStoppedState = errorState.onStop()
        assertTrue(finalStoppedState is OnlineAsrState.Stopped)
    }
}
