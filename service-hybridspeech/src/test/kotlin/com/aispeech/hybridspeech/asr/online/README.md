# State Management Test Suite

This directory contains comprehensive tests for the OnlineASR state management system. The tests verify that the state system maintains consistency under various conditions and that illegal transitions are properly rejected.

## Test Coverage

### 1. Concurrent State Updates (`OnlineAsrStateConcurrencyTest`)

**Purpose**: Verifies that concurrent update attempts keep state consistent and maintain thread safety.

**Key Tests**:
- `concurrent updates maintain state consistency` - Tests rapid state updates from multiple coroutines
- `compare-and-set operations handle contention correctly` - Verifies CAS operations under high contention
- `rapid network state changes maintain consistency` - Tests network flapping scenarios
- `conditional updates work correctly under contention` - Tests pause/resume operations under concurrent access
- `state reset during concurrent operations maintains consistency` - Tests state resets during active operations
- `memory consistency during high frequency updates` - Verifies memory visibility across threads

**Verification**: 
- All state transitions maintain invariants
- No race conditions occur
- Compare-and-set operations work correctly
- High-frequency updates don't corrupt state

### 2. Illegal Transitions (`StateTransitionValidationTest`)

**Purpose**: Ensures that illegal state transitions are rejected with proper exceptions.

**Key Tests**:
- `reject needsResumeOnReady true when readyState is READY` - Tests invariant enforcement
- `reject shouldRetryOnNetworkRestore when network available and connected` - Tests retry flag logic
- `reject paused state when not running` - Tests pause state constraints
- `reject non-NOT_CONNECTED readyState when not running` - Tests ready state constraints
- `validate transition method enforces invariants` - Tests validation during transitions
- `extension functions respect invariants` - Tests that helper functions maintain invariants

**Verification**:
- Invalid states throw `IllegalArgumentException`
- State invariants are enforced consistently
- Extension functions maintain state consistency
- Edge cases are handled properly

### 3. Network Flapping and Pause/Resume (`NetworkFlapPauseResumeTest`)

**Purpose**: Verifies that pause/resume during network flapping works identical to old behavior.

**Key Tests**:
- `pause during network flapping behaves as expected` - Tests pause behavior during network changes
- `resume after pause with network flapping resumes correctly` - Tests resume after network issues
- `simultaneous pause and network loss handles correctly` - Tests concurrent pause and network events
- `resume with continuous network flapping restores correct state` - Tests resume during active network issues
- `maintain pause during persistent network instability` - Tests pause persistence through network problems

**Verification**:
- Pause state is preserved during network changes
- Resume operations work correctly regardless of network state
- Network flapping doesn't affect pause/resume logic
- State remains consistent during network instability

### 4. Regression Scenarios (`RegressionScenariosTest`)

**Purpose**: Regression-tests with recorded scenarios to prevent known issues from recurring.

**Key Scenarios**:
- `scenario 1` - Rapid connection failures with retry attempts
- `scenario 2` - Pause during connection establishment
- `scenario 3` - Stop during active resume session
- `scenario 4` - Network flapping during resume transmission
- `scenario 5` - Concurrent pause and network state changes
- `scenario 6` - Error recovery with retry attempts
- `scenario 7` - Audio type change during operation
- `scenario 8` - Resume state transitions with various ready states

**Verification**:
- Previously identified issues don't regress
- Complex real-world scenarios work correctly
- State remains valid through complex transitions
- Error recovery works as expected

### 5. Integration Tests (`OrchestratorIntegrationTest`)

**Purpose**: Tests the complete orchestrator functionality with state management integration.

**Key Tests**:
- `orchestrator state updates reflect network changes` - End-to-end network state handling
- `state transitions during connection lifecycle` - Complete connection flow testing
- `pause and resume operations maintain state consistency` - Integration of pause/resume with orchestrator
- `error handling maintains state consistency` - Error scenarios with state management
- `resume session state management` - Resume functionality integration
- `stop operation cleans up state properly` - Cleanup verification
- `concurrent state modifications are handled safely` - Concurrent access in integrated environment

**Verification**:
- State management integrates correctly with orchestrator
- Real-world operations maintain state consistency
- All components work together correctly
- Cleanup operations work properly

## State Invariants Verified

All tests verify these critical state invariants:

1. **Resume Invariant**: `needsResumeOnReady` cannot be `true` when `readyState` is `READY`
2. **Retry Invariant**: `shouldRetryOnNetworkRestore` should be `false` when network is available and connected
3. **Pause Invariant**: Cannot be paused when not running
4. **Ready State Invariant**: `readyState` should be `NOT_CONNECTED` when not running

## Running the Tests

### Run All State Management Tests
```bash
./gradlew test --tests com.aispeech.hybridspeech.asr.online.StateManagementTestSuite
```

### Run Individual Test Classes
```bash
# Concurrency tests
./gradlew test --tests com.aispeech.hybridspeech.asr.online.OnlineAsrStateConcurrencyTest

# Validation tests
./gradlew test --tests com.aispeech.hybridspeech.asr.online.StateTransitionValidationTest

# Network flapping tests
./gradlew test --tests com.aispeech.hybridspeech.asr.online.NetworkFlapPauseResumeTest

# Regression tests
./gradlew test --tests com.aispeech.hybridspeech.asr.online.RegressionScenariosTest

# Integration tests
./gradlew test --tests com.aispeech.hybridspeech.asr.online.OrchestratorIntegrationTest
```

### Run Specific Test Methods
```bash
# Test specific concurrent scenario
./gradlew test --tests "*.OnlineAsrStateConcurrencyTest.concurrent updates maintain state consistency"

# Test specific validation scenario
./gradlew test --tests "*.StateTransitionValidationTest.reject needsResumeOnReady true when readyState is READY"
```

## Test Dependencies

These tests require the following dependencies in your `build.gradle.kts`:

```kotlin
testImplementation("org.junit.jupiter:junit-jupiter:5.8.2")
testImplementation("org.junit.platform:junit-platform-suite:1.8.2")
testImplementation("org.jetbrains.kotlinx:kotlinx-coroutines-test:1.6.4")
testImplementation("org.mockito.kotlin:mockito-kotlin:4.0.0")
```

## Key Testing Patterns

### 1. State Invariant Validation
```kotlin
private fun assertValidState(state: OrchestratorFlags) {
    // Verify all invariants hold
    if (state.needsResumeOnReady) {
        assertNotEquals(OnlineAsrReadyState.READY, state.readyState)
    }
    // ... other invariants
}
```

### 2. Concurrent Testing
```kotlin
val jobs = (1..numCoroutines).map { coroutineId ->
    launch {
        repeat(updatesPerCoroutine) {
            stateHolder.updateStateWithRetry { state ->
                // Perform state update
            }
        }
    }
}
jobs.joinAll()
```

### 3. Exception Testing
```kotlin
assertThrows<IllegalArgumentException> {
    stateHolder.setState(invalidState)
}
```

## Contributing

When adding new tests:

1. Ensure all state invariants are verified
2. Test both success and failure cases
3. Include concurrent access scenarios where applicable
4. Add regression tests for any new issues discovered
5. Document the purpose and verification criteria for new tests

## Known Issues

If any test fails, it indicates a regression in state management that needs immediate attention. The state system is critical for proper operation of the OnlineASR system.
