package com.aispeech.hybridspeech.asr.online

import kotlinx.coroutines.*
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.test.*
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.TestInstance
import org.mockito.kotlin.*
import java.util.concurrent.atomic.AtomicBoolean
import java.util.concurrent.atomic.AtomicInteger

/**
 * Integration tests for OnlineAsrOrchestrator with complete state management
 * 
 * This test suite verifies that:
 * 1. The orchestrator maintains state consistency during real operations
 * 2. Network state changes are properly handled
 * 3. Pause/resume operations work correctly with the state system
 * 4. Integration between state management and audio processing is correct
 */
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class OrchestratorIntegrationTest {
    
    private lateinit var stateHolder: StateHolder
    private lateinit var mockNetworkStateMonitor: NetworkStateMonitor
    private lateinit var networkAvailableFlow: MutableStateFlow<Boolean>
    
    @BeforeEach
    fun setup() {
        stateHolder = StateHolder(OrchestratorFlags.initial())
        
        // Create mock network state monitor
        networkAvailableFlow = MutableStateFlow(true)
        mockNetworkStateMonitor = mock {
            on { networkAvailableFlow } doReturn <EMAIL>()
            on { isNetworkAvailable() } doAnswer { networkAvailableFlow.value }
        }
    }
    
    @Test
    fun `orchestrator state updates reflect network changes`() = runTest {
        // Initial state
        assertTrue(stateHolder.current.isNetworkAvailable)
        
        // Simulate network becoming unavailable
        stateHolder.updateStateWithRetry { it.withNetwork(false) }
        assertFalse(stateHolder.current.isNetworkAvailable)
        
        // Start ASR while network is down
        stateHolder.updateStateWithRetry { it.withRunning(true) }
        assertTrue(stateHolder.current.isRunning)
        assertFalse(stateHolder.current.isNetworkAvailable)
        
        // Network comes back
        stateHolder.updateStateWithRetry { it.onNetworkRestored() }
        assertTrue(stateHolder.current.isNetworkAvailable)
        assertTrue(stateHolder.current.isRunning)
    }
    
    @Test
    fun `state transitions during connection lifecycle`() = runTest {
        // Start ASR
        stateHolder.updateStateWithRetry { it.withRunning(true) }
        assertTrue(stateHolder.current.isRunning)
        assertEquals(OnlineAsrReadyState.NOT_CONNECTED, stateHolder.current.readyState)
        
        // Begin connecting
        stateHolder.updateStateWithRetry { it.withReadyState(OnlineAsrReadyState.CONNECTING) }
        assertEquals(OnlineAsrReadyState.CONNECTING, stateHolder.current.readyState)
        
        // Connection established
        stateHolder.updateStateWithRetry { it.onConnected() }
        assertEquals(OnlineAsrReadyState.CONNECTED, stateHolder.current.readyState)
        
        // Business ready
        stateHolder.updateStateWithRetry { it.onReady() }
        assertEquals(OnlineAsrReadyState.READY, stateHolder.current.readyState)
        
        // All states should be valid
        assertTrue(stateHolder.current.isRunning)
        assertTrue(stateHolder.current.isNetworkAvailable)
    }
    
    @Test
    fun `pause and resume operations maintain state consistency`() = runTest {
        // Start ASR and get to ready state
        stateHolder.updateStateWithRetry { 
            it.withRunning(true)
              .onConnected()
              .onReady()
        }
        
        val preState = stateHolder.current
        assertTrue(preState.isRunning)
        assertFalse(preState.isPausedByUser)
        assertEquals(OnlineAsrReadyState.READY, preState.readyState)
        
        // Pause
        stateHolder.updateStateWithRetry { it.withPause(true) }
        val pausedState = stateHolder.current
        assertTrue(pausedState.isRunning, "Should remain running when paused")
        assertTrue(pausedState.isPausedByUser)
        
        // Resume
        stateHolder.updateStateWithRetry { it.withPause(false) }
        val resumedState = stateHolder.current
        assertTrue(resumedState.isRunning)
        assertFalse(resumedState.isPausedByUser)
    }
    
    @Test
    fun `error handling maintains state consistency`() = runTest {
        // Start and connect
        stateHolder.updateStateWithRetry { 
            it.withRunning(true)
              .onConnected()
        }
        assertEquals(OnlineAsrReadyState.CONNECTED, stateHolder.current.readyState)
        
        // Error occurs
        stateHolder.updateStateWithRetry { it.onConnectionError() }
        assertEquals(OnlineAsrReadyState.ERROR, stateHolder.current.readyState)
        assertTrue(stateHolder.current.isRunning, "Should remain running in error state")
        
        // Network becomes unavailable during error
        stateHolder.updateStateWithRetry { it.onNetworkLost() }
        assertFalse(stateHolder.current.isNetworkAvailable)
        assertTrue(stateHolder.current.shouldRetryOnNetworkRestore, 
                  "Should set retry flag when network lost during error")
        
        // Recovery
        stateHolder.updateStateWithRetry { 
            it.onNetworkRestored()
              .withReadyState(OnlineAsrReadyState.CONNECTING)
              .onConnected()
        }
        assertEquals(OnlineAsrReadyState.CONNECTED, stateHolder.current.readyState)
        assertTrue(stateHolder.current.isNetworkAvailable)
        assertFalse(stateHolder.current.shouldRetryOnNetworkRestore)
    }
    
    @Test
    fun `resume session state management`() = runTest {
        // Start with resume needed
        stateHolder.updateStateWithRetry { 
            it.withRunning(true)
              .withReadyState(OnlineAsrReadyState.CONNECTED)
              .withResumeOnReady(true)
        }
        
        assertTrue(stateHolder.current.needsResumeOnReady)
        assertEquals(OnlineAsrReadyState.CONNECTED, stateHolder.current.readyState)
        
        // Become ready - should trigger resume and clear flag
        stateHolder.updateStateWithRetry { it.onReady() }
        assertEquals(OnlineAsrReadyState.READY, stateHolder.current.readyState)
        assertFalse(stateHolder.current.needsResumeOnReady, 
                   "Resume flag should be cleared when becoming ready")
        
        // Test shouldStartResume logic
        val shouldResumeState = OrchestratorFlags.running().copy(
            isPausedByUser = false,
            readyState = OnlineAsrReadyState.CONNECTED, // Not READY
            needsResumeOnReady = true
        )
        assertFalse(shouldResumeState.shouldStartResume(), 
                   "Should not start resume when not in READY state")
    }
    
    @Test
    fun `stop operation cleans up state properly`() = runTest {
        // Start with complex state
        stateHolder.updateStateWithRetry { 
            it.withRunning(true)
              .withPause(true)
              .withReadyState(OnlineAsrReadyState.CONNECTED)
              .withResumeOnReady(true)
              .withRetryOnNetworkRestore(true)
        }
        
        val preStopState = stateHolder.current
        assertTrue(preStopState.isRunning)
        assertTrue(preStopState.isPausedByUser)
        assertTrue(preStopState.needsResumeOnReady)
        assertTrue(preStopState.shouldRetryOnNetworkRestore)
        
        // Stop ASR
        stateHolder.updateStateWithRetry { it.withRunning(false) }
        
        val stoppedState = stateHolder.current
        assertFalse(stoppedState.isRunning)
        assertFalse(stoppedState.isPausedByUser, "Pause should be cleared when stopping")
        assertFalse(stoppedState.needsResumeOnReady, "Resume flag should be cleared when stopping")
        assertFalse(stoppedState.shouldRetryOnNetworkRestore, "Retry flag should be cleared when stopping")
        assertEquals(OnlineAsrReadyState.NOT_CONNECTED, stoppedState.readyState,
                    "Ready state should be reset when stopping")
    }
    
    @Test
    fun `audio type changes maintain state consistency`() = runTest {
        // Start with Opus
        stateHolder.updateStateWithRetry { 
            it.withRunning(true)
              .withAudioType(AudioType.OGG_OPUS)
              .onConnected()
        }
        
        assertEquals(AudioType.OGG_OPUS, stateHolder.current.audioType)
        assertTrue(stateHolder.current.isRunning)
        assertEquals(OnlineAsrReadyState.CONNECTED, stateHolder.current.readyState)
        
        // Change to MP3
        stateHolder.updateStateWithRetry { it.withAudioType(AudioType.MP3) }
        
        assertEquals(AudioType.MP3, stateHolder.current.audioType)
        assertTrue(stateHolder.current.isRunning, "Should remain running after audio type change")
        assertEquals(OnlineAsrReadyState.CONNECTED, stateHolder.current.readyState,
                    "Connection state should be preserved after audio type change")
    }
    
    @Test
    fun `concurrent state modifications are handled safely`() = runTest {
        val operationCount = AtomicInteger(0)
        val successCount = AtomicInteger(0)
        
        // Launch multiple coroutines performing different operations
        val jobs = listOf(
            // Network operations
            launch {
                repeat(50) {
                    operationCount.incrementAndGet()
                    stateHolder.updateStateWithRetry { state ->
                        successCount.incrementAndGet()
                        state.withNetwork(it % 2 == 0)
                    }
                    yield()
                }
            },
            // Pause/resume operations
            launch {
                repeat(50) {
                    operationCount.incrementAndGet()
                    if (stateHolder.current.canPause()) {
                        stateHolder.updateStateWithRetry { state ->
                            successCount.incrementAndGet()
                            state.withPause(true)
                        }
                    } else if (stateHolder.current.canResume()) {
                        stateHolder.updateStateWithRetry { state ->
                            successCount.incrementAndGet()
                            state.withPause(false)
                        }
                    }
                    yield()
                }
            },
            // Ready state operations
            launch {
                repeat(50) {
                    operationCount.incrementAndGet()
                    val readyState = OnlineAsrReadyState.values()[it % OnlineAsrReadyState.values().size]
                    stateHolder.updateStateWithRetry { state ->
                        successCount.incrementAndGet()
                        state.withReadyState(readyState)
                    }
                    yield()
                }
            }
        )
        
        // Set initial running state
        stateHolder.updateStateWithRetry { it.withRunning(true) }
        
        jobs.joinAll()
        
        // All operations should have completed
        assertTrue(operationCount.get() > 0, "Should have performed operations")
        assertTrue(successCount.get() > 0, "Should have successful operations")
        
        // Final state should be valid
        val finalState = stateHolder.current
        assertValidState(finalState)
    }
    
    @Test
    fun `conditional operations respect state constraints`() = runTest {
        // Test canPause conditions
        val stoppedState = OrchestratorFlags.stopped()
        assertFalse(stoppedState.canPause(), "Cannot pause when stopped")
        
        val runningState = OrchestratorFlags.running()
        assertTrue(runningState.canPause(), "Can pause when running and not paused")
        
        val pausedState = runningState.withPause(true)
        assertFalse(pausedState.canPause(), "Cannot pause when already paused")
        
        // Test canResume conditions
        assertFalse(stoppedState.canResume(), "Cannot resume when stopped")
        assertFalse(runningState.canResume(), "Cannot resume when not paused")
        assertTrue(pausedState.canResume(), "Can resume when paused")
        
        // Test shouldAttemptReconnection conditions
        val reconnectState = OrchestratorFlags.running().copy(
            isNetworkAvailable = true,
            isPausedByUser = false,
            shouldRetryOnNetworkRestore = true
        )
        assertTrue(reconnectState.shouldAttemptReconnection())
        
        // Fail conditions
        assertFalse(reconnectState.copy(isRunning = false).shouldAttemptReconnection())
        assertFalse(reconnectState.copy(isNetworkAvailable = false).shouldAttemptReconnection())
        assertFalse(reconnectState.copy(isPausedByUser = true).shouldAttemptReconnection())
        assertFalse(reconnectState.copy(shouldRetryOnNetworkRestore = false).shouldAttemptReconnection())
    }
    
    private fun assertValidState(state: OrchestratorFlags) {
        // Verify all state invariants
        if (state.needsResumeOnReady) {
            assertNotEquals(OnlineAsrReadyState.READY, state.readyState,
                           "needsResumeOnReady cannot be true when readyState is READY")
        }
        
        if (state.shouldRetryOnNetworkRestore && state.isNetworkAvailable) {
            assertFalse(state.readyState in listOf(OnlineAsrReadyState.CONNECTED, OnlineAsrReadyState.READY),
                       "shouldRetryOnNetworkRestore should be false when network available and connected")
        }
        
        if (state.isPausedByUser) {
            assertTrue(state.isRunning, "Cannot be paused when not running")
        }
        
        if (!state.isRunning) {
            assertEquals(OnlineAsrReadyState.NOT_CONNECTED, state.readyState,
                        "readyState should be NOT_CONNECTED when not running")
        }
    }
}
