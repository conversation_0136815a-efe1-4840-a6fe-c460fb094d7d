package com.aispeech.hybridspeech.keepalive

import android.content.Context
import androidx.test.core.app.ApplicationProvider
import androidx.test.ext.junit.runners.AndroidJUnit4
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.test.*
import org.junit.After
import org.junit.Assert.*
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import org.mockito.kotlin.*

/**
 * KeepAliveManager单元测试
 */
@ExperimentalCoroutinesApi
@RunWith(AndroidJUnit4::class)
class KeepAliveManagerTest {
    
    private lateinit var context: Context
    private lateinit var keepAliveManager: KeepAliveManager
    private lateinit var testConfig: KeepAliveConfig
    
    @Mock
    private lateinit var mockCallback: KeepAliveCallback
    
    private val testDispatcher = StandardTestDispatcher()
    
    @Before
    fun setUp() {
        MockitoAnnotations.openMocks(this)
        
        // 设置测试调度器
        Dispatchers.setMain(testDispatcher)
        
        context = ApplicationProvider.getApplicationContext()
        keepAliveManager = KeepAliveManager.getInstance()
        
        // 创建测试配置
        testConfig = KeepAliveConfig(
            targetPackages = listOf("com.test.package1", "com.test.package2"),
            foregroundServiceConfig = ForegroundServiceConfig(enabled = false), // 禁用以避免实际服务操作
            periodicCheckConfig = PeriodicCheckConfig(
                enabled = true,
                checkInterval = 1000L // 1秒，用于快速测试
            ),
            broadcastReceiverConfig = BroadcastReceiverConfig(enabled = false), // 禁用以避免实际广播注册
            dozeWhitelistConfig = DozeWhitelistConfig(enabled = false), // 禁用以避免系统权限问题
            packageActivationConfig = PackageActivationConfig(enabled = true)
        )
    }
    
    @After
    fun tearDown() {
        runTest {
            keepAliveManager.release()
        }
        Dispatchers.resetMain()
    }
    
    @Test
    fun testManagerInitialization() = runTest {
        // 测试初始化
        val result = keepAliveManager.initialize(context, testConfig, mockCallback)
        assertTrue("Manager should initialize successfully", result)
        
        // 验证状态
        assertEquals(KeepAliveStatus.INACTIVE, keepAliveManager.managerStatus.first())
        assertFalse("Manager should not be running initially", keepAliveManager.isRunning())
    }
    
    @Test
    fun testManagerStartStop() = runTest {
        // 初始化
        keepAliveManager.initialize(context, testConfig, mockCallback)
        
        // 启动
        keepAliveManager.start()
        advanceUntilIdle()
        
        assertTrue("Manager should be running after start", keepAliveManager.isRunning())
        assertEquals(KeepAliveStatus.ACTIVE, keepAliveManager.managerStatus.first())
        
        // 停止
        keepAliveManager.stop()
        advanceUntilIdle()
        
        assertFalse("Manager should not be running after stop", keepAliveManager.isRunning())
        assertEquals(KeepAliveStatus.INACTIVE, keepAliveManager.managerStatus.first())
    }
    
    @Test
    fun testPackageStatusCheck() = runTest {
        // 初始化并启动
        keepAliveManager.initialize(context, testConfig, mockCallback)
        keepAliveManager.start()
        advanceUntilIdle()
        
        // 检查包状态
        val packageName = "com.test.package1"
        keepAliveManager.checkPackageStatus(packageName)
        advanceUntilIdle()
        
        // 验证回调被调用
        verify(mockCallback, atLeastOnce()).onEvent(
            eq(KeepAliveEvent.CHECK_COMPLETED),
            eq(packageName),
            any(),
            any()
        )
    }
    
    @Test
    fun testPackageActivation() = runTest {
        // 初始化并启动
        keepAliveManager.initialize(context, testConfig, mockCallback)
        keepAliveManager.start()
        advanceUntilIdle()
        
        // 激活包
        val packageName = "com.test.package1"
        val result = keepAliveManager.activatePackage(packageName)
        
        // 由于是测试环境，激活可能失败，但应该有尝试
        // 验证相关回调或事件
        verify(mockCallback, atLeastOnce()).onEvent(
            any(),
            eq(packageName),
            any(),
            any()
        )
    }
    
    @Test
    fun testConfigUpdate() = runTest {
        // 初始化
        keepAliveManager.initialize(context, testConfig, mockCallback)
        
        // 更新配置
        val newConfig = testConfig.copy(
            periodicCheckConfig = testConfig.periodicCheckConfig.copy(
                checkInterval = 2000L
            )
        )
        
        keepAliveManager.updateConfig(newConfig)
        advanceUntilIdle()
        
        // 验证配置更新成功（通过检查内部状态或行为变化）
        // 这里可以通过统计信息验证
        val statistics = keepAliveManager.getStatistics()
        assertTrue("Statistics should contain updated config", statistics.isNotEmpty())
    }
    
    @Test
    fun testMultiplePackageStatus() = runTest {
        // 初始化并启动
        keepAliveManager.initialize(context, testConfig, mockCallback)
        keepAliveManager.start()
        advanceUntilIdle()
        
        // 获取所有包状态
        val statuses = keepAliveManager.getAllPackageStatuses()
        
        // 验证返回的状态包含配置的包
        testConfig.targetPackages.forEach { packageName ->
            assertTrue("Status should contain package $packageName", statuses.containsKey(packageName))
        }
    }
    
    @Test
    fun testCallbackNotifications() = runTest {
        // 初始化并启动
        keepAliveManager.initialize(context, testConfig, mockCallback)
        keepAliveManager.start()
        advanceUntilIdle()
        
        // 等待一些周期性检查
        advanceTimeBy(3000L)
        advanceUntilIdle()
        
        // 验证回调被调用
        verify(mockCallback, atLeastOnce()).onEvent(any(), any(), any(), any())
    }
    
    @Test
    fun testErrorHandling() = runTest {
        // 测试无效配置
        val invalidConfig = KeepAliveConfig(targetPackages = emptyList())
        
        val result = keepAliveManager.initialize(context, invalidConfig, mockCallback)
        
        // 即使配置无效，初始化也应该成功（但可能有警告）
        assertTrue("Manager should handle invalid config gracefully", result)
    }
    
    @Test
    fun testStatisticsGeneration() = runTest {
        // 初始化并启动
        keepAliveManager.initialize(context, testConfig, mockCallback)
        keepAliveManager.start()
        advanceUntilIdle()
        
        // 获取统计信息
        val statistics = keepAliveManager.getStatistics()
        
        // 验证统计信息包含预期字段
        assertTrue("Statistics should not be empty", statistics.isNotEmpty())
        assertTrue("Statistics should contain manager status", statistics.containsKey("managerStatus"))
        assertTrue("Statistics should contain target packages", statistics.containsKey("targetPackages"))
    }
    
    @Test
    fun testConcurrentOperations() = runTest {
        // 初始化
        keepAliveManager.initialize(context, testConfig, mockCallback)
        
        // 并发启动和停止
        val job1 = launch { keepAliveManager.start() }
        val job2 = launch { keepAliveManager.stop() }
        val job3 = launch { keepAliveManager.checkPackageStatus("com.test.package1") }
        
        // 等待所有操作完成
        job1.join()
        job2.join()
        job3.join()
        advanceUntilIdle()
        
        // 验证没有崩溃或异常
        // 最终状态应该是一致的
        val finalStatus = keepAliveManager.managerStatus.first()
        assertTrue("Final status should be valid", finalStatus in KeepAliveStatus.values())
    }
    
    @Test
    fun testResourceCleanup() = runTest {
        // 初始化并启动
        keepAliveManager.initialize(context, testConfig, mockCallback)
        keepAliveManager.start()
        advanceUntilIdle()
        
        // 释放资源
        keepAliveManager.release()
        advanceUntilIdle()
        
        // 验证状态重置
        assertEquals(KeepAliveStatus.UNKNOWN, keepAliveManager.managerStatus.first())
        assertFalse("Manager should not be running after release", keepAliveManager.isRunning())
    }
}
