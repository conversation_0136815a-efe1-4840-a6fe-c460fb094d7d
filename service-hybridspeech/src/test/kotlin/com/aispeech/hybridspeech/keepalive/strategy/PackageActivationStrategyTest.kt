package com.aispeech.hybridspeech.keepalive.strategy

import android.content.Context
import androidx.test.core.app.ApplicationProvider
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.aispeech.hybridspeech.keepalive.*
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.test.*
import org.junit.After
import org.junit.Assert.*
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import org.mockito.kotlin.*

/**
 * PackageActivationStrategy单元测试
 */
@ExperimentalCoroutinesApi
@RunWith(AndroidJUnit4::class)
class PackageActivationStrategyTest {
    
    private lateinit var context: Context
    private lateinit var strategy: PackageActivationStrategy
    private lateinit var testConfig: KeepAliveConfig
    
    @Mock
    private lateinit var mockCallback: KeepAliveCallback
    
    private val testDispatcher = StandardTestDispatcher()
    
    @Before
    fun setUp() {
        MockitoAnnotations.openMocks(this)
        
        // 设置测试调度器
        Dispatchers.setMain(testDispatcher)
        
        context = ApplicationProvider.getApplicationContext()
        
        // 创建测试配置
        testConfig = KeepAliveConfig(
            targetPackages = listOf("com.test.package"),
            packageActivationConfig = PackageActivationConfig(
                enabled = true,
                activationMethod = PackageActivationConfig.ActivationMethod.ACTIVITY,
                maxActivationRetry = 2,
                activationTimeout = 5000L,
                retryDelay = 1000L
            )
        )
        
        strategy = PackageActivationStrategy()
    }
    
    @After
    fun tearDown() {
        runTest {
            strategy.release()
        }
        Dispatchers.resetMain()
    }
    
    @Test
    fun testStrategyInitialization() = runTest {
        // 初始化策略
        strategy.initialize(context, testConfig, mockCallback)
        
        // 验证策略状态
        assertTrue("Strategy should be enabled", strategy.isEnabled)
        assertEquals("PackageActivation", strategy.name)
        assertFalse("Strategy should not be running initially", strategy.isRunning)
    }
    
    @Test
    fun testStrategyStartStop() = runTest {
        // 初始化
        strategy.initialize(context, testConfig, mockCallback)
        
        // 启动
        val packages = listOf("com.test.package1", "com.test.package2")
        strategy.start(packages)
        advanceUntilIdle()
        
        assertTrue("Strategy should be running after start", strategy.isRunning)
        
        // 停止
        strategy.stop()
        advanceUntilIdle()
        
        assertFalse("Strategy should not be running after stop", strategy.isRunning)
    }
    
    @Test
    fun testPackageStatusCheck() = runTest {
        // 初始化并启动
        strategy.initialize(context, testConfig, mockCallback)
        strategy.start(listOf("com.test.package"))
        advanceUntilIdle()
        
        // 检查包状态
        val status = strategy.checkStatus("com.test.package")
        
        // 验证状态是有效的
        assertTrue("Status should be valid", status in KeepAliveStatus.values())
    }
    
    @Test
    fun testPackageActivation() = runTest {
        // 初始化并启动
        strategy.initialize(context, testConfig, mockCallback)
        strategy.start(listOf("com.test.package"))
        advanceUntilIdle()
        
        // 尝试激活包
        val result = strategy.activate("com.test.package")
        
        // 由于是测试环境，激活可能失败，但应该有尝试
        // 验证执行统计被记录
        val statistics = strategy.getStatistics()
        assertTrue("Statistics should contain execution count", statistics.containsKey("totalExecutions"))
    }
    
    @Test
    fun testActivationRetry() = runTest {
        // 创建配置，设置重试次数
        val retryConfig = testConfig.copy(
            packageActivationConfig = testConfig.packageActivationConfig.copy(
                maxActivationRetry = 3,
                retryDelay = 100L // 短延迟用于测试
            )
        )
        
        strategy.initialize(context, retryConfig, mockCallback)
        strategy.start(listOf("com.nonexistent.package"))
        advanceUntilIdle()
        
        // 尝试激活不存在的包（应该失败并重试）
        val result = strategy.activate("com.nonexistent.package")
        
        // 验证重试逻辑
        val statistics = strategy.getStatistics()
        assertTrue("Should have execution attempts", statistics["totalExecutions"] as Long > 0)
    }
    
    @Test
    fun testActivationTimeout() = runTest {
        // 创建配置，设置短超时
        val timeoutConfig = testConfig.copy(
            packageActivationConfig = testConfig.packageActivationConfig.copy(
                activationTimeout = 100L // 100ms超时
            )
        )
        
        strategy.initialize(context, timeoutConfig, mockCallback)
        strategy.start(listOf("com.test.package"))
        advanceUntilIdle()
        
        // 测试超时处理
        val startTime = System.currentTimeMillis()
        val result = strategy.activate("com.test.package")
        val endTime = System.currentTimeMillis()
        
        // 验证在合理时间内完成（考虑超时设置）
        assertTrue("Should complete within reasonable time", endTime - startTime < 5000L)
    }
    
    @Test
    fun testDifferentActivationMethods() = runTest {
        // 测试Activity方法
        val activityConfig = testConfig.copy(
            packageActivationConfig = testConfig.packageActivationConfig.copy(
                activationMethod = PackageActivationConfig.ActivationMethod.ACTIVITY
            )
        )
        
        strategy.initialize(context, activityConfig, mockCallback)
        strategy.start(listOf("com.test.package"))
        
        val activityResult = strategy.activate("com.test.package")
        
        // 测试Shell方法
        val shellConfig = testConfig.copy(
            packageActivationConfig = testConfig.packageActivationConfig.copy(
                activationMethod = PackageActivationConfig.ActivationMethod.SHELL
            )
        )
        
        strategy.updateConfig(shellConfig)
        val shellResult = strategy.activate("com.test.package")
        
        // 验证两种方法都有尝试
        val statistics = strategy.getStatistics()
        assertTrue("Should have execution attempts", statistics["totalExecutions"] as Long >= 2)
    }
    
    @Test
    fun testCallbackNotifications() = runTest {
        // 初始化并启动
        strategy.initialize(context, testConfig, mockCallback)
        strategy.start(listOf("com.test.package"))
        advanceUntilIdle()
        
        // 执行激活
        strategy.activate("com.test.package")
        advanceUntilIdle()
        
        // 验证回调被调用（可能是错误回调，因为测试环境中包不存在）
        verify(mockCallback, atLeastOnce()).onEvent(any(), any(), any(), any())
    }
    
    @Test
    fun testStatisticsCollection() = runTest {
        // 初始化并启动
        strategy.initialize(context, testConfig, mockCallback)
        strategy.start(listOf("com.test.package1", "com.test.package2"))
        advanceUntilIdle()
        
        // 执行一些操作
        strategy.checkStatus("com.test.package1")
        strategy.activate("com.test.package2")
        advanceUntilIdle()
        
        // 获取统计信息
        val statistics = strategy.getStatistics()
        
        // 验证统计信息包含预期字段
        assertTrue("Should contain total executions", statistics.containsKey("totalExecutions"))
        assertTrue("Should contain successful executions", statistics.containsKey("successfulExecutions"))
        assertTrue("Should contain failed executions", statistics.containsKey("failedExecutions"))
        assertTrue("Should contain average execution time", statistics.containsKey("averageExecutionTime"))
        assertTrue("Should contain activation method", statistics.containsKey("activationMethod"))
    }
    
    @Test
    fun testConfigUpdate() = runTest {
        // 初始化
        strategy.initialize(context, testConfig, mockCallback)
        
        // 更新配置
        val newConfig = testConfig.copy(
            packageActivationConfig = testConfig.packageActivationConfig.copy(
                activationMethod = PackageActivationConfig.ActivationMethod.COMPREHENSIVE,
                maxActivationRetry = 5
            )
        )
        
        strategy.updateConfig(newConfig)
        
        // 验证配置更新
        val statistics = strategy.getStatistics()
        assertEquals("Should use comprehensive method", 
            PackageActivationConfig.ActivationMethod.COMPREHENSIVE.name, 
            statistics["activationMethod"])
    }
    
    @Test
    fun testErrorHandling() = runTest {
        // 初始化
        strategy.initialize(context, testConfig, mockCallback)
        strategy.start(listOf("com.invalid.package"))
        advanceUntilIdle()
        
        // 尝试激活无效包
        val result = strategy.activate("com.invalid.package")
        
        // 验证错误处理
        verify(mockCallback, atLeastOnce()).onError(
            eq("com.invalid.package"),
            eq(KeepAliveStrategy.PACKAGE_ACTIVATION),
            any(),
            any()
        )
        
        // 验证失败统计
        val statistics = strategy.getStatistics()
        assertTrue("Should have failed executions", (statistics["failedExecutions"] as Long) > 0)
    }
    
    @Test
    fun testResourceCleanup() = runTest {
        // 初始化并启动
        strategy.initialize(context, testConfig, mockCallback)
        strategy.start(listOf("com.test.package"))
        advanceUntilIdle()
        
        // 释放资源
        strategy.release()
        advanceUntilIdle()
        
        // 验证状态重置
        assertFalse("Strategy should not be running after release", strategy.isRunning)
        
        // 验证统计信息被重置
        val statistics = strategy.getStatistics()
        assertEquals("Executions should be reset", 0L, statistics["totalExecutions"])
    }
}
