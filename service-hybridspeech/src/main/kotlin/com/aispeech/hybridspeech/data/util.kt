package com.aispeech.hybridspeech.data

import android.os.Build
import android.os.Parcel
import android.os.Parcelable


inline fun <reified T : Parcelable> Parcel.readParcelableStrict(): T {
  val clsLoader = T::class.java.classLoader
  val value = if (Build.VERSION.SDK_INT >= 33) {
    readParcelable(clsLoader, T::class.java)
  } else {
    @Suppress("DEPRECATION")
    readParcelable<T>(clsLoader)
  }
  return requireNotNull(value) { "Parcel: ${T::class.java.simpleName} is null" }
}