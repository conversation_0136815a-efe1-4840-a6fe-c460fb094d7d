package com.aispeech.hybridspeech.data
import android.content.Context
import com.aispeech.aibase.AILog
import java.io.File
import java.io.FileOutputStream
import java.io.IOException

object AssetCopyUtil {

  private const val TAG = "AssetCopyUtil"
  private const val PREFS_NAME = "asset_copy_prefs"
  private const val PREF_ASSET_VERSION_PREFIX = "asset_version_"

  /**
   * 将 assets 目录下的指定资源（文件或文件夹）复制到应用的内部 files 目录。
   *
   * @param context Context 对象
   * @param assetPath assets 目录下的相对路径，例如 "sdk_resources" 或 "configs/data.json"。
   *                  如果是一个文件夹，则会递归复制整个文件夹。
   * @param targetSubDir files 目录下的目标子目录名，例如 "my_sdk_data"。如果为 null 或空，则直接复制到 files 目录下。
   * @param currentAssetVersion 当前资源的版本号，用于判断是否需要更新。每次资源更新时，应增加此版本号。
   * @return 复制成功后，目标资源的绝对路径；如果失败，则返回 null。
   */
  fun copyAssetsToInternalStorage(
    context: Context,
    assetPath: String,
    targetSubDir: String?,
    currentAssetVersion: Int = 1 // 默认为版本1
  ): String? {
    val prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
    val versionKey = PREF_ASSET_VERSION_PREFIX + assetPath.replace(File.separatorChar, '_')
    val lastCopiedVersion = prefs.getInt(versionKey, 0)

    val destinationDirName = targetSubDir ?: assetPath.substringAfterLast('/')
    val baseDestinationDir = context.filesDir // data/data/packageName/files/
    val finalDestinationDir = File(baseDestinationDir, destinationDirName)

    // 如果当前版本高于已复制版本，或者目标目录不存在，则执行复制
    if (currentAssetVersion > lastCopiedVersion || !finalDestinationDir.exists() || (finalDestinationDir.isDirectory && finalDestinationDir.list()?.isEmpty() == true)) {
      AILog.i(TAG, "Newer version ($currentAssetVersion > $lastCopiedVersion) or destination missing. Copying assets from '$assetPath' to '${finalDestinationDir.absolutePath}'")

      // 如果是更新，先删除旧的目录/文件
      if (finalDestinationDir.exists()) {
        if (!deleteRecursive(finalDestinationDir)) {
          AILog.e(TAG, "Failed to delete old directory: ${finalDestinationDir.absolutePath}")
        }
      }

      if (!finalDestinationDir.mkdirs() && !finalDestinationDir.isDirectory) {
        AILog.e(TAG, "Failed to create destination directory: ${finalDestinationDir.absolutePath}")
      }


      try {
        val assetManager = context.assets
        val assetFiles: Array<String>? = try {
          assetManager.list(assetPath)
        } catch (e: IOException) {
          null
        }

        if (assetFiles == null) { // assetPath 是一个文件
          val targetFile = if (targetSubDir != null) {
            File(finalDestinationDir, assetPath.substringAfterLast('/'))
          } else {
            File(baseDestinationDir, assetPath.substringAfterLast('/'))
          }
          targetFile.parentFile?.mkdirs()
          copyAssetFile(context, assetPath, targetFile)
        } else {
          if (assetFiles.isEmpty() && assetPath.isNotEmpty()) {
            finalDestinationDir.mkdirs()
            AILog.i(TAG, "Asset path '$assetPath' is an empty directory. Created destination: ${finalDestinationDir.absolutePath}")
          } else {
            for (fileName in assetFiles) {
              val source = if (assetPath.isEmpty()) fileName else "$assetPath${File.separator}$fileName"
              val dest = File(finalDestinationDir, fileName)
              copyAssetPathRecursive(context, source, dest)
            }
          }
        }

        prefs.edit().putInt(versionKey, currentAssetVersion).apply()
        AILog.i(TAG, "Successfully copied assets from '$assetPath' to '${finalDestinationDir.absolutePath}'. Version: $currentAssetVersion")
        return finalDestinationDir.absolutePath
      } catch (e: IOException) {
        AILog.e(TAG, "Failed to copy assets from '$assetPath'", e)
        // 清理可能部分复制的文件
        deleteRecursive(finalDestinationDir)
        return null
      }
    } else {
      AILog.i(TAG, "Assets '$assetPath' already up to date (version $lastCopiedVersion) at '${finalDestinationDir.absolutePath}'")
      return finalDestinationDir.absolutePath
    }
  }

  private fun copyAssetPathRecursive(context: Context, assetPath: String, destFile: File) {
    val assetManager = context.assets
    try {
      val assets = assetManager.list(assetPath)
      if (assets.isNullOrEmpty()) {
        if (!assetPath.endsWith("/")) { //
          try {
            copyAssetFile(context, assetPath, destFile)
          } catch (e: IOException) {
            if (!destFile.exists()) {
              destFile.mkdirs()
              AILog.d(TAG, "Created empty directory: ${destFile.absolutePath} from asset: $assetPath")
            }
          }
        } else {
          if (!destFile.exists()) {
            destFile.mkdirs()
            AILog.d(TAG, "Created empty directory: ${destFile.absolutePath} from asset: $assetPath")
          }
        }
      } else {
        if (!destFile.exists()) {
          destFile.mkdirs()
        }
        for (asset in assets) {
          val newSource = if (assetPath.isEmpty()) asset else "$assetPath${File.separator}$asset"
          val newDest = File(destFile, asset)
          copyAssetPathRecursive(context, newSource, newDest)
        }
      }
    } catch (e: IOException) {
      copyAssetFile(context, assetPath, destFile)
    }
  }


  @Throws(IOException::class)
  private fun copyAssetFile(context: Context, assetFileName: String, destinationFile: File) {
    AILog.d(TAG, "Copying asset file: $assetFileName to ${destinationFile.absolutePath}")
    destinationFile.parentFile?.mkdirs()
    context.assets.open(assetFileName).use { inputStream ->
      FileOutputStream(destinationFile).use { outputStream ->
        inputStream.copyTo(outputStream)
      }
    }
  }

  private fun deleteRecursive(fileOrDirectory: File): Boolean {
    if (fileOrDirectory.isDirectory) {
      fileOrDirectory.listFiles()?.forEach { child ->
        deleteRecursive(child)
      }
    }
    return fileOrDirectory.delete()
  }
}
