package com.aispeech.hybridspeech

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import com.aispeech.aibase.AILog

class BootReceiver : BroadcastReceiver() {
  override fun onReceive(context: Context, intent: Intent?) {
    if (intent == null) return
    val action = intent.action
    if (action == Intent.ACTION_BOOT_COMPLETED ||
      action == Intent.ACTION_LOCKED_BOOT_COMPLETED ||
      action == Intent.ACTION_MY_PACKAGE_REPLACED) {
      AILog.i("BootReceiver", "开机广播收到，准备启动 HybridSpeechService")
      val serviceIntent = Intent(context, HybridSpeechService::class.java)
      context.startForegroundService(serviceIntent)
    }
  }
}
