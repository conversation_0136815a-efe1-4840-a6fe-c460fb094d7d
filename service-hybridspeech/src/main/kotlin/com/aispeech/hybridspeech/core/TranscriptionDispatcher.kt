package com.aispeech.hybridspeech.core

import android.content.Context
import com.aispeech.aibase.AILog
import com.aispeech.hybridspeech.OfflineTranslationConfig
import com.aispeech.hybridspeech.TranscriptionResult
import com.aispeech.hybridspeech.asr.offline.OfflineTranslationEngine
import com.aispeech.hybridspeech.asr.offline.TranslationConfig
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Job
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.SharedFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.launch
import kotlinx.serialization.json.jsonObject
import kotlinx.serialization.json.jsonPrimitive
import java.util.concurrent.ConcurrentHashMap

/**
 * 转写分发器
 * 统一处理在线/离线ASR结果，输出统一分段结构给上层业务
 * 在线模式：直接分发第三方已分段结果
 * 离线模式：先本地分段再分发，支持离线翻译
 */
class TranscriptionDispatcher(private val context: Context): CoroutineScope {
  companion object {
    private const val TAG = "TranscriptionDispatcher"
    private const val SEGMENT_TIMEOUT_MS = 5000L // 5秒分段超时
    private const val MAX_SEGMENT_LENGTH = 100 // 最大分段长度（字符数）
  }

  // 协程作用域委托 - 使用结构化并发
  private val scopeDelegate = CoroutineScopeManager.createModuleScopeDelegate(
    moduleName = TAG,
    parentScope = CoroutineScopeManager.getIOParentScope()
  )

  // 委托CoroutineScope接口到scopeDelegate
  override val coroutineContext = scopeDelegate.coroutineContext

  private val _transcriptionResultFlow = MutableSharedFlow<TranscriptionResult>(
    replay = 0,
    extraBufferCapacity = 100
  )
  val transcriptionResultFlow: SharedFlow<TranscriptionResult> =
    _transcriptionResultFlow.asSharedFlow()

  private var isRunning = false
  private var onlineProcessingJob: Job? = null
  private var offlineProcessingJob: Job? = null
  private var segmentTimeoutJob: Job? = null

  // 使用ParagraphProcessor替换原来的离线分段逻辑
  private val paragraphProcessor = ParagraphProcessor(maxParagraphWords = MAX_SEGMENT_LENGTH)

  // 离线翻译引擎
  private var offlineTranslationEngine: OfflineTranslationEngine? = null
  private var translationConfig: OfflineTranslationConfig? = null

  // 缓存原文结果，用于翻译后复制数据结构
  private val originalTextCache = ConcurrentHashMap<String, TranscriptionResult.ProcessingTextResult>()

  /**
   * 设置翻译配置
   */
  fun setTranslationConfig(config: OfflineTranslationConfig?) {
    this.translationConfig = config
    AILog.i(TAG, "setTranslationConfig, translate=${config?.toLanguage}")

    if (config?.enabled == true) {
      initializeTranslationEngine(config)
    } else {
      releaseTranslationEngine()
    }
  }

  /**
   * 初始化翻译引擎
   */
  private fun initializeTranslationEngine(config: OfflineTranslationConfig) {
    try {
      offlineTranslationEngine = OfflineTranslationEngine().apply {
        setTaskName(config.taskName)
        if (config.resourcePath.isNotEmpty()) {
          setResourcePath(config.resourcePath)
        }

        setStatusChangeListener { status ->
          AILog.i(TAG, "Translation engine status: $status")
        }
      }

      // 监听翻译结果
      launch {
        offlineTranslationEngine?.translationResultFlow?.collect { result ->
          handleTranslationResult(result)
        }
      }

      // 初始化翻译引擎
      val initSuccess = offlineTranslationEngine?.initialize() ?: false
      if (initSuccess) {
        AILog.i(TAG, "Translation engine initialized successfully")
      } else {
        AILog.e(TAG, "Failed to initialize translation engine")
      }
    } catch (e: Exception) {
      AILog.e(TAG, "Error initializing translation engine", e)
    }
  }

  /**
   * 释放翻译引擎
   */
  private fun releaseTranslationEngine() {
    offlineTranslationEngine?.release()
    offlineTranslationEngine = null
    AILog.i(TAG, "Translation engine released")
  }

  /**
   * 开始分发处理
   */
  fun start(
    onlineResultFlow: SharedFlow<TranscriptionResult>,
    offlineResultFlow: SharedFlow<TranscriptionResult>
  ): Boolean {
    try {
      if (isRunning) {
        AILog.w(TAG, "Already running")
        return true
      }

      // 处理在线结果
      onlineProcessingJob =
        launch {
          onlineResultFlow.collect { result ->
            processOnlineResult(result)
          }
        }

      // 处理离线结果
      offlineProcessingJob =
        launch {
          offlineResultFlow.collect { result ->
            processOfflineResult(result)
          }
        }



      isRunning = true
      AILog.i(TAG, "Transcription dispatcher started")
      return true

    } catch (e: Exception) {
      AILog.e(TAG, "Error starting transcription dispatcher", e)
      return false
    }
  }

  /**
   * 停止分发处理
   */
  fun stop() {
    isRunning = false

    onlineProcessingJob?.cancel()
    offlineProcessingJob?.cancel()
    segmentTimeoutJob?.cancel()

    // 释放翻译引擎
    releaseTranslationEngine()

    // 重置段落处理器状态
    launch {
      try {
        resetParagraphProcessor()
        AILog.d(TAG, "Paragraph processor reset completed")
      } catch (e: Exception) {
        AILog.e(TAG, "Error resetting paragraph processor", e)
      }
    }

    // 清理缓存
    originalTextCache.clear()

    AILog.i(TAG, "Transcription dispatcher stopped")
  }


  /**
   * 关闭分发器
   * 包括停止分发处理、释放翻译引擎和关闭协程作用域
   */
  fun shutdown(reason: String = "TranscriptionDispatcher shutdown") {
    // 先停止分发处理
    if (isRunning) {
      stop()
    }

    // 关闭协程作用域
    scopeDelegate.shutdown(reason)
    AILog.i(TAG, "Transcription dispatcher shut down: $reason")
  }
  /**
   * 处理在线结果 - 在线服务已经分段处理过，直接分发
   */
  private fun processOnlineResult(result: TranscriptionResult) {
    try {
      val text = when (result) {
        is TranscriptionResult.FinalTextResult -> result.text
        is TranscriptionResult.ProcessingTextResult -> result.text
        is TranscriptionResult.IntermediateResult -> result.`var`
        is TranscriptionResult.AgendaResult -> result.text
        is TranscriptionResult.StartResult -> "连接建立"
        is TranscriptionResult.InitializationResult -> "初始化文本"
      }
      AILog.d(TAG, "Processing online result: $text")

      // 在线结果已经分段处理过，直接分发
      _transcriptionResultFlow.tryEmit(result)
      AILog.v(TAG, "Online result dispatched: ${result::class.simpleName}")

    } catch (e: Exception) {
      AILog.e(TAG, "Error processing online result", e)
    }
  }

  /**
   * 处理离线结果
   */
  private fun processOfflineResult(result: TranscriptionResult) {
    try {
      val text = when (result) {
        is TranscriptionResult.FinalTextResult -> result.text
        is TranscriptionResult.ProcessingTextResult -> result.text
        is TranscriptionResult.IntermediateResult -> result.`var`
        is TranscriptionResult.AgendaResult -> result.text
        is TranscriptionResult.StartResult -> "连接建立"
        is TranscriptionResult.InitializationResult -> "初始化文本"
      }
      AILog.d(TAG, "Processing offline result: $text")

      when (result) {
        is TranscriptionResult.StartResult -> {
          // 连接建立结果，直接分发
          AILog.d(TAG, "Offline: Start result - ${result.message}")
          _transcriptionResultFlow.tryEmit(result)
        }

        is TranscriptionResult.FinalTextResult -> {
          // 最终结果，直接分发，不需要分段处理
          AILog.d(TAG, "Offline: Final text result - ${result.text}")
          _transcriptionResultFlow.tryEmit(result)
        }

        is TranscriptionResult.AgendaResult -> {
          // 议程结果，直接分发
          AILog.d(TAG, "Offline: Agenda result - ${result.text} (seq: ${result.seq}, last: ${result.isLast})")
          _transcriptionResultFlow.tryEmit(result)
        }

        is TranscriptionResult.ProcessingTextResult -> {
          // 处理中的结果使用ParagraphProcessor进行分段处理
          AILog.v(TAG, "Offline: Processing text result - $text (isOnline: ${result.isOnline})")
          launch {
            handleOfflineProcessingResult(result)
          }
        }

        is TranscriptionResult.IntermediateResult -> {
          // 中间结果直接分发，不参与分段统计
          AILog.v(TAG, "Offline: Intermediate result - $text (isOnline: ${result.isOnline})")
          _transcriptionResultFlow.tryEmit(result)
        }

        is TranscriptionResult.InitializationResult -> TODO()
      }

    } catch (e: Exception) {
      AILog.e(TAG, "Error processing offline result", e)
    }
  }

  /**
   * 使用ParagraphProcessor处理离线ProcessingTextResult
   */
  private suspend fun handleOfflineProcessingResult(result: TranscriptionResult.ProcessingTextResult) {
    try {
      // 使用ParagraphProcessor处理结果，获取可能带有分段标记的新结果
      val processedResult = paragraphProcessor.process(result)

      AILog.v(TAG, "Processed offline result: text='${processedResult.text}', lineFeed=${processedResult.lineFeed}")

      // 分发原文结果
      _transcriptionResultFlow.tryEmit(processedResult)

      // 如果启用了翻译且是原文，则进行翻译
      if (shouldTranslate(processedResult)) {
        performTranslation(processedResult)
      }

    } catch (e: Exception) {
      AILog.e(TAG, "Error processing offline result with ParagraphProcessor", e)
      // 出错时直接分发原始结果
      _transcriptionResultFlow.tryEmit(result)
    }
  }

  /**
   * 判断是否需要翻译
   */
  private fun shouldTranslate(result: TranscriptionResult.ProcessingTextResult): Boolean {
    val config = translationConfig ?: return false
    return config.enabled &&
      result.contentType == TranscriptionResult.ContentType.ORIGINAL &&
      result.text.isNotBlank() &&
      offlineTranslationEngine != null
  }

  /**
   * 执行翻译
   */
  private fun performTranslation(originalResult: TranscriptionResult.ProcessingTextResult) {
    val config = translationConfig ?: return
    val engine = offlineTranslationEngine ?: return

    try {
      // 缓存原文结果，用于后续复制数据结构
      val cacheKey = "${originalResult.begin}-${originalResult.end}-${originalResult.text.hashCode()}"
      originalTextCache[cacheKey] = originalResult

      // 创建翻译配置
      val translationConfig = TranslationConfig(
        taskName = config.taskName,
        fromLanguage = config.fromLanguage,
        toLanguage = config.toLanguage,
        domain = config.domain,
        useNer = config.useNer,
        usePost = config.usePost,
        resourcePath = config.resourcePath,
        nerUserPath = config.nerUserPath,
        nerUserVocabs = config.nerUserVocabs
      )

      // 执行翻译
      val success = engine.translateText(originalResult.text, translationConfig)
      if (success) {
        AILog.d(TAG, "Translation started for: ${originalResult.text}")
      } else {
        AILog.w(TAG, "Failed to start translation for: ${originalResult.text}")
        // 清理缓存
        originalTextCache.remove(cacheKey)
      }

    } catch (e: Exception) {
      AILog.e(TAG, "Error performing translation", e)
    }
  }

  /**
   * 处理翻译结果
   */
  private fun handleTranslationResult(translationResult: String) {
    try {
      AILog.d(TAG, "Received translation result: $translationResult")

      if (translationResult.isBlank() || translationResult.startsWith("Error:")) {
        AILog.w(TAG, "Invalid or error translation result: $translationResult")
        return
      }

      // 解析翻译结果JSON
      val translatedText = parseTranslationResult(translationResult)
      if (translatedText.isBlank()) {
        AILog.w(TAG, "Failed to parse translation result")
        return
      }

      // 查找对应的原文结果
      val originalResult = findMatchingOriginalResult(translatedText)
      if (originalResult == null) {
        AILog.w(TAG, "No matching original result found for translation")
        return
      }

      // 创建翻译结果，复制原文的数据结构
      val translationResultCopy = originalResult.copy(
        text = translatedText,
        contentType = TranscriptionResult.ContentType.TRANSLATION
      )

      // 分发翻译结果
      _transcriptionResultFlow.tryEmit(translationResultCopy)
      AILog.d(TAG, "Translation result dispatched: $translatedText")

    } catch (e: Exception) {
      AILog.e(TAG, "Error handling translation result", e)
    }
  }

  /**
   * 解析翻译结果JSON，提取翻译文本
   */
  private fun parseTranslationResult(jsonResult: String): String {
    return try {
      // 解析翻译结果JSON格式: {"data":{"transRec":"I want to eat something"},"errno":0}
      val jsonObject = kotlinx.serialization.json.Json.parseToJsonElement(jsonResult).jsonObject
      val data = jsonObject["data"]?.jsonObject
      data?.get("transRec")?.jsonPrimitive?.content ?: ""
    } catch (e: Exception) {
      AILog.e(TAG, "Error parsing translation JSON", e)
      ""
    }
  }

  /**
   * 查找匹配的原文结果
   */
  private fun findMatchingOriginalResult(translatedText: String): TranscriptionResult.ProcessingTextResult? {
    if (originalTextCache.isEmpty()) {
      return null
    }

    val firstEntry = originalTextCache.entries.firstOrNull()
    return firstEntry?.let { entry ->
      // 移除已使用的缓存
      originalTextCache.remove(entry.key)
      entry.value
    }
  }


  /**
   * 重置段落处理器状态（用于停止录音时）
   */
  suspend fun resetParagraphProcessor() {
    paragraphProcessor.resetState()
  }



  /**
   * 获取分发器状态
   */
  fun getStatus(): TranscriptionDispatcherStatus {
    return TranscriptionDispatcherStatus(
      isRunning = isRunning,
      paragraphProcessorActive = true
    )
  }
}

/**
 * 转写分发器状态
 */
data class TranscriptionDispatcherStatus(
  val isRunning: Boolean,
  val paragraphProcessorActive: Boolean
)
