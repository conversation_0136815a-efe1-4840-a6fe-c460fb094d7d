package com.aispeech.hybridspeech.core

import com.aispeech.aibase.AILog
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.CoroutineExceptionHandler
import kotlinx.coroutines.CoroutineName
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.cancel
import kotlinx.coroutines.isActive
import kotlin.coroutines.ContinuationInterceptor
import kotlin.coroutines.CoroutineContext

/**
 * 协程作用域管理器 - 重构版本
 * 提供全局共享的父作用域，各模块通过委托模式创建子作用域
 * 遵循结构化并发原则，支持自动生命周期管理
 */
object CoroutineScopeManager {
  private const val TAG = "CoroutineScopeManager"

  // 全局异常处理器
  private val globalExceptionHandler = CoroutineExceptionHandler { _, exception ->
    AILog.e(TAG, "Uncaught coroutine exception", exception)
  }

  // 应用级父作用域 - 整个应用生命周期
  private val applicationParentScope = CoroutineScope(
    SupervisorJob() +
      Dispatchers.Default +
      globalExceptionHandler +
      CoroutineName("ApplicationParent")
  )

  // IO父作用域 - 用于IO密集型任务
  private val ioParentScope = CoroutineScope(
    SupervisorJob() +
      Dispatchers.IO +
      globalExceptionHandler +
      CoroutineName("IOParent")
  )

  // 主线程父作用域 - 用于UI更新
  private val mainParentScope = CoroutineScope(
    SupervisorJob() +
      Dispatchers.Main +
      globalExceptionHandler +
      CoroutineName("MainParent")
  )

  /**
   * 获取应用级父作用域
   */
//  fun getApplicationParentScope(): CoroutineScope = applicationParentScope

  /**
   * 获取IO父作用域
   */
  fun getIOParentScope(): CoroutineScope = ioParentScope

  /**
   * 获取主线程父作用域
   */
  fun getMainParentScope(): CoroutineScope = mainParentScope

  /**
   * 创建模块作用域委托
   * @param moduleName 模块名称
   * @param parentScope 父作用域，默认为IO父作用域
   * @param dispatcher 调度器，如果不指定则使用父作用域的调度器
   */
  fun createModuleScopeDelegate(
    moduleName: String,
    parentScope: CoroutineScope = ioParentScope,
    dispatcher: CoroutineDispatcher? = null
  ): ModuleScopeDelegate {
    return ModuleScopeDelegate(
      moduleName = moduleName,
      parentScope = parentScope,
      dispatcher = dispatcher
    )
  }

  /**
   * 关闭所有父作用域（应用退出时调用）
   */
  fun shutdown() {
    AILog.i(TAG, "Shutting down all parent scopes...")
    applicationParentScope.cancel("Application shutdown")
    ioParentScope.cancel("Application shutdown")
    mainParentScope.cancel("Application shutdown")
    AILog.i(TAG, "All parent scopes shut down")
  }

  /**
   * 检查父作用域是否活跃
   */
  fun isActive(): Boolean {
    return applicationParentScope.isActive &&
      ioParentScope.isActive &&
      mainParentScope.isActive
  }
}

/**
 * 模块作用域委托
 * 实现CoroutineScope接口，内部创建可独立取消的子作用域
 * 遵循结构化并发原则，支持优雅的生命周期管理
 */
class ModuleScopeDelegate(
  private val moduleName: String,
  private val parentScope: CoroutineScope,
  private val dispatcher: CoroutineDispatcher? = null
) : CoroutineScope {

  private val tag = "ModuleScope[$moduleName]"

  // 模块专用的子作用域
  private var moduleScope: CoroutineScope? = null

  // 模块异常处理器
  private val moduleExceptionHandler = CoroutineExceptionHandler { _, exception ->
    AILog.e(tag, "Uncaught exception in module $moduleName", exception)
  }

  override val coroutineContext: CoroutineContext
    get() = getOrCreateModuleScope().coroutineContext

  /**
   * 获取或创建模块作用域
   */
  private fun getOrCreateModuleScope(): CoroutineScope {
    return moduleScope ?: synchronized(this) {
      moduleScope ?: createNewModuleScope().also { moduleScope = it }
    }
  }

  /**
   * 创建新的模块作用域
   */
  private fun createNewModuleScope(): CoroutineScope {
    val parentJob = parentScope.coroutineContext[Job]
    val parentDispatcher = parentScope.coroutineContext[ContinuationInterceptor] as CoroutineDispatcher
    val context = SupervisorJob(parentJob) +
      (dispatcher ?: parentDispatcher) +
      moduleExceptionHandler +
      CoroutineName("${moduleName}Module")

    AILog.d(tag, "Created new module scope for $moduleName")
    return CoroutineScope(context)
  }

  /**
   * 关闭模块作用域
   * 取消所有子协程，但不影响其他模块
   */
  fun shutdown(reason: String = "Module $moduleName shutdown") {
    synchronized(this) {
      moduleScope?.let { scope ->
        scope.cancel(reason)
        moduleScope = null
        AILog.i(tag, "Module scope shut down: $reason")
      }
    }
  }

  /**
   * 检查模块作用域是否活跃
   */
  fun isActive(): Boolean {
    return moduleScope?.isActive == true
  }

  /**
   * 重启模块作用域
   * 用于模块重新初始化的场景
   */
  fun restart(reason: String = "Module $moduleName restart") {
    synchronized(this) {
      shutdown("$reason - shutting down old scope")
      // 下次访问时会自动创建新的作用域
      AILog.i(tag, "Module scope prepared for restart")
    }
  }
}
