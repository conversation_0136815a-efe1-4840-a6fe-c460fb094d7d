package com.aispeech.hybridspeech.core

import android.os.RemoteException
import com.aispeech.aibase.AILog
import com.aispeech.hybridspeech.AudioRecordingConfig
import com.aispeech.hybridspeech.IRecordProgressCallback
import com.aispeech.hybridspeech.RecordingProgressConfig
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.SharedFlow
import kotlinx.coroutines.isActive
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.atomic.AtomicLong

/**
 * 录音进度管理器
 * 负责监听PCM数据流，计算录音时长，并管理进度回调
 */
class RecordingProgressManager {
  companion object {
    private const val TAG = "RecordingProgressManager"
  }

  // 录音相关状态
  private var isRecording = false
  private var recordingStartTime = 0L
  private val totalSamplesReceived = AtomicLong(0L)
  private var audioConfig: AudioRecordingConfig? = null
  private var progressConfig: RecordingProgressConfig? = null

  // 回调管理
  private val callbacks = ConcurrentHashMap<IRecordProgressCallback, CallbackInfo>()

  // 协程任务
  private var pcmMonitorJob: Job? = null
  private val callbackJobs = ConcurrentHashMap<IRecordProgressCallback, Job>()

  /**
   * 回调信息
   */
  private data class CallbackInfo(
    val callback: IRecordProgressCallback,
    val intervalMs: Int
  )

  /**
   * 开始录音进度监控
   * @param pcmDataFlow PCM数据流
   * @param audioConfig 音频配置
   * @param progressConfig 进度配置
   */
  fun startProgressMonitoring(
    pcmDataFlow: SharedFlow<ByteArray>,
    audioConfig: AudioRecordingConfig,
    progressConfig: RecordingProgressConfig
  ) {
    if (isRecording) {
      AILog.w(TAG, "Progress monitoring already started")
      return
    }

    AILog.i(TAG, "Starting progress monitoring")

    this.audioConfig = audioConfig
    this.progressConfig = progressConfig
    this.isRecording = true
    this.recordingStartTime = System.currentTimeMillis()
    this.totalSamplesReceived.set(0L)

    // 通知开始录音并启动已注册的回调
    notifyRecordingStarted()

    // 开始监听PCM数据流
    pcmMonitorJob = launchIO {
      pcmDataFlow.safeCollect(
        tag = TAG,
        onError = { error ->
          AILog.e(TAG, "Error in PCM data monitoring", error)
          notifyError("PCM monitoring error: ${error.message}")
        }
      ) { pcmData ->
        processPcmData(pcmData)
      }
    }

    AILog.i(TAG, "Progress monitoring started successfully")
  }

  /**
   * 停止录音进度监控
   */
  fun stopProgressMonitoring() {
    if (!isRecording) {
      AILog.w(TAG, "Progress monitoring not running")
      return
    }

    AILog.i(TAG, "Stopping progress monitoring")

    // 停止PCM监控任务
    pcmMonitorJob?.cancel()
    pcmMonitorJob = null

    // 停止所有回调任务
    callbackJobs.forEach { (_, job) -> job.cancel() }
    callbackJobs.clear()

    // 通知录音停止
    val totalDuration = getCurrentDuration()
    notifyRecordingStopped(totalDuration)

    // isRecording 不能在 [getCurrentDuration] 前面，会读不到结束的时长
    isRecording = false

    // 清理状态
    totalSamplesReceived.set(0L)
    audioConfig = null
    progressConfig = null

    AILog.i(TAG, "Progress monitoring stopped")
  }

  /**
   * 注册进度回调
   * @param callback 回调接口
   * @param intervalMs 回调间隔（毫秒）
   */
  fun registerProgressCallback(callback: IRecordProgressCallback, intervalMs: Int) {
    // 先验证基本范围，如果没有配置就使用默认范围
    val validIntervalMs = if (progressConfig != null) {
      intervalMs.coerceIn(progressConfig!!.minIntervalMs, progressConfig!!.maxIntervalMs)
    } else {
      intervalMs.coerceIn(100, 1000) // 默认范围
    }

    if (validIntervalMs != intervalMs) {
      AILog.w(TAG, "Interval $intervalMs adjusted to $validIntervalMs")
    }

    val callbackInfo = CallbackInfo(callback, validIntervalMs)
    callbacks[callback] = callbackInfo

    // 如果正在录音，立即启动回调任务
    if (isRecording) {
      startCallbackTask(callbackInfo)
    }

    AILog.i(TAG, "Registered progress callback with interval ${validIntervalMs}ms (recording: $isRecording)")
  }

  /**
   * 取消注册进度回调
   */
  fun unregisterProgressCallback(callback: IRecordProgressCallback) {
    callbacks.remove(callback)

    // 停止相应的回调任务
    callbackJobs.remove(callback)?.cancel()

    AILog.i(TAG, "Unregistered progress callback")
  }

  /**
   * 取消所有进度回调
   */
  fun unregisterAllProgressCallbacks() {
    callbacks.clear()

    // 停止所有回调任务
    callbackJobs.forEach { (_, job) -> job.cancel() }
    callbackJobs.clear()

    AILog.i(TAG, "Unregistered all progress callbacks")
  }

  /**
   * 获取当前录音时长
   * @return 录音时长（毫秒）
   */
  fun getCurrentDuration(): Long {
    if (!isRecording) {
      return 0L
    }

    val config = audioConfig ?: return 0L
    val samples = totalSamplesReceived.get()

    // 根据音频配置计算时长
    return calculateDurationFromSamples(samples, config)
  }

  /**
   * 处理PCM数据
   */
  private fun processPcmData(pcmData: ByteArray) {
    val config = audioConfig ?: return

    // 计算样本数量
    val bytesPerSample = config.bitsPerSample / 8
    val samplesInChunk = pcmData.size / (bytesPerSample * config.channels)

    // 更新总样本数
    totalSamplesReceived.addAndGet(samplesInChunk.toLong())
  }

  /**
   * 启动回调任务
   */
  private fun startCallbackTask(callbackInfo: CallbackInfo) {
    // 停止已存在的任务
    callbackJobs[callbackInfo.callback]?.cancel()

    // 启动新的回调任务
    val job = launchIO {
      while (isActive && isRecording) {
        try {
          val currentDuration = getCurrentDuration()
          callbackInfo.callback.onRecordingProgress(currentDuration)
        } catch (e: RemoteException) {
          AILog.w(TAG, "Callback failed, removing: ${e.message}")
          callbacks.remove(callbackInfo.callback)
          break
        } catch (e: Exception) {
          AILog.e(TAG, "Error in progress callback", e)
        }

        delay(callbackInfo.intervalMs.toLong())
      }
    }

    callbackJobs[callbackInfo.callback] = job
  }

  /**
   * 通知录音开始
   */
  private fun notifyRecordingStarted() {
    callbacks.forEach { (callback, callbackInfo) ->
      try {
        callback.onRecordingStarted()
        // 启动回调任务
        startCallbackTask(callbackInfo)
      } catch (e: RemoteException) {
        AILog.w(TAG, "Callback failed during start notification: ${e.message}")
        callbacks.remove(callback)
      } catch (e: Exception) {
        AILog.e(TAG, "Error notifying recording started", e)
      }
    }
  }

  /**
   * 通知录音停止
   */
  private fun notifyRecordingStopped(totalDurationMs: Long) {
    callbacks.forEach { (callback, _) ->
      try {
        callback.onRecordingStopped(totalDurationMs)
      } catch (e: RemoteException) {
        AILog.w(TAG, "Callback failed during stop notification: ${e.message}")
      } catch (e: Exception) {
        AILog.e(TAG, "Error notifying recording stopped", e)
      }
    }
  }

  /**
   * 通知错误
   */
  private fun notifyError(error: String) {
    callbacks.forEach { (callback, _) ->
      try {
        callback.onError(error)
      } catch (e: RemoteException) {
        AILog.w(TAG, "Callback failed during error notification: ${e.message}")
      } catch (e: Exception) {
        AILog.e(TAG, "Error notifying error", e)
      }
    }
  }

  /**
   * 根据样本数和音频配置计算时长
   */
  private fun calculateDurationFromSamples(samples: Long, config: AudioRecordingConfig): Long {
    return (samples * 1000L) / config.sampleRate
  }

  /**
   * 检查是否正在录音
   */
  fun isRecording(): Boolean = isRecording

  /**
   * 获取已注册的回调数量
   */
  fun getCallbackCount(): Int = callbacks.size
}