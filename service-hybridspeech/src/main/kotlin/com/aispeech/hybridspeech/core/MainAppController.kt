package com.aispeech.hybridspeech.core

import android.content.Context
import com.aispeech.aibase.AILog
import com.aispeech.hybridspeech.IHybridSpeechConfigProvider
import com.aispeech.hybridspeech.IPauseRecordingCallback
import com.aispeech.hybridspeech.IRecordProgressCallback
import com.aispeech.hybridspeech.IResumeRecordingCallback
import com.aispeech.hybridspeech.IStartRecordingCallback
import com.aispeech.hybridspeech.IStopRecordingCallback
import com.aispeech.hybridspeech.PauseRecordingErrorCode
import com.aispeech.hybridspeech.RecordingConfig
import com.aispeech.hybridspeech.RecordingResultInfo
import com.aispeech.hybridspeech.ResumeRecordingErrorCode
import com.aispeech.hybridspeech.ServiceStatus
import com.aispeech.hybridspeech.StartRecordingErrorCode
import com.aispeech.hybridspeech.StopRecordingErrorCode
import com.aispeech.hybridspeech.TranscriptionResult
import com.aispeech.hybridspeech.asr.offline.OfflineOrchestrator
import com.aispeech.hybridspeech.asr.offline.OfflineOrchestratorStatus
import com.aispeech.hybridspeech.asr.online.NetworkStateMonitor
import com.aispeech.hybridspeech.asr.online.OnlineAsrOrchestrator
import com.aispeech.hybridspeech.asr.online.OnlineAsrStatus
import com.aispeech.hybridspeech.audio.AudioManager
import com.aispeech.hybridspeech.storage.RecordingResult
import com.aispeech.hybridspeech.storage.RecordingStorageManager
import com.aispeech.hybridspeech.storage.RecordingStorageManagerStatus
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.SharedFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.launch
import kotlinx.coroutines.withTimeoutOrNull
import java.io.File

/**
 * 主应用控制器 - 重构版本
 * 实现CoroutineScope接口，使用委托模式管理协程生命周期
 * 核心业务逻辑，用户交互，状态管理，协调各模块
 */
class MainAppController(private val context: Context) : CoroutineScope {
  companion object {
    private const val TAG = "MainAppController"
  }

  // 协程作用域委托 - 使用结构化并发
  private val scopeDelegate = CoroutineScopeManager.createModuleScopeDelegate(
    moduleName = TAG,
    parentScope = CoroutineScopeManager.getMainParentScope()
  )

  // 委托CoroutineScope接口到scopeDelegate
  override val coroutineContext = scopeDelegate.coroutineContext

  // 核心模块
  private val audioManager = AudioManager()
  private val onlineAsrOrchestrator = OnlineAsrOrchestrator()
  private val offlineOrchestrator = OfflineOrchestrator(context)
  private val recordingStorageManager = RecordingStorageManager()
  private val transcriptionDispatcher = TranscriptionDispatcher(context)

  // 网络状态监测器
  private val networkStateMonitor = NetworkStateMonitor(context, this)

  // 状态管理
  private var currentStatus = ServiceStatus.IDLE
  private var isOnlineMode = true
  private var recordingResult: RecordingResult? = null

  // 对外事件流
  private val _transcriptionResultFlow = MutableSharedFlow<TranscriptionResult>(
    replay = 0,
    extraBufferCapacity = 100
  )
  val transcriptionResultFlow: SharedFlow<TranscriptionResult> = _transcriptionResultFlow.asSharedFlow()

  private val _statusChangeFlow = MutableSharedFlow<Int>(
    replay = 1,
    extraBufferCapacity = 10
  )
  val statusChangeFlow: SharedFlow<Int> = _statusChangeFlow.asSharedFlow()

  private val _errorFlow = MutableSharedFlow<String>(
    replay = 0,
    extraBufferCapacity = 10
  )
  val errorFlow: SharedFlow<String> = _errorFlow.asSharedFlow()

  private var resultSubscriptionJob: Job? = null

  init {
    // 初始化网络监测器
    onlineAsrOrchestrator.setNetworkStateMonitor(networkStateMonitor)
    AILog.i(TAG, "MainAppController initialized with network monitoring")
  }

  /**
   * 异步开始录音和转写
   */
  fun startRecordingWithConfigAsync(config: RecordingConfig, callback: IStartRecordingCallback) {
    // 启动协程处理异步启动流程
    launch {
      try {
        // 检查当前状态
        if (currentStatus != ServiceStatus.IDLE) {
          AILog.w(TAG, "Cannot start recording, current status: $currentStatus")
          callback.onStartRecordingError(
            StartRecordingErrorCode.INVALID_STATE,
            "Cannot start recording, current status: $currentStatus"
          )
          return@launch
        }

        // 检查配置有效性
        if (config.pcmFilePath.isBlank() || config.mp3FilePath.isBlank()) {
          AILog.e(TAG, "Invalid config: file paths cannot be empty")
          callback.onStartRecordingError(
            StartRecordingErrorCode.INVALID_CONFIG,
            "PCM or MP3 file path is empty"
          )
          return@launch
        }

        AILog.i(TAG, "Starting async recording process")

        // 统一启动录音，内部会根据模式处理初始化
        startRecordingInternal(config, callback)

      } catch (e: Exception) {
        AILog.e(TAG, "Error in async start recording", e)
        callback.onStartRecordingError(
          StartRecordingErrorCode.UNKNOWN_ERROR,
          "Failed to start recording: ${e.message}"
        )
      }
    }
  }

  /**
   * 内部录音启动逻辑（统一入口）
   */
  private suspend fun startRecordingInternal(config: RecordingConfig, callback: IStartRecordingCallback) {
    try {
      AILog.i(TAG, "Starting recording with unified entry point")
      AILog.i(TAG, "Config: useOnlineMode=${config.useOnlineMode}, language=${config.language}")

      // 设置模式标志
      isOnlineMode = config.useOnlineMode

      // 如果是离线模式，需要先处理离线引擎初始化
      if (!config.useOnlineMode) {
        updateStatus(ServiceStatus.INITIALIZING)

        // 应用离线引擎配置
        config.offlineEngineConfig?.let { offlineConfig ->
          AILog.i(TAG, "Applying offline engine config: modelPath=${offlineConfig.modelPath}")
          if (!offlineOrchestrator.setConfig(offlineConfig.modelPath)) {
            AILog.e(TAG, "Failed to set offline engine config")
            _errorFlow.tryEmit("Failed to configure offline ASR engine - check model file path and permissions")
            updateStatus(ServiceStatus.ERROR)
            callback.onStartRecordingError(
              StartRecordingErrorCode.OFFLINE_ENGINE_INIT_FAILED,
              "Failed to configure offline ASR engine - check model file path and permissions"
            )
            return
          }
        }

        // 等待离线引擎初始化完成
        AILog.i(TAG, "Waiting for offline engine initialization...")
        if (!waitForOfflineEngineInitialization()) {
          AILog.e(TAG, "Offline engine initialization timeout or failed")
          _errorFlow.tryEmit("Offline ASR engine initialization timeout - check model file and configuration")
          updateStatus(ServiceStatus.ERROR)
          callback.onStartRecordingError(
            StartRecordingErrorCode.OFFLINE_ENGINE_INIT_FAILED,
            "Offline ASR engine initialization timeout - check model file and configuration"
          )
          return
        }
        AILog.i(TAG, "Offline engine initialization completed")

        // 应用翻译引擎配置
        config.offlineTranslationConfig?.let { offlineTranslationConfig ->
          AILog.d(TAG, "Applying offline translation config: $offlineTranslationConfig")
          if (offlineTranslationConfig.enabled) {
            transcriptionDispatcher.setTranslationConfig(offlineTranslationConfig)
          }
        }
      }

      // 应用音频配置并启动音频采集（包含MP3编码配置和进度配置）
      AILog.i(TAG, "Starting audio capture...")
      if (!audioManager.startAudioCaptureWithFullConfig(config.audioConfig, config.mp3Config, config.progressConfig)) {
        AILog.e(TAG, "Failed to start audio capture with full config")
        updateStatus(ServiceStatus.ERROR)
        callback.onStartRecordingError(
          StartRecordingErrorCode.AUDIO_CAPTURE_START_FAILED,
          "Failed to start audio capture - check microphone permissions and availability"
        )
        return
      }
      AILog.i(TAG, "Audio capture started successfully")

      // 启动存储管理器，直接使用配置中的文件路径
      AILog.i(TAG, "Starting recording storage manager with specified file paths")

      if (!recordingStorageManager.startRecordingWithPaths(
          config.pcmFilePath,
          config.mp3FilePath,
          audioManager.pcmDataFlow,
          audioManager.mp3DataFlow,
          config.mp3Config
        )) {
        AILog.e(TAG, "Failed to start recording storage manager with paths")
        audioManager.stopAudioCapture()
        updateStatus(ServiceStatus.ERROR)
        callback.onStartRecordingError(
          StartRecordingErrorCode.STORAGE_MANAGER_START_FAILED,
          "Failed to start recording storage - check storage permissions and disk space"
        )
        return
      }

      AILog.i(TAG, "Recording storage manager started successfully")

      // 根据模式启动对应的ASR处理器并获取结果流
      val (onlineResultFlow, offlineResultFlow) = if (config.useOnlineMode) {
        val onlineFlow = startOnlineAsrMode(config, callback) ?: return
        val emptyOfflineFlow = MutableSharedFlow<TranscriptionResult>().asSharedFlow()
        Pair(onlineFlow, emptyOfflineFlow)
      } else {
        val offlineFlow = startOfflineAsrMode(config, callback) ?: return
        val emptyOnlineFlow = MutableSharedFlow<TranscriptionResult>().asSharedFlow()
        Pair(emptyOnlineFlow, offlineFlow)
      }

      // 启动转写分发器
      transcriptionDispatcher.start(onlineResultFlow, offlineResultFlow)

      // 订阅结果
      subscribeToTranscriptionResults()

      updateStatus(ServiceStatus.RECORDING)
      callback.onStartRecordingSuccess()

    } catch (e: Exception) {
      AILog.e(TAG, "Error in startRecordingInternal", e)
      stopRecordingInternal()
      updateStatus(ServiceStatus.ERROR)
      callback.onStartRecordingError(
        StartRecordingErrorCode.UNKNOWN_ERROR,
        "Failed to start recording: ${e.message}"
      )
    }
  }

  /**
   * 启动在线ASR模式
   */
  private suspend fun startOnlineAsrMode(config: RecordingConfig, callback: IStartRecordingCallback): SharedFlow<TranscriptionResult>? {
    AILog.i(TAG, "Starting online ASR mode...")

    // 设置存储管理器到 OnlineAsrOrchestrator（用于续传支持）
    onlineAsrOrchestrator.setStorageManager(recordingStorageManager)
    AILog.i(TAG, "Storage manager set for online ASR orchestrator (resume support)")

    // 应用在线ASR配置
    config.onlineAsrConfig?.let { onlineConfig ->
      AILog.i(TAG, "Applying online ASR config: serverUrl=${onlineConfig.serverUrl}")
      onlineAsrOrchestrator.setConfig(onlineConfig.serverUrl, onlineConfig.audioType)
    }

    AILog.i(TAG, "Starting online ASR orchestrator")

    // 使用统一的入口方法，内部会根据配置自动选择音频类型和处理续传
    val startResult = onlineAsrOrchestrator.startWithRecordingConfig(
      config, audioManager.pcmDataFlow, audioManager.mp3DataFlow
    )

    if (!startResult.success) {
      AILog.e(TAG, "Failed to start online ASR: ${startResult.errorMessage}")
      stopRecordingInternal()
      updateStatus(ServiceStatus.ERROR)

      // 根据错误类型选择合适的错误码
      val errorCode = when (startResult.errorCode) {
        "NETWORK_CONFIG_ERROR" -> StartRecordingErrorCode.CONFIG_PROVIDER_ERROR
        "MISSING_DATA_FLOW" -> StartRecordingErrorCode.AUDIO_CAPTURE_START_FAILED
        "ASR_START_FAILED" -> StartRecordingErrorCode.ONLINE_ASR_START_FAILED
        else -> StartRecordingErrorCode.UNKNOWN_ERROR
      }

      callback.onStartRecordingError(
        errorCode,
        startResult.errorMessage ?: "Failed to start online ASR"
      )
      return null
    }

    AILog.i(TAG, "Online ASR orchestrator started successfully")
    return onlineAsrOrchestrator.transcriptionResultFlow
  }

  /**
   * 启动离线ASR模式
   */
  private suspend fun startOfflineAsrMode(config: RecordingConfig, callback: IStartRecordingCallback): SharedFlow<TranscriptionResult>? {
    AILog.i(TAG, "Starting offline ASR mode...")

    // 启动离线ASR编排器
    if (!offlineOrchestrator.start(audioManager.pcmDataFlow)) {
      AILog.e(TAG, "Failed to start offline ASR orchestrator")
      stopRecordingInternal()
      updateStatus(ServiceStatus.ERROR)
      callback.onStartRecordingError(
        StartRecordingErrorCode.OFFLINE_ASR_START_FAILED,
        "Failed to start offline ASR - check offline model availability"
      )
      return null
    }

    AILog.i(TAG, "Offline ASR orchestrator started successfully")
    return offlineOrchestrator.transcriptionResultFlow
  }

  /**
   * 异步停止录音和转写，并通过回调返回结果
   */
  fun stopRecordingWithResultAsync(callback: IStopRecordingCallback) {
    // 启动协程处理异步停止流程
    launch {
      try {
        // 检查当前状态 - 允许在RECORDING或PAUSED状态下停止录音
        if (currentStatus != ServiceStatus.RECORDING && currentStatus != ServiceStatus.PAUSED) {
          AILog.w(TAG, "Cannot stop recording, current status: $currentStatus")
          callback.onStopRecordingError(
            StopRecordingErrorCode.INVALID_STATE,
            "Cannot stop recording, current status: $currentStatus"
          )
          return@launch
        }

        AILog.i(TAG, "Starting async stop recording process")
        updateStatus(ServiceStatus.PROCESSING)

        // 执行异步停止流程
        val result = stopRecordingInternalWithResult()

        if (result != null) {
          AILog.i(TAG, "Async stop recording completed successfully")
          updateStatus(ServiceStatus.IDLE)
          callback.onStopRecordingSuccess(result)
        } else {
          AILog.e(TAG, "Async stop recording failed")
          updateStatus(ServiceStatus.ERROR)
          callback.onStopRecordingError(
            StopRecordingErrorCode.UNKNOWN_ERROR,
            "Failed to stop recording - internal error"
          )
        }

      } catch (e: Exception) {
        AILog.e(TAG, "Error in async stop recording", e)
        updateStatus(ServiceStatus.ERROR)
        callback.onStopRecordingError(
          StopRecordingErrorCode.UNKNOWN_ERROR,
          "Failed to stop recording: ${e.message}"
        )
      }
    }
  }

  /**
   * 异步暂停录音和转写
   */
  fun pauseRecordingAsync(callback: IPauseRecordingCallback) {
    // 启动协程处理异步暂停流程
    launch {
      try {
        // 检查当前状态
        if (currentStatus != ServiceStatus.RECORDING) {
          AILog.w(TAG, "Cannot pause recording, current status: $currentStatus")
          callback.onPauseRecordingError(
            PauseRecordingErrorCode.INVALID_STATE,
            "Cannot pause recording, current status: $currentStatus"
          )
          return@launch
        }

        AILog.i(TAG, "Starting async pause recording process")

        // 执行暂停流程
        val success = pauseRecordingInternal()

        if (success) {
          AILog.i(TAG, "Async pause recording completed successfully")
          updateStatus(ServiceStatus.PAUSED)
          callback.onPauseRecordingSuccess()
        } else {
          AILog.e(TAG, "Async pause recording failed")
          callback.onPauseRecordingError(
            PauseRecordingErrorCode.UNKNOWN_ERROR,
            "Failed to pause recording - internal error"
          )
        }

      } catch (e: Exception) {
        AILog.e(TAG, "Error in async pause recording", e)
        callback.onPauseRecordingError(
          PauseRecordingErrorCode.UNKNOWN_ERROR,
          "Failed to pause recording: ${e.message}"
        )
      }
    }
  }

  /**
   * 异步继续录音和转写
   */
  fun resumeRecordingAsync(callback: IResumeRecordingCallback) {
    // 启动协程处理异步继续流程
    launch {
      try {
        // 检查当前状态
        if (currentStatus != ServiceStatus.PAUSED) {
          AILog.w(TAG, "Cannot resume recording, current status: $currentStatus")
          callback.onResumeRecordingError(
            ResumeRecordingErrorCode.INVALID_STATE,
            "Cannot resume recording, current status: $currentStatus"
          )
          return@launch
        }

        AILog.i(TAG, "Starting async resume recording process")

        // 执行继续流程
        val success = resumeRecordingInternal()

        if (success) {
          AILog.i(TAG, "Async resume recording completed successfully")
          updateStatus(ServiceStatus.RECORDING)
          callback.onResumeRecordingSuccess()
        } else {
          AILog.e(TAG, "Async resume recording failed")
          callback.onResumeRecordingError(
            ResumeRecordingErrorCode.UNKNOWN_ERROR,
            "Failed to resume recording - internal error"
          )
        }

      } catch (e: Exception) {
        AILog.e(TAG, "Error in async resume recording", e)
        callback.onResumeRecordingError(
          ResumeRecordingErrorCode.UNKNOWN_ERROR,
          "Failed to resume recording: ${e.message}"
        )
      }
    }
  }

  /**
   * 异步内部停止录音逻辑，带错误处理
   */
  private suspend fun stopRecordingInternalWithResult(): RecordingResultInfo? {
    return withTimeoutOrNull(30000L) { // 30秒超时
      try {
        // 取消结果订阅
        resultSubscriptionJob?.cancel()
        AILog.d(TAG, "Result subscription cancelled")

        // 停止转写分发器
        transcriptionDispatcher.stop()
        AILog.d(TAG, "Transcription dispatcher stopped")

        // 停止ASR处理
        if (isOnlineMode) {
          onlineAsrOrchestrator.stop()
          AILog.d(TAG, "Online ASR orchestrator stopped")
        } else {
          offlineOrchestrator.stop()
          AILog.d(TAG, "Offline orchestrator stopped")
        }

        // 停止存储管理器
        recordingResult = recordingStorageManager.stopRecording()
        if (recordingResult == null) {
          AILog.e(TAG, "Failed to get recording result from storage manager")
          return@withTimeoutOrNull null
        }
        AILog.d(TAG, "Recording storage manager stopped")

        // 停止音频采集
        audioManager.stopAudioCapture()
        AILog.d(TAG, "Audio capture stopped")

        // 创建录音结果信息
        val duration = getRecordingDuration()

        recordingResult?.let { result ->
          val pcmFile = File(result.pcmFilePath)
          val mp3File = File(result.mp3FilePath)

          val recordingResultInfo = com.aispeech.hybridspeech.RecordingResultInfo(
            pcmFilePath = result.pcmFilePath,
            mp3FilePath = result.mp3FilePath,
            durationMs = duration,
            fileSizeBytes = pcmFile.length() + mp3File.length(),
            audioConfig = audioManager.getAudioConfig()?.let { audioConfig ->
              com.aispeech.hybridspeech.AudioRecordingConfig(
                sampleRate = audioConfig.sampleRate,
                channels = audioConfig.channels,
                bitsPerSample = audioConfig.bitsPerSample,
                bufferSizeFactor = 2
              )
            } ?: com.aispeech.hybridspeech.AudioRecordingConfig.createDefault()
          )

          AILog.i(TAG, "Recording result info created successfully")
          return@withTimeoutOrNull recordingResultInfo
        } ?: run {
          AILog.e(TAG, "Recording result is null")
          return@withTimeoutOrNull null
        }

      } catch (e: Exception) {
        AILog.e(TAG, "Unexpected error in async stop recording", e)
        return@withTimeoutOrNull null
      }
    } ?: run {
      // 超时处理
      AILog.e(TAG, "Async stop recording timeout")
      null
    }
  }


  /**
   * 内部暂停录音逻辑
   */
  private suspend fun pauseRecordingInternal(): Boolean {
    return try {
      AILog.i(TAG, "Pausing recording components")

      // 暂停音频采集
      if (!audioManager.pauseAudioCapture()) {
        AILog.e(TAG, "Failed to pause audio capture")
        return false
      }

      // 根据模式暂停对应的ASR处理
      if (isOnlineMode) {
        try {
          AILog.i(TAG, "Pausing online ASR orchestrator")
          onlineAsrOrchestrator.pauseOnlineAsr()
        } catch (e: Exception) {
          AILog.e(TAG, "Failed to pause online ASR orchestrator", e)
          // 尝试恢复音频采集，因为在线ASR暂停失败
          audioManager.resumeAudioCapture()
          return false
        }
      } else {
        try {
          AILog.i(TAG, "Pausing offline ASR orchestrator")
          offlineOrchestrator.pauseOfflineAsr()
        } catch (e: Exception) {
          AILog.e(TAG, "Failed to pause offline ASR orchestrator", e)
          // 尝试恢复音频采集，因为离线ASR暂停失败
          audioManager.resumeAudioCapture()
          return false
        }
      }

      AILog.i(TAG, "Recording paused successfully")
      true
    } catch (e: Exception) {
      AILog.e(TAG, "Error pausing recording", e)
      false
    }
  }

  /**
   * 内部继续录音逻辑
   */
  private suspend fun resumeRecordingInternal(): Boolean {
    return try {
      AILog.i(TAG, "Resuming recording components")

      // 根据模式恢复对应的ASR处理
      if (isOnlineMode) {
        try {
          AILog.i(TAG, "Resuming online ASR orchestrator")
          onlineAsrOrchestrator.resumeOnlineAsr()
        } catch (e: Exception) {
          AILog.e(TAG, "Failed to resume online ASR orchestrator", e)
          return false
        }
      } else {
        try {
          AILog.i(TAG, "Resuming offline ASR orchestrator")
          offlineOrchestrator.resumeOfflineAsr(audioManager.pcmDataFlow)
        } catch (e: Exception) {
          AILog.e(TAG, "Failed to resume offline ASR orchestrator", e)
          return false
        }
      }

      // 恢复音频采集
      if (!audioManager.resumeAudioCapture()) {
        AILog.e(TAG, "Failed to resume audio capture")
        // 如果音频采集恢复失败，需要重新暂停对应的ASR
        if (isOnlineMode) {
          try {
            onlineAsrOrchestrator.pauseOnlineAsr()
          } catch (pauseException: Exception) {
            AILog.e(TAG, "Failed to re-pause online ASR after audio resume failure", pauseException)
          }
        } else {
          try {
            offlineOrchestrator.pauseOfflineAsr()
          } catch (pauseException: Exception) {
            AILog.e(TAG, "Failed to re-pause offline ASR after audio resume failure", pauseException)
          }
        }
        return false
      }

      AILog.i(TAG, "Recording resumed successfully")
      true
    } catch (e: Exception) {
      AILog.e(TAG, "Error resuming recording", e)
      false
    }
  }


  /**
   * 内部停止录音逻辑
   */
  private suspend fun stopRecordingInternal() {
    // 取消结果订阅
    resultSubscriptionJob?.cancel()

    // 停止转写分发器
    transcriptionDispatcher.stop()

    // 停止ASR处理
    if (isOnlineMode) {
      onlineAsrOrchestrator.stop()
    } else {
      offlineOrchestrator.stop()
    }

    // 停止存储管理器
    recordingResult = recordingStorageManager.stopRecording()

    // 停止音频采集
    audioManager.stopAudioCapture()

    // 清理配置提供者引用
    onlineAsrOrchestrator.setConfigProvider(null)
  }

  /**
   * 内部停止录音逻辑 (立即返回，异步停止)
   * 用于需要快速响应的场景
   */
  private fun stopRecordingInternalAsync() {
    // 取消结果订阅
    resultSubscriptionJob?.cancel()

    // 停止转写分发器
    transcriptionDispatcher.stop()

    // 异步停止ASR处理
    if (isOnlineMode) {
      onlineAsrOrchestrator.stopAsync()
    } else {
      offlineOrchestrator.stop()
    }

    // 停止存储管理器
    recordingResult = recordingStorageManager.stopRecording()

    // 停止音频采集
    audioManager.stopAudioCapture()

    // 清理配置提供者引用
    onlineAsrOrchestrator.setConfigProvider(null)
  }

  /**
   * 使用配置提供者启动在线录音
   * 服务端会通过配置提供者获取已签名的WebSocket URL和ASR续传配置
   */
  fun startRecordingWithProvider(
    config: RecordingConfig,
    configProvider: IHybridSpeechConfigProvider,
    callback: IStartRecordingCallback
  ) {
    launch {
      try {
        // 检查当前状态
        if (currentStatus != ServiceStatus.IDLE) {
          AILog.w(TAG, "Cannot start recording, current status: $currentStatus")
          callback.onStartRecordingError(
            StartRecordingErrorCode.INVALID_STATE,
            "Cannot start recording, current status: $currentStatus"
          )
          return@launch
        }

        AILog.i(TAG, "Starting recording with config provider")
        AILog.i(TAG, "Config: useOnlineMode=${config.useOnlineMode}")

        updateStatus(ServiceStatus.INITIALIZING)

        if (config.useOnlineMode) {
          val asrConfig = config.onlineAsrConfig ?: throw IllegalArgumentException("OnlineAsrConfig is required for online mode")
          onlineAsrOrchestrator.setConfigProvider(
            provider = configProvider,
            recordId = asrConfig.recordId,
            userId = asrConfig.userId,
            audioType = asrConfig.audioType
          )
        }

        startRecordingInternal(config, callback)
      } catch (e: Exception) {
        AILog.e(TAG, "Error starting online recording with provider", e)
        updateStatus(ServiceStatus.ERROR)
        callback.onStartRecordingError(
          StartRecordingErrorCode.UNKNOWN_ERROR,
          "Failed to start online recording: ${e.message}"
        )
      }
    }
  }

  /**
   * 获取录音文件路径
   */
  fun getRecordingFilePath(): String? {
    return recordingResult?.pcmFilePath
  }

  /**
   * 获取当前状态
   */
  fun getCurrentStatus(): Int {
    return currentStatus
  }

  // ============ 录音进度相关方法 ============

  /**
   * 获取当前录音时长
   */
  fun getRecordingDuration(): Long {
    return audioManager.getRecordingDuration()
  }

  /**
   * 注册录音进度回调
   */
  fun registerProgressCallback(callback: IRecordProgressCallback, intervalMs: Int) {
    audioManager.registerProgressCallback(callback, intervalMs)
  }

  /**
   * 取消注册录音进度回调
   */
  fun unregisterProgressCallback(callback: IRecordProgressCallback) {
    audioManager.unregisterProgressCallback(callback)
  }

  /**
   * 取消所有录音进度回调
   */
  fun unregisterAllProgressCallbacks() {
    audioManager.unregisterAllProgressCallbacks()
  }

  /**
   * 订阅转写结果
   */
  private fun subscribeToTranscriptionResults() {
    resultSubscriptionJob = launch {
      transcriptionDispatcher.transcriptionResultFlow.safeCollect(
        tag = TAG,
        onError = { error ->
          AILog.e(TAG, "Error in transcription result flow", error)
        }
      ) { result ->
        _transcriptionResultFlow.tryEmit(result)
      }
    }
  }

  /**
   * 更新状态
   */
  private fun updateStatus(newStatus: Int) {
    if (currentStatus != newStatus) {
      currentStatus = newStatus
      _statusChangeFlow.tryEmit(newStatus)
      AILog.d(TAG, "Status changed to: $newStatus")
    }
  }

  /**
   * 等待离线引擎初始化完成
   * 使用挂起函数，不阻塞主线程，最多等待10秒
   */
  private suspend fun waitForOfflineEngineInitialization(): Boolean {
    repeat(10) { attempt -> // 最多尝试10次，每次间隔1秒
      val status = offlineOrchestrator.getStatus()

      if (status.isInitialized && !status.isInitializing) {
        AILog.i(TAG, "Offline engine initialized successfully after ${attempt + 1} seconds")
        return true
      }

      if (!status.isInitializing && !status.isInitialized) {
        AILog.e(TAG, "Offline engine initialization appears to have failed")
        return false
      }

      AILog.d(TAG, "Waiting for offline engine initialization... (${attempt + 1}s)")
      delay(1000L)
    }

    AILog.e(TAG, "Timeout waiting for offline engine initialization after 10 seconds")
    return false
  }

  /**
   * 获取详细状态信息
   */
  fun getDetailedStatus(): MainControllerStatus {
    return MainControllerStatus(
      currentStatus = currentStatus,
      isOnlineMode = isOnlineMode,
      audioManagerRunning = audioManager.isRunning(),
      recordingStorageManagerStatus = recordingStorageManager.getStatus(),
      transcriptionDispatcherStatus = transcriptionDispatcher.getStatus(),
      onlineAsrStatus = if (isOnlineMode) onlineAsrOrchestrator.getStatus() else null,
      offlineOrchestratorStatus = if (!isOnlineMode) offlineOrchestrator.getStatus() else null,
      recordingResult = recordingResult
    )
  }

  /**
   * 关闭控制器协程作用域
   * 取消所有子协程，但不影响其他模块
   */
  fun shutdown(reason: String = "MainAppController shutdown") {
    // 停止网络监测器
    networkStateMonitor.stop()

    scopeDelegate.shutdown(reason)
    AILog.i(TAG, "Main controller scope shut down: $reason")
  }

  /**
   * 释放资源
   */
  fun release() {
    if (currentStatus == ServiceStatus.RECORDING) {
      stopRecordingInternalAsync()
    }

    // 取消结果订阅
    resultSubscriptionJob?.cancel()

    // 关闭各模块的协程作用域（包括资源清理）
    audioManager.shutdown("Application shutdown")
    transcriptionDispatcher.shutdown("Application shutdown")
    onlineAsrOrchestrator.shutdown("Application shutdown") // 内部会处理socket客户端销毁
    offlineOrchestrator.shutdown("Application shutdown") // 内部会处理引擎资源释放
    recordingStorageManager.shutdown("Application shutdown")

    // 关闭自己的作用域
    shutdown("Application shutdown")
    AILog.i(TAG, "Main controller released")
  }

  /**
   * 释放资源
   */
  suspend fun releaseAndJoin() {
    if (currentStatus == ServiceStatus.RECORDING) {
      stopRecordingInternal()
    }

    // 取消结果订阅
    resultSubscriptionJob?.cancel()

    // 关闭各模块的协程作用域（包括资源清理）
    audioManager.shutdown("Application shutdown")
    transcriptionDispatcher.shutdown("Application shutdown")
    onlineAsrOrchestrator.shutdown("Application shutdown") // 内部会处理socket客户端销毁
    offlineOrchestrator.shutdown("Application shutdown") // 内部会处理引擎资源释放
    recordingStorageManager.shutdown("Application shutdown")

    // 关闭自己的作用域
    shutdown("Application shutdown")

    // 最后关闭所有父作用域，这会自动取消所有剩余子作用域
    CoroutineScopeManager.shutdown()

    AILog.i(TAG, "Main controller released")
  }
}

/**
 * 主控制器状态
 */
data class MainControllerStatus(
  val currentStatus: Int,
  val isOnlineMode: Boolean,
  val audioManagerRunning: Boolean,
  val recordingStorageManagerStatus: RecordingStorageManagerStatus,
  val transcriptionDispatcherStatus: TranscriptionDispatcherStatus,
  val onlineAsrStatus: OnlineAsrStatus?,
  val offlineOrchestratorStatus: OfflineOrchestratorStatus?,
  val recordingResult: RecordingResult?
)
