package com.aispeech.hybridspeech

import android.app.Application
import android.os.Environment
import android.provider.Settings
import com.aispeech.DUILiteConfig
import com.aispeech.DUILiteSDK
import com.aispeech.aibase.AILog
import com.aispeech.aibase.logger.LogLevel
import com.aispeech.export.config.AuthConfig
import com.aispeech.hybridspeech.core.CoroutineScopeManager
import com.aispeech.lite.AuthType
import com.blankj.utilcode.util.PathUtils
import java.io.File

class HybridSpeechApplication: Application() {

  companion object {
    const val TAG = "HybridSpeechApplication"
    private const val MAX_LOG_SIZE = 3 * 1024 * 1024
  }

  override fun onCreate() {
    super.onCreate()
    initAiLog()
    initializeThirdPartySDKs()
    AILog.d(TAG, "onCreate")
  }

  override fun onTerminate() {
    super.onTerminate()
    CoroutineScopeManager.shutdown()
  }

  private fun initAiLog() {
    var parentPath = PathUtils.getExternalStoragePath()
    if (parentPath.length < 2) {
      parentPath = Environment.getExternalStorageDirectory().absolutePath
    }

    var logLevel = LogLevel.RELEASE
    if (BuildConfig.DEBUG) {
      logLevel = LogLevel.FULL
    }

    AILog.Init(
      this, logLevel, MAX_LOG_SIZE, BuildConfig.DEBUG,
      parentPath + File.separator + "AISpeechLog" + File.separator, "hybridspeech"
    )
    AILog.i(TAG, "onCreate begin")
  }

  private fun initializeThirdPartySDKs() {
    val androidId: String = Settings.Secure.getString(contentResolver, Settings.Secure.ANDROID_ID)

    val authConfig = AuthConfig.Builder()
      .setAuthTimeout(5000)
      .setCustomDeviceName(androidId)
      .setType(AuthType.ONLINE)
      .setAuthServer("https://auth.duiopen.com")
      .create()


    // 产品认证需设置 apiKey, productId, productKey, productSecret
    val config = DUILiteConfig.Builder()
      .setApiKey("b91ae261a9f5b91ae261a9f5684b97db")
      .setProductId("279630735")
      .setProductKey("6d8289e5937ce3c6240d2a9a853bb8ed")
      .setProductSecret("6c196197235e7378c9ce2c1ca1f40adf")
      .setAuthConfig(authConfig)
      .create()

    // SDK预初始化方法
    // init预初始化方法耗时极少，不会影响App首次冷启动用户体验
    DUILiteSDK.init(applicationContext)
    DUILiteSDK.setJavaLiteLogLevel(3)
    DUILiteSDK.setNativeLogLevel(3)


    // SDK 授权
    DUILiteSDK.doAuth(applicationContext, config, object : DUILiteSDK.InitListener {
      override fun success() {
        AILog.d(TAG, "授权成功! ")
      }

      override fun error(errorCode: String, errorInfo: String) {
        AILog.d(TAG, "授权失败, errorcode: $errorCode,errorInfo:$errorInfo")
      }
    })
  }
}