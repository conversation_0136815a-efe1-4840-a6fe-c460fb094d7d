package com.aispeech.hybridspeech

import android.annotation.SuppressLint
import android.app.Notification
import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.Service
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.os.Build
import android.os.IBinder
import android.os.RemoteCallbackList
import androidx.core.app.NotificationCompat
import com.aispeech.aibase.AILog
import com.aispeech.hybridspeech.core.CoroutineScopeManager
import com.aispeech.hybridspeech.session.RecordingSessionImpl
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import java.util.UUID
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.atomic.AtomicInteger
import java.util.concurrent.atomic.AtomicLong

/**
 * 基于会话（Session）模式的混合语音识别服务（简化版，无持久化）。
 * 通过工厂模式创建和管理独立的录音会话。
 * 支持多会话并发管理，每个会话独立运行，互不干扰。
 */
class HybridSpeechSessionService : Service() {

  companion object {
    private const val TAG = "HybridSpeechSessionSvc" // 使用新标签以区分日志
    private const val NOTIFICATION_ID = 1002 // 使用新的通知ID避免冲突
    private const val CHANNEL_ID = "hybrid_speech_session_channel"
    private const val SESSION_TIMEOUT_MS = 30 * 60 * 1000L // 30分钟会话超时
    private const val MAX_CONCURRENT_SESSIONS = 10 // 最大并发会话数
  }

  // 用于管理所有活跃的会话
  private val activeSessions = ConcurrentHashMap<String, RecordingSessionImpl>()

  // 会话状态统计
  private val sessionStats = SessionStatistics()

  // 全局配置提供者，由所有会话共享
  private var configProvider: IHybridSpeechConfigProvider? = null

  // 服务级别的协程作用域管理
  private val serviceScopeDelegate = CoroutineScopeManager.createModuleScopeDelegate(
    moduleName = "HybridSpeechSessionService-${this.hashCode()}",
    parentScope = CoroutineScopeManager.getMainParentScope()
  )
  private val serviceScope: CoroutineScope = serviceScopeDelegate



  // 会话监控和清理任务
  private var sessionMonitorJob: Job? = null
  private var notificationUpdateJob: Job? = null

  /**
   * 这是暴露给客户端的 Binder 对象，实现了会话工厂接口。
   */
  private val factoryBinder = object : IHybridSpeechSessionFactory.Stub() {
    override fun createRecordingSession(config: RecordingConfig, callback: IRecordingSessionCallback) {
      val sessionId = UUID.randomUUID().toString()
      AILog.i(TAG, "Request to create new session with ID: $sessionId")

      // 检查并发会话数限制
      if (activeSessions.size >= MAX_CONCURRENT_SESSIONS) {
        AILog.w(TAG, "Maximum concurrent sessions reached: ${activeSessions.size}")
        try {
          callback.onSessionCreateFailed("Maximum concurrent sessions reached: $MAX_CONCURRENT_SESSIONS")
        } catch (e: Exception) {
          AILog.e(TAG, "Failed to notify client about session limit", e)
        }
        return
      }

      try {
        // 1. 创建会话的内部实现
        val sessionImpl = RecordingSessionImpl(
          context = applicationContext,
          sessionId = sessionId,
          clientCallback = callback,
          onRelease = { id ->
            AILog.i(TAG, "Session $id released, removing from active list.")
            activeSessions.remove(id)
            sessionStats.onSessionEnded()
            updateNotificationAsync()
          },
          onStatusChanged = { id, status ->
            updateNotificationAsync()
          }
        )

        // 2. 将会话实例存入Map进行管理
        activeSessions[sessionId] = sessionImpl
        sessionStats.onSessionCreated()

        // 3. 创建返回给客户端的 Session Binder 代理
        val sessionStub = createSessionStub(sessionId)

        // 4. 启动会话的内部逻辑（例如，开始录音）
        // 将全局的 configProvider 传递给会话
        sessionImpl.start(config, configProvider)

        // 5. 通过回调，将 IRecordingSession 的代理对象返回给客户端
        callback.onSessionCreated(sessionStub)
        AILog.i(TAG, "Session $sessionId created and returned to client. Active sessions: ${activeSessions.size}")

        updateNotificationAsync()

      } catch (e: Exception) {
        AILog.e(TAG, "Failed to create session $sessionId", e)
        activeSessions.remove(sessionId) // 确保清理
        sessionStats.onSessionFailed()
        try {
          callback.onSessionCreateFailed("Failed to create session: ${e.message}")
        } catch (re: Exception) {
          AILog.e(TAG, "Failed to notify client about session creation failure", re)
        }
      }
    }

    override fun registerConfigProvider(provider: IHybridSpeechConfigProvider?) {
      AILog.i(TAG, "Global config provider registered.")
      <EMAIL> = provider
    }

    override fun unregisterConfigProvider() {
      AILog.i(TAG, "Global config provider unregistered.")
      <EMAIL> = null
    }
  }

  /**
   * 为指定的 sessionId 创建一个 IRecordingSession.Stub 实例。
   */
  private fun createSessionStub(sessionId: String): IRecordingSession.Stub {
    return object : IRecordingSession.Stub() {
      // 辅助函数，用于安全地获取会话实例
      private fun getSession(): RecordingSessionImpl? {
        val session = activeSessions[sessionId]
        if (session == null) {
          AILog.w(TAG, "Attempted to operate on a released or non-existent session: $sessionId")
        }
        return session
      }

      override fun pause() { getSession()?.pause() }
      override fun resume() { getSession()?.resume() }
      override fun stop() { getSession()?.stop() }
      override fun getStatus(): Int = getSession()?.getStatus() ?: ServiceStatus.IDLE // 返回默认状态
      override fun getRecordingDuration(): Long = getSession()?.getRecordingDuration() ?: 0L
      override fun release() { getSession()?.release() }
    }
  }

  @SuppressLint("InlinedApi")
  override fun onCreate() {
    super.onCreate()
    AILog.i(TAG, "Service onCreate")

    try {
      createNotificationChannel()



      val filter = IntentFilter(Intent.ACTION_USER_PRESENT)
      registerReceiver(userPresentReceiver, filter)
      AILog.i(TAG, "USER_PRESENT broadcast receiver registered.")

      if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
        startForeground(
          NOTIFICATION_ID,
          createNotification(),
          android.content.pm.ServiceInfo.FOREGROUND_SERVICE_TYPE_MICROPHONE
        )
      } else {
        startForeground(NOTIFICATION_ID, createNotification())
      }

      // 启动会话监控和清理任务
      startSessionMonitoring()

      AILog.i(TAG, "Service started in foreground and monitoring initialized.")
    } catch (e: Exception) {
      AILog.e(TAG, "Service onCreate failed", e)
      throw e
    }
  }

  override fun onBind(intent: Intent?): IBinder {
    AILog.i(TAG, "Service onBind, returning session factory binder.")
    return factoryBinder
  }

  override fun onUnbind(intent: Intent?): Boolean {
    AILog.i(TAG, "Service onUnbind. Active sessions: ${activeSessions.size}")
    return super.onUnbind(intent)
  }

  override fun onDestroy() {
    AILog.i(TAG, "Service onDestroy")

    // 停止监控任务
    sessionMonitorJob?.cancel()
    notificationUpdateJob?.cancel()

    // 清理所有仍然活跃的会话
    AILog.i(TAG, "Releasing all active sessions...")
    val sessionCount = activeSessions.size
    activeSessions.values.forEach { it.release() }
    activeSessions.clear()
    AILog.i(TAG, "Released $sessionCount sessions")

    // 清理协程作用域
    serviceScopeDelegate.shutdown()

    unregisterReceiver(userPresentReceiver)
    AILog.i(TAG, "USER_PRESENT broadcast receiver unregistered.")

    super.onDestroy()
  }

  // --- 通知相关代码 (与你原有的实现基本一致) ---

  private fun createNotificationChannel() {
    val channel = NotificationChannel(
      CHANNEL_ID,
      "Hybrid Speech Session Service",
      NotificationManager.IMPORTANCE_LOW
    ).apply {
      description = "Manages hybrid speech recognition sessions"
      setShowBadge(false)
    }
    getSystemService(NotificationManager::class.java).createNotificationChannel(channel)
  }

  private fun createNotification(): Notification {
    // 在会话模式下，通知可以更通用，或者反映活跃会话的数量
    val sessionCount = activeSessions.size
    val recordingSessions = activeSessions.values.count { it.getStatus() == ServiceStatus.RECORDING }
    val pausedSessions = activeSessions.values.count { it.getStatus() == ServiceStatus.PAUSED }

    val contentText = when {
      sessionCount == 0 -> "待机中"
      recordingSessions > 0 && pausedSessions > 0 ->
        "活跃会话: $sessionCount (录音: $recordingSessions, 暂停: $pausedSessions)"
      recordingSessions > 0 ->
        "活跃会话: $sessionCount (录音中: $recordingSessions)"
      pausedSessions > 0 ->
        "活跃会话: $sessionCount (已暂停: $pausedSessions)"
      else ->
        "活跃会话: $sessionCount"
    }

    val expandedText = buildString {
      append("总会话数: ${sessionStats.totalSessions}")
      append(" | 成功: ${sessionStats.successfulSessions}")
      if (sessionStats.failedSessions > 0) {
        append(" | 失败: ${sessionStats.failedSessions}")
      }
    }

    return NotificationCompat.Builder(this, CHANNEL_ID)
      .setContentTitle("混合语音服务")
      .setContentText(contentText)
      .setStyle(NotificationCompat.BigTextStyle()
        .bigText("$contentText\n$expandedText"))
      .setSmallIcon(android.R.drawable.ic_btn_speak_now)
      .setOngoing(true)
      .setShowWhen(false)
      .build()
  }

  private fun updateNotification() {
    val notification = createNotification()
    getSystemService(NotificationManager::class.java).notify(NOTIFICATION_ID, notification)
  }

  private fun updateNotificationAsync() {
    notificationUpdateJob?.cancel()
    notificationUpdateJob = serviceScope.launch {
      delay(100) // 防抖动，避免频繁更新
      updateNotification()
    }
  }

  // --- 会话监控和管理 ---

  private fun startSessionMonitoring() {
    sessionMonitorJob = serviceScope.launch {
      while (true) {
        delay(60_000) // 每分钟检查一次

        try {
          cleanupExpiredSessions()
          logSessionStatistics()
        } catch (e: Exception) {
          AILog.e(TAG, "Error in session monitoring", e)
        }
      }
    }
  }

  private fun cleanupExpiredSessions() {
    val currentTime = System.currentTimeMillis()
    val expiredSessions = mutableListOf<String>()

    activeSessions.forEach { (sessionId, session) ->
      if (currentTime - session.getCreationTime() > SESSION_TIMEOUT_MS) {
        AILog.w(TAG, "Session $sessionId expired after ${SESSION_TIMEOUT_MS}ms")
        expiredSessions.add(sessionId)
      }
    }

    expiredSessions.forEach { sessionId ->
      activeSessions[sessionId]?.let { session ->
        try {
          session.release()
        } catch (e: Exception) {
          AILog.e(TAG, "Error releasing expired session $sessionId", e)
        }
      }
    }

    if (expiredSessions.isNotEmpty()) {
      AILog.i(TAG, "Cleaned up ${expiredSessions.size} expired sessions")
      updateNotificationAsync()
    }
  }

  private fun logSessionStatistics() {
    if (activeSessions.isNotEmpty()) {
      val statusCounts = activeSessions.values.groupingBy { it.getStatus() }.eachCount()
      AILog.d(TAG, "Session statistics - Active: ${activeSessions.size}, Status counts: $statusCounts")
    }
  }

  // --- 广播接收器 (与你原有的实现一致) ---

  private val userPresentReceiver = object : BroadcastReceiver() {
    override fun onReceive(context: Context?, intent: Intent?) {
      if (intent?.action == Intent.ACTION_USER_PRESENT) {
        AILog.i(TAG, "User unlocked screen. Ensuring service is running.")
        try {
          // 注意：这里要启动正确的 Service 类
          context?.startForegroundService(Intent(context, HybridSpeechSessionService::class.java))
        } catch (e: Exception) {
          AILog.e(TAG, "Failed to start HybridSpeechSessionService on user present", e)
        }
      }
    }
  }
}

/**
 * 会话统计信息类
 */
private class SessionStatistics {
  private val _totalSessions = AtomicInteger(0)
  private val _successfulSessions = AtomicInteger(0)
  private val _failedSessions = AtomicInteger(0)

  val totalSessions: Int get() = _totalSessions.get()
  val successfulSessions: Int get() = _successfulSessions.get()
  val failedSessions: Int get() = _failedSessions.get()

  fun onSessionCreated() {
    _totalSessions.incrementAndGet()
  }

  fun onSessionEnded() {
    _successfulSessions.incrementAndGet()
  }

  fun onSessionFailed() {
    _failedSessions.incrementAndGet()
  }

  fun reset() {
    _totalSessions.set(0)
    _successfulSessions.set(0)
    _failedSessions.set(0)
  }
}
}