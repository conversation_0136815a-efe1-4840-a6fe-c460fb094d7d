package com.aispeech.hybridspeech.asr.online

import com.aispeech.aibase.AILog
import java.util.concurrent.atomic.AtomicReference

/**
 * Centralized State Machine for Online ASR Orchestrator
 * 
 * This immutable data class represents the complete state of the OnlineASR system,
 * replacing distributed volatile fields with a centralized, thread-safe state management approach.
 * 
 * Key design principles:
 * - **Immutability**: All state transitions create new instances
 * - **Atomicity**: Updates use compare-and-set operations to ensure consistency
 * - **Centralization**: Single source of truth for all orchestrator state
 * - **Thread Safety**: StateFlow and AtomicReference ensure safe concurrent access
 * 
 * @property isRunning Whether the ASR system is actively running
 * @property isNetworkAvailable Current network connectivity status
 * @property shouldRetryOnNetworkRestore Flag indicating if reconnection should be attempted when network returns
 * @property isPausedByUser Whether the system is paused by user action (manual pause/resume)
 * @property needsResumeOnReady Flag to trigger resume transmission when connection becomes ready
 * @property readyState Current connection and business readiness state
 * @property audioType Active audio processing type (OGG_OPUS or MP3)
 * @property resumeState Current resume transmission state (NORMAL or RESUMING)
 * @property pauseRealTimeProcessing Whether real-time processing is paused during resume
 * @property isProcessing Whether audio processing is active
 * @property isResuming Whether resume transmission is in progress
 */
data class OrchestratorFlags(
    val isRunning: Boolean = false,
    val isNetworkAvailable: Boolean = true,
    val shouldRetryOnNetworkRestore: Boolean = false,
    val isPausedByUser: Boolean = false,
    val needsResumeOnReady: Boolean = false,
    val readyState: OnlineAsrReadyState = OnlineAsrReadyState.NOT_CONNECTED,
    val audioType: AudioType = AudioType.OGG_OPUS,
    val resumeState: ResumeState = ResumeState.NORMAL,
    val pauseRealTimeProcessing: Boolean = false,
    val isProcessing: Boolean = false,
    val isResuming: Boolean = false
) {
    companion object {
        /**
         * Default initial state
         */
        fun initial() = OrchestratorFlags()
        
        /**
         * State when starting ASR
         */
        fun running() = OrchestratorFlags(
            isRunning = true,
            isNetworkAvailable = true,
            shouldRetryOnNetworkRestore = false,
            isPausedByUser = false,
            needsResumeOnReady = false,
            readyState = OnlineAsrReadyState.NOT_CONNECTED
        )
        
        /**
         * State when ASR is stopped
         */
        fun stopped() = OrchestratorFlags(
            isRunning = false,
            isNetworkAvailable = true,
            shouldRetryOnNetworkRestore = false,
            isPausedByUser = false,
            needsResumeOnReady = false,
            readyState = OnlineAsrReadyState.NOT_CONNECTED
        )
    }
}

/**
 * Thread-safe state holder that performs atomic compare-and-set transitions
 */
class StateHolder(initialState: OrchestratorFlags = OrchestratorFlags.initial()) {
private val stateRef = AtomicReference(initialState)

    /**
     * Transition method using the small DSL
     */
    fun transition(label: String, block: (OrchestratorFlags) -> OrchestratorFlags) {
        val old = stateRef.getAndUpdate { block(it) }
        logStateChange(label, old, stateRef.get())
        validate(stateRef.get())
    }

    /**
     * Log state change with detailed information
     */
    private fun logStateChange(label: String, old: OrchestratorFlags, new: OrchestratorFlags) {
        if (old != new) {
            AILog.d("StateHolder", "[$label] State transition: $old -> $new")
        } else {
            AILog.v("StateHolder", "[$label] No state change (already: $new)")
        }
    }

    /**
     * Validate state invariants
     */
    private fun validate(newState: OrchestratorFlags) {
        // Invariant: needsResumeOnReady can be true only when readyState != READY
        require(!(newState.needsResumeOnReady && newState.readyState == OnlineAsrReadyState.READY)) {
            "Invalid state: needsResumeOnReady cannot be true when readyState is READY. Current state: $newState"
        }
        
        // Invariant: shouldRetryOnNetworkRestore should be false when network is available and connected
        require(!(newState.shouldRetryOnNetworkRestore && newState.isNetworkAvailable && 
                  newState.readyState in listOf(OnlineAsrReadyState.CONNECTED, OnlineAsrReadyState.READY))) {
            "Invalid state: shouldRetryOnNetworkRestore should be false when network is available and connected. Current state: $newState"
        }
        
        // Invariant: cannot be paused when not running
        require(!(newState.isPausedByUser && !newState.isRunning)) {
            "Invalid state: cannot be paused when not running. Current state: $newState"
        }
        
        // Invariant: readyState should be NOT_CONNECTED when not running
        require(!((!newState.isRunning) && newState.readyState != OnlineAsrReadyState.NOT_CONNECTED)) {
            "Invalid state: readyState should be NOT_CONNECTED when not running. Current state: $newState"
        }
    }
    val current: OrchestratorFlags
        get() = stateRef.get()
    
    /**
     * Atomically update state using compare-and-set
     * @param transform function that transforms current state to new state
     * @return true if update succeeded, false if retry needed
     */
    fun updateState(transform: (OrchestratorFlags) -> OrchestratorFlags): Boolean {
        val currentState = stateRef.get()
        val newState = transform(currentState)
        return stateRef.compareAndSet(currentState, newState)
    }
    
    /**
     * Atomically update state with retry until successful
     * @param transform function that transforms current state to new state
     * @return the new state after successful update
     */
    fun updateStateWithRetry(transform: (OrchestratorFlags) -> OrchestratorFlags): OrchestratorFlags {
        while (true) {
            val currentState = stateRef.get()
            val newState = transform(currentState)
            if (stateRef.compareAndSet(currentState, newState)) {
                return newState
            }
            // If CAS failed, retry with fresh current state
        }
    }
    
    /**
     * Conditionally update state only if predicate matches
     * @param predicate condition that must be true for update to proceed
     * @param transform function that transforms current state to new state
     * @return true if update succeeded, false if predicate failed or CAS failed
     */
    fun updateStateIf(
        predicate: (OrchestratorFlags) -> Boolean,
        transform: (OrchestratorFlags) -> OrchestratorFlags
    ): Boolean {
        val currentState = stateRef.get()
        if (!predicate(currentState)) {
            return false
        }
        val newState = transform(currentState)
        return stateRef.compareAndSet(currentState, newState)
    }
    
    /**
     * Reset to initial state
     */
    fun reset() {
        stateRef.set(OrchestratorFlags.initial())
    }
    
    /**
     * Set to specific state
     */
    fun setState(newState: OrchestratorFlags) {
        stateRef.set(newState)
    }
}

/**
 * Extension functions for creating new instances with desired changes
 */

/**
 * Create new instance with network status changed
 */
fun OrchestratorFlags.withNetwork(isAvailable: Boolean): OrchestratorFlags {
    return copy(
        isNetworkAvailable = isAvailable,
        // Clear retry flag when network becomes available
        shouldRetryOnNetworkRestore = if (isAvailable) false else shouldRetryOnNetworkRestore
    )
}

/**
 * Create new instance with retry flag set
 */
fun OrchestratorFlags.withRetryOnNetworkRestore(shouldRetry: Boolean): OrchestratorFlags {
    return copy(shouldRetryOnNetworkRestore = shouldRetry)
}

/**
 * Create new instance with running state changed
 */
fun OrchestratorFlags.withRunning(running: Boolean): OrchestratorFlags {
    return if (running) {
        copy(isRunning = true)
    } else {
        // When stopping, clear most flags
        copy(
            isRunning = false,
            shouldRetryOnNetworkRestore = false,
            isPausedByUser = false,
            needsResumeOnReady = false,
            readyState = OnlineAsrReadyState.NOT_CONNECTED
        )
    }
}

/**
 * Create new instance with ready state changed
 */
fun OrchestratorFlags.withReadyState(state: OnlineAsrReadyState): OrchestratorFlags {
    return copy(
        readyState = state,
        // Clear resume flag when disconnected or in error
        needsResumeOnReady = when (state) {
            OnlineAsrReadyState.NOT_CONNECTED,
            OnlineAsrReadyState.ERROR,
            OnlineAsrReadyState.CLOSED -> false
            else -> needsResumeOnReady
        }
    )
}

/**
 * Toggle pause state
 */
fun OrchestratorFlags.togglePause(): OrchestratorFlags {
    return copy(
        isPausedByUser = !isPausedByUser,
        // Clear resume flag when pausing
        needsResumeOnReady = if (!isPausedByUser) false else needsResumeOnReady
    )
}

/**
 * Create new instance with pause state set
 */
fun OrchestratorFlags.withPause(paused: Boolean): OrchestratorFlags {
    return copy(
        isPausedByUser = paused,
        // Clear resume flag when pausing
        needsResumeOnReady = if (paused) false else needsResumeOnReady
    )
}

/**
 * Create new instance with resume flag set
 */
fun OrchestratorFlags.withResumeOnReady(needsResume: Boolean): OrchestratorFlags {
    return copy(needsResumeOnReady = needsResume)
}

/**
 * Create new instance for connection established
 */
fun OrchestratorFlags.onConnected(): OrchestratorFlags {
    return copy(
        readyState = OnlineAsrReadyState.CONNECTED,
        shouldRetryOnNetworkRestore = false
    )
}

/**
 * Create new instance for connection ready (business ready)
 */
fun OrchestratorFlags.onReady(): OrchestratorFlags {
    return copy(
        readyState = OnlineAsrReadyState.READY,
        shouldRetryOnNetworkRestore = false
    )
}

/**
 * Create new instance for connection error
 */
fun OrchestratorFlags.onConnectionError(): OrchestratorFlags {
    return copy(
        readyState = OnlineAsrReadyState.ERROR,
        needsResumeOnReady = false,
        // Set retry flag only if network is available and not paused
        shouldRetryOnNetworkRestore = if (!isNetworkAvailable && !isPausedByUser) true else shouldRetryOnNetworkRestore
    )
}

/**
 * Create new instance for disconnected state
 */
fun OrchestratorFlags.onDisconnected(): OrchestratorFlags {
    return copy(
        readyState = OnlineAsrReadyState.NOT_CONNECTED,
        needsResumeOnReady = false
    )
}

/**
 * Create new instance when network is restored
 */
fun OrchestratorFlags.onNetworkRestored(): OrchestratorFlags {
    return copy(
        isNetworkAvailable = true,
        shouldRetryOnNetworkRestore = false
    )
}

/**
 * Create new instance when network is lost
 */
fun OrchestratorFlags.onNetworkLost(): OrchestratorFlags {
    return copy(
        isNetworkAvailable = false,
        // Set retry flag if connection was problematic
        shouldRetryOnNetworkRestore = when (readyState) {
            OnlineAsrReadyState.ERROR,
            OnlineAsrReadyState.NOT_CONNECTED -> true
            else -> shouldRetryOnNetworkRestore
        }
    )
}

/**
 * Check if should attempt reconnection
 */
fun OrchestratorFlags.shouldAttemptReconnection(): Boolean {
    return isRunning && 
           isNetworkAvailable && 
           !isPausedByUser && 
           shouldRetryOnNetworkRestore
}

/**
 * Check if should start resume transmission
 */
fun OrchestratorFlags.shouldStartResume(): Boolean {
    return isRunning && 
           !isPausedByUser && 
           readyState == OnlineAsrReadyState.READY &&
           needsResumeOnReady
}

/**
 * Check if can pause
 */
fun OrchestratorFlags.canPause(): Boolean {
    return isRunning && !isPausedByUser
}

/**
 * Check if can resume
 */
fun OrchestratorFlags.canResume(): Boolean {
    return isRunning && isPausedByUser
}

/**
 * Create new instance with audio type changed
 */
fun OrchestratorFlags.withAudioType(newAudioType: AudioType): OrchestratorFlags {
    return copy(audioType = newAudioType)
}
