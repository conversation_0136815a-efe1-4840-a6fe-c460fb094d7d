package com.aispeech.hybridspeech.asr.online

import com.aispeech.aibase.AILog
import com.aispeech.hybridspeech.IHybridSpeechConfigProvider
import com.aispeech.hybridspeech.INetworkConfigCallback
import com.aispeech.hybridspeech.NetworkConfig
import com.aispeech.hybridspeech.NetworkConfigRequest
import com.aispeech.hybridspeech.RecordingConfig
import kotlinx.coroutines.suspendCancellableCoroutine
import kotlin.coroutines.resume

/**
 * 统一网络配置管理器
 * 负责处理所有网络配置获取逻辑，包括初始配置和续传配置
 * 提供协程化的挂起方法，简化异步操作
 */
class NetworkConfigManager {
  companion object {
    private const val TAG = "NetworkConfigManager"
  }

  /**
   * 录音会话信息
   */
  data class SessionInfo(
    val recordId: Long,
    val userId: String,
    val audioType: String
  )

  /**
   * 获取初始网络配置的挂起方法
   * @param provider 网络配置提供者
   * @param config 录音配置
   * @param sessionInfo 录音会话信息
   * @return 网络配置，失败时返回null
   */
  suspend fun fetchNetworkConfig(
    provider: IHybridSpeechConfigProvider,
    config: RecordingConfig,
    sessionInfo: SessionInfo
  ): NetworkConfig? {
    return try {
      AILog.i(TAG, "Requesting initial network config from provider...")

      // 从在线ASR配置中获取参数创建网络配置请求
      val asrConfig = config.onlineAsrConfig
        ?: throw IllegalArgumentException("OnlineAsrConfig is required for online mode")

      val networkConfigRequest = NetworkConfigRequest.createAsrWebSocketRequest(
        recordId = sessionInfo.recordId,
        userId = sessionInfo.userId,
        language = config.language,
        audioType = sessionInfo.audioType,
        translate = config.translate,
        enableRealtimeAgenda = asrConfig.enableRealtimeAgenda
      )

      getNetworkConfigSuspend(provider, networkConfigRequest)
    } catch (e: Exception) {
      AILog.e(TAG, "Error fetching initial network config", e)
      null
    }
  }

  /**
   * 获取续传配置的挂起方法
   * @param provider 网络配置提供者
   * @param sessionInfo 录音会话信息
   * @param currentSessionId 当前会话ID
   * @param audioOffsetMs 音频偏移量
   * @param reason 获取配置的原因（用于日志）
   * @return 续传配置，如果获取失败则返回null
   */
  suspend fun fetchResumeConfig(
    provider: IHybridSpeechConfigProvider,
    sessionInfo: SessionInfo,
    currentSessionId: String?,
    audioOffsetMs: Long,
    reason: String
  ): com.aispeech.hybridspeech.AsrResumeConfig? {
    return try {
      AILog.i(TAG, "Fetching resume config for $reason: sessionId=$currentSessionId, audioOffsetMs=${audioOffsetMs}ms")

      // 创建续传请求
      val resumeRequest = NetworkConfigRequest.createSmartResumeRequest(
        recordId = sessionInfo.recordId,
        userId = sessionInfo.userId,
        sessionId = currentSessionId ?: "",
        resumeFromOffset = audioOffsetMs,
        audioType = sessionInfo.audioType
      )

      val networkConfig = getNetworkConfigSuspend(provider, resumeRequest)
      val resumeConfig = networkConfig?.asrResumeConfig

      if (resumeConfig != null) {
        AILog.i(TAG, "Got resume config for $reason: sessionId=${resumeConfig.sessionId}")
        resumeConfig
      } else {
        AILog.i(TAG, "No resume config returned for $reason")
        null
      }
    } catch (e: Exception) {
      AILog.e(TAG, "Error getting resume config for $reason", e)
      null
    }
  }

  /**
   * 将回调式的网络配置获取转换为协程挂起函数
   * 统一的底层方法，支持初始配置和续传配置获取
   * @param configProvider 配置提供者
   * @param request 网络配置请求
   * @return 网络配置，失败时返回null
   */
  private suspend fun getNetworkConfigSuspend(
    configProvider: IHybridSpeechConfigProvider,
    request: NetworkConfigRequest
  ): NetworkConfig? {
    return suspendCancellableCoroutine { continuation ->
      val callback = object : INetworkConfigCallback.Stub() {
        override fun onConfigReady(config: NetworkConfig) {
          AILog.i(TAG, "Received network config from provider")
          continuation.resume(config)
        }

        override fun onConfigError(errorCode: Int, errorMessage: String) {
          AILog.e(TAG, "Failed to get network config: $errorCode - $errorMessage")
          continuation.resume(null)
        }
      }

      try {
        configProvider.requestNetworkConfig(request, callback)
      } catch (e: Exception) {
        AILog.e(TAG, "Error requesting network config", e)
        continuation.resume(null)
      }
    }
  }

  /**
   * 验证网络配置是否有效
   * @param networkConfig 网络配置
   * @return 是否有效
   */
  fun validateNetworkConfig(networkConfig: NetworkConfig?): Boolean {
    if (networkConfig == null) {
      AILog.e(TAG, "Network config is null")
      return false
    }

    val websocketConfig = networkConfig.websocketConfig
    if (websocketConfig == null) {
      AILog.e(TAG, "WebSocket config is null")
      return false
    }

    if (websocketConfig.signedUrl.isBlank()) {
      AILog.e(TAG, "WebSocket signed URL is blank")
      return false
    }

    AILog.i(TAG, "Network config validation passed")
    return true
  }

  /**
   * 提取WebSocket URL
   * @param networkConfig 网络配置
   * @return WebSocket URL，失败时返回null
   */
  fun extractWebSocketUrl(networkConfig: NetworkConfig): String? {
    return networkConfig.websocketConfig?.signedUrl
  }

  /**
   * 检查是否有续传配置
   * @param networkConfig 网络配置
   * @return 是否有续传配置
   */
  fun hasResumeConfig(networkConfig: NetworkConfig): Boolean {
    return networkConfig.asrResumeConfig != null
  }

  /**
   * 记录网络配置信息（用于调试）
   * @param networkConfig 网络配置
   */
  fun logNetworkConfigInfo(networkConfig: NetworkConfig) {
    val websocketConfig = networkConfig.websocketConfig
    if (websocketConfig != null) {
      AILog.i(TAG, "WebSocket URL configured: ${websocketConfig.signedUrl.take(100)}...")
    }

    val asrResumeConfig = networkConfig.asrResumeConfig
    if (asrResumeConfig != null) {
      AILog.i(TAG, "ASR resume config available: resumeFromOffset=${asrResumeConfig.resumeFromOffset}")
    } else {
      AILog.w(TAG, "No ASR resume config found in network config")
    }
  }
}
