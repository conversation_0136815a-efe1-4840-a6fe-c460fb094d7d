package com.aispeech.hybridspeech.asr.online

/**
 * Sealed Interface State Machine for OnlineASR System
 * 
 * This sealed interface represents a comprehensive state machine for the OnlineASR system,
 * providing type-safe state management with explicit state transitions and compile-time guarantees.
 * 
 * ## Architecture Overview
 * 
 * This state machine complements the centralized `OrchestratorFlags` approach by providing:
 * - **Type Safety**: Each state is a distinct type, preventing invalid state combinations
 * - **Exhaustive Matching**: Compiler ensures all states are handled in when expressions
 * - **Immutable Transitions**: All state changes return new state instances
 * - **Network Awareness**: All states track network availability and retry behavior
 * 
 * ## State Hierarchy
 * 
 * ```
 * OnlineAsrState
 * ├── Stopped           // ASR is not running - initial state
 * ├── Starting          // ASR is starting up but not yet connected
 * └── Running States:
 *     ├── RunningDisconnected    // Running but not connected to server
 *     ├── RunningConnecting      // Running and attempting to connect
 *     ├── RunningConnected       // Running and connected (socket level)
 *     ├── RunningReady           // Running and ready for business operations
 *     ├── RunningError           // Running but connection is in error state
 *     ├── RunningPaused          // Running but paused by user
 *     └── RunningClosed          // Running but connection is closed
 * ```
 * 
 * ## State Transition Rules
 * 
 * 1. **Network State Management**: All states maintain `isNetworkAvailable` and `shouldRetryOnNetworkRestore`
 * 2. **Resume Handling**: Running states support `needsResumeOnReady` for continuation logic
 * 3. **User Control**: Pause/resume operations respect user intent over automatic behavior
 * 4. **Error Recovery**: Error states automatically set retry flags based on network availability
 * 
 * ## Usage with Centralized State
 * 
 * This sealed interface works alongside `OrchestratorFlags` to provide:
 * - **Granular Control**: Fine-grained state transitions with business logic
 * - **Event Handling**: Type-safe event processing in state machines
 * - **Testing**: Easy unit testing of specific state behaviors
 * - **Documentation**: Self-documenting state machine with clear transitions
 * 
 * @see OrchestratorFlags for centralized flag-based state management
 * @see OnlineAsrOrchestrator for the main coordinator using both approaches
 */
sealed interface OnlineAsrState {
    val isNetworkAvailable: Boolean
    val shouldRetryOnNetworkRestore: Boolean
    
    /**
     * ASR is not running - initial state
     */
    data class Stopped(
        override val isNetworkAvailable: Boolean = true,
        override val shouldRetryOnNetworkRestore: Boolean = false
    ) : OnlineAsrState
    
    /**
     * ASR is starting up but not yet connected
     */
    data class Starting(
        override val isNetworkAvailable: Boolean = true,
        override val shouldRetryOnNetworkRestore: Boolean = false
    ) : OnlineAsrState
    
    /**
     * ASR is running and actively processing, not connected to server
     */
    data class RunningDisconnected(
        override val isNetworkAvailable: Boolean = true,
        override val shouldRetryOnNetworkRestore: Boolean = false,
        val needsResumeOnReady: Boolean = false
    ) : OnlineAsrState
    
    /**
     * ASR is running and attempting to connect to server
     */
    data class RunningConnecting(
        override val isNetworkAvailable: Boolean = true,
        override val shouldRetryOnNetworkRestore: Boolean = false,
        val needsResumeOnReady: Boolean = false
    ) : OnlineAsrState
    
    /**
     * ASR is running and connected to server (socket connected)
     */
    data class RunningConnected(
        override val isNetworkAvailable: Boolean = true,
        override val shouldRetryOnNetworkRestore: Boolean = false,
        val needsResumeOnReady: Boolean = false
    ) : OnlineAsrState
    
    /**
     * ASR is running and ready to process (business ready)
     */
    data class RunningReady(
        override val isNetworkAvailable: Boolean = true,
        override val shouldRetryOnNetworkRestore: Boolean = false,
        val needsResumeOnReady: Boolean = false
    ) : OnlineAsrState
    
    /**
     * ASR is running but connection is in error state
     */
    data class RunningError(
        override val isNetworkAvailable: Boolean = true,
        override val shouldRetryOnNetworkRestore: Boolean = false
    ) : OnlineAsrState
    
    /**
     * ASR is running but paused by user
     */
    data class RunningPaused(
        override val isNetworkAvailable: Boolean = true,
        override val shouldRetryOnNetworkRestore: Boolean = false
    ) : OnlineAsrState
    
    /**
     * ASR connection is closed
     */
    data class RunningClosed(
        override val isNetworkAvailable: Boolean = true,
        override val shouldRetryOnNetworkRestore: Boolean = false
    ) : OnlineAsrState
}

/**
 * Extension functions for state queries
 */

fun OnlineAsrState.isRunning(): Boolean = when (this) {
    is OnlineAsrState.Stopped,
    is OnlineAsrState.Starting -> false
    else -> true
}

fun OnlineAsrState.isPausedByUser(): Boolean = this is OnlineAsrState.RunningPaused

fun OnlineAsrState.needsResumeOnReady(): Boolean = when (this) {
    is OnlineAsrState.RunningDisconnected -> needsResumeOnReady
    is OnlineAsrState.RunningConnecting -> needsResumeOnReady
    is OnlineAsrState.RunningConnected -> needsResumeOnReady
    is OnlineAsrState.RunningReady -> needsResumeOnReady
    else -> false
}

fun OnlineAsrState.getReadyState(): OnlineAsrReadyState = when (this) {
    is OnlineAsrState.Stopped,
    is OnlineAsrState.Starting,
    is OnlineAsrState.RunningDisconnected,
    is OnlineAsrState.RunningClosed -> OnlineAsrReadyState.NOT_CONNECTED
    is OnlineAsrState.RunningConnecting -> OnlineAsrReadyState.CONNECTING
    is OnlineAsrState.RunningConnected -> OnlineAsrReadyState.CONNECTED
    is OnlineAsrState.RunningReady -> OnlineAsrReadyState.READY
    is OnlineAsrState.RunningError -> OnlineAsrReadyState.ERROR
    is OnlineAsrState.RunningPaused -> OnlineAsrReadyState.NOT_CONNECTED
}

/**
 * State transition functions that return new states
 */

fun OnlineAsrState.withNetworkStatus(isAvailable: Boolean): OnlineAsrState = when (this) {
    is OnlineAsrState.Stopped -> copy(
        isNetworkAvailable = isAvailable,
        shouldRetryOnNetworkRestore = if (isAvailable) false else shouldRetryOnNetworkRestore
    )
    is OnlineAsrState.Starting -> copy(
        isNetworkAvailable = isAvailable,
        shouldRetryOnNetworkRestore = if (isAvailable) false else shouldRetryOnNetworkRestore
    )
    is OnlineAsrState.RunningDisconnected -> copy(
        isNetworkAvailable = isAvailable,
        shouldRetryOnNetworkRestore = if (isAvailable) false else shouldRetryOnNetworkRestore
    )
    is OnlineAsrState.RunningConnecting -> copy(
        isNetworkAvailable = isAvailable,
        shouldRetryOnNetworkRestore = if (isAvailable) false else shouldRetryOnNetworkRestore
    )
    is OnlineAsrState.RunningConnected -> copy(
        isNetworkAvailable = isAvailable,
        shouldRetryOnNetworkRestore = if (isAvailable) false else shouldRetryOnNetworkRestore
    )
    is OnlineAsrState.RunningReady -> copy(
        isNetworkAvailable = isAvailable,
        shouldRetryOnNetworkRestore = if (isAvailable) false else shouldRetryOnNetworkRestore
    )
    is OnlineAsrState.RunningError -> copy(
        isNetworkAvailable = isAvailable,
        shouldRetryOnNetworkRestore = if (isAvailable) false else (!isAvailable)
    )
    is OnlineAsrState.RunningPaused -> copy(
        isNetworkAvailable = isAvailable,
        shouldRetryOnNetworkRestore = if (isAvailable) false else shouldRetryOnNetworkRestore
    )
    is OnlineAsrState.RunningClosed -> copy(
        isNetworkAvailable = isAvailable,
        shouldRetryOnNetworkRestore = if (isAvailable) false else shouldRetryOnNetworkRestore
    )
}

fun OnlineAsrState.onStart(): OnlineAsrState = when (this) {
    is OnlineAsrState.Stopped -> OnlineAsrState.Starting(isNetworkAvailable, shouldRetryOnNetworkRestore)
    else -> this // Already running
}

fun OnlineAsrState.onStarted(): OnlineAsrState = when (this) {
    is OnlineAsrState.Starting -> OnlineAsrState.RunningDisconnected(isNetworkAvailable, shouldRetryOnNetworkRestore)
    else -> this
}

fun OnlineAsrState.onConnecting(): OnlineAsrState = when (this) {
    is OnlineAsrState.RunningDisconnected -> OnlineAsrState.RunningConnecting(isNetworkAvailable, shouldRetryOnNetworkRestore, needsResumeOnReady)
    is OnlineAsrState.RunningError -> OnlineAsrState.RunningConnecting(isNetworkAvailable, false)
    else -> this
}

fun OnlineAsrState.onConnected(): OnlineAsrState = when (this) {
    is OnlineAsrState.RunningConnecting -> OnlineAsrState.RunningConnected(isNetworkAvailable, false, needsResumeOnReady)
    else -> this
}

fun OnlineAsrState.onReady(): OnlineAsrState = when (this) {
    is OnlineAsrState.RunningConnected -> OnlineAsrState.RunningReady(isNetworkAvailable, false, needsResumeOnReady)
    else -> this
}

fun OnlineAsrState.onError(): OnlineAsrState = when (this) {
    is OnlineAsrState.RunningDisconnected,
    is OnlineAsrState.RunningConnecting,
    is OnlineAsrState.RunningConnected,
    is OnlineAsrState.RunningReady -> OnlineAsrState.RunningError(
        isNetworkAvailable = isNetworkAvailable,
        shouldRetryOnNetworkRestore = if (!isNetworkAvailable) true else shouldRetryOnNetworkRestore
    )
    else -> this
}

fun OnlineAsrState.onDisconnected(): OnlineAsrState = when (this) {
    is OnlineAsrState.RunningConnected,
    is OnlineAsrState.RunningReady,
    is OnlineAsrState.RunningError -> OnlineAsrState.RunningDisconnected(isNetworkAvailable, shouldRetryOnNetworkRestore)
    else -> this
}

fun OnlineAsrState.onClosed(): OnlineAsrState = when (this) {
    is OnlineAsrState.RunningConnected,
    is OnlineAsrState.RunningReady,
    is OnlineAsrState.RunningError,
    is OnlineAsrState.RunningDisconnected -> OnlineAsrState.RunningClosed(isNetworkAvailable, shouldRetryOnNetworkRestore)
    else -> this
}

fun OnlineAsrState.onPause(): OnlineAsrState = when (this) {
    is OnlineAsrState.RunningDisconnected,
    is OnlineAsrState.RunningConnecting,
    is OnlineAsrState.RunningConnected,
    is OnlineAsrState.RunningReady,
    is OnlineAsrState.RunningError,
    is OnlineAsrState.RunningClosed -> OnlineAsrState.RunningPaused(isNetworkAvailable, shouldRetryOnNetworkRestore)
    else -> this
}

fun OnlineAsrState.onResume(): OnlineAsrState = when (this) {
    is OnlineAsrState.RunningPaused -> OnlineAsrState.RunningDisconnected(isNetworkAvailable, shouldRetryOnNetworkRestore)
    else -> this
}

fun OnlineAsrState.onStop(): OnlineAsrState = OnlineAsrState.Stopped()

fun OnlineAsrState.withResumeOnReady(needsResume: Boolean): OnlineAsrState = when (this) {
    is OnlineAsrState.RunningDisconnected -> copy(needsResumeOnReady = needsResume)
    is OnlineAsrState.RunningConnecting -> copy(needsResumeOnReady = needsResume)
    is OnlineAsrState.RunningConnected -> copy(needsResumeOnReady = needsResume)
    is OnlineAsrState.RunningReady -> copy(needsResumeOnReady = needsResume)
    else -> this
}

fun OnlineAsrState.withRetryOnNetworkRestore(shouldRetry: Boolean): OnlineAsrState = when (this) {
    is OnlineAsrState.Stopped -> copy(shouldRetryOnNetworkRestore = shouldRetry)
    is OnlineAsrState.Starting -> copy(shouldRetryOnNetworkRestore = shouldRetry)
    is OnlineAsrState.RunningDisconnected -> copy(shouldRetryOnNetworkRestore = shouldRetry)
    is OnlineAsrState.RunningConnecting -> copy(shouldRetryOnNetworkRestore = shouldRetry)
    is OnlineAsrState.RunningConnected -> copy(shouldRetryOnNetworkRestore = shouldRetry)
    is OnlineAsrState.RunningReady -> copy(shouldRetryOnNetworkRestore = shouldRetry)
    is OnlineAsrState.RunningError -> copy(shouldRetryOnNetworkRestore = shouldRetry)
    is OnlineAsrState.RunningPaused -> copy(shouldRetryOnNetworkRestore = shouldRetry)
    is OnlineAsrState.RunningClosed -> copy(shouldRetryOnNetworkRestore = shouldRetry)
}

/**
 * Conditional state checks
 */

fun OnlineAsrState.shouldAttemptReconnection(): Boolean {
    return isRunning() && 
           isNetworkAvailable && 
           !isPausedByUser() && 
           shouldRetryOnNetworkRestore
}

fun OnlineAsrState.shouldStartResume(): Boolean {
    return this is OnlineAsrState.RunningReady && needsResumeOnReady
}

fun OnlineAsrState.canPause(): Boolean {
    return isRunning() && !isPausedByUser()
}

fun OnlineAsrState.canResume(): Boolean {
    return isPausedByUser()
}

fun OnlineAsrState.canConnect(): Boolean = when (this) {
    is OnlineAsrState.RunningDisconnected,
    is OnlineAsrState.RunningError -> isNetworkAvailable
    else -> false
}
