package com.aispeech.hybridspeech.asr.online

/**
 * Example usage of the StateHolder transition DSL
 * This demonstrates how to use the new transition method safely
 */
class StateTransitionExample {
    
    private val stateHolder = StateHolder()
    
    /**
     * Example: Start ASR with transition DSL
     */
    fun startAsr() {
        stateHolder.transition("Start ASR") { currentState ->
            currentState.withRunning(true)
        }
    }
    
    /**
     * Example: Connect to server
     */
    fun connectToServer() {
        stateHolder.transition("Connect to server") { currentState ->
            currentState.withReadyState(OnlineAsrReadyState.CONNECTING)
        }
    }
    
    /**
     * Example: Server connection established
     */
    fun onServerConnected() {
        stateHolder.transition("Server connected") { currentState ->
            currentState.onConnected()
        }
    }
    
    /**
     * Example: Server ready for business
     */
    fun onServerReady() {
        stateHolder.transition("Server ready") { currentState ->
            currentState.onReady()
        }
    }
    
    /**
     * Example: Handle network loss
     */
    fun onNetworkLost() {
        stateHolder.transition("Network lost") { currentState ->
            currentState.onNetworkLost()
        }
    }
    
    /**
     * Example: Set resume flag while connecting
     */
    fun setResumeOnConnecting() {
        stateHolder.transition("Set resume on connecting") { currentState ->
            currentState
                .withReadyState(OnlineAsrReadyState.CONNECTING)
                .withResumeOnReady(true)
        }
    }
    
    /**
     * Example: Pause ASR
     */
    fun pauseAsr() {
        stateHolder.transition("Pause ASR") { currentState ->
            currentState.withPause(true)
        }
    }
    
    /**
     * Example: Resume ASR
     */
    fun resumeAsr() {
        stateHolder.transition("Resume ASR") { currentState ->
            currentState.withPause(false)
        }
    }
    
    /**
     * Example: Stop ASR - resets to initial state
     */
    fun stopAsr() {
        stateHolder.transition("Stop ASR") { currentState ->
            currentState.withRunning(false)
        }
    }
    
    /**
     * Example: This would trigger a validation error
     * Uncommenting this will cause a require() failure
     */
    /*
    fun triggerValidationError() {
        stateHolder.transition("Invalid state - WILL FAIL") { currentState ->
            currentState
                .withReadyState(OnlineAsrReadyState.READY)
                .withResumeOnReady(true) // This violates the invariant!
        }
    }
    */
    
    /**
     * Get current state
     */
    fun getCurrentState(): OrchestratorFlags {
        return stateHolder.current
    }
}
