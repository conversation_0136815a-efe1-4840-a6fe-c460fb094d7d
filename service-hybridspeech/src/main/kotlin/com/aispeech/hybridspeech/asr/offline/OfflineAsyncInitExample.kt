package com.aispeech.hybridspeech.asr.offline

import android.content.Context
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.launch
import kotlinx.coroutines.delay

/**
 * 演示如何使用异步初始化的离线ASR引擎
 * 
 * 主要特性：
 * 1. 引擎初始化不阻塞启动流程
 * 2. 初始化期间的PCM数据会被缓存
 * 3. 初始化完成后自动处理缓存的数据
 */
class OfflineAsyncInitExample(private val context: Context) {
    
    private val orchestrator = OfflineOrchestrator(context)
    private val pcmDataFlow = MutableSharedFlow<ByteArray>()
    private val scope = CoroutineScope(Dispatchers.Main)
    
    /**
     * 启动离线ASR处理
     * 这个方法会立即返回，不会等待引擎初始化完成
     */
    fun startOfflineAsr(modelPath: String) {
        scope.launch {
            // 1. 设置配置并启动异步初始化
            val configSuccess = orchestrator.setConfig(modelPath)
            if (!configSuccess) {
                println("Failed to start engine initialization")
                return@launch
            }
            
            // 2. 立即启动处理流程，即使引擎还在初始化
            val startSuccess = orchestrator.start(pcmDataFlow)
            if (!startSuccess) {
                println("Failed to start orchestrator")
                return@launch
            }
            
            println("Offline ASR started successfully")
            println("Engine initialization is running in background")
            println("PCM data will be cached until initialization completes")
            
            // 3. 开始模拟音频数据流
            simulateAudioStream()
        }
    }
    
    /**
     * 模拟实时音频数据流
     */
    private suspend fun simulateAudioStream() {
        // 模拟在引擎初始化期间就开始接收音频数据
        repeat(20) { index ->
            val pcmData = generateMockPcmData(index)
            
            // 发送PCM数据
            pcmDataFlow.emit(pcmData)
            
            if (index < 10) {
                println("Sending PCM data #$index (during initialization - will be cached)")
            } else {
                println("Sending PCM data #$index (after initialization - direct processing)")
            }
            
            // 模拟20ms的音频帧间隔
            delay(20)
        }
    }
    
    /**
     * 生成模拟的PCM数据
     */
    private fun generateMockPcmData(index: Int): ByteArray {
        // 生成640字节的PCM数据（16kHz, 16bit, 20ms）
        return ByteArray(640) { (index * 10 + it).toByte() }
    }
    
    /**
     * 监听转写结果
     */
    fun observeTranscriptionResults() {
        scope.launch {
            orchestrator.transcriptionResultFlow.collect { result ->
                println("Transcription result: $result")
            }
        }
    }
    
    /**
     * 停止离线ASR处理
     */
    fun stopOfflineAsr() {
        orchestrator.stop()
        println("Offline ASR stopped")
    }
    
    /**
     * 释放资源
     */
    fun release() {
        orchestrator.release()
        println("Offline ASR resources released")
    }
    
    /**
     * 获取引擎状态信息
     */
    fun getEngineStatus(): String {
        return if (orchestrator.canStart()) {
            "Engine is ready and can start processing"
        } else {
            "Engine is initializing or not ready"
        }
    }
}

/**
 * 使用示例
 */
fun demonstrateAsyncInit(context: Context) {
    val example = OfflineAsyncInitExample(context)
    
    // 开始监听转写结果
    example.observeTranscriptionResults()
    
    // 启动离线ASR（异步初始化）
    example.startOfflineAsr("/path/to/offline/model")
    
    // 这里可以立即执行其他操作，不需要等待初始化完成
    println("Other operations can continue immediately...")
    
    // 在适当的时候停止和释放资源
    // example.stopOfflineAsr()
    // example.release()
}
