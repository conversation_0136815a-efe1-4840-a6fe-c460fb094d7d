package com.aispeech.hybridspeech.asr.online

import com.aispeech.aibase.AILog
import com.aispeech.hybridspeech.AsrResumeConfig
import com.aispeech.hybridspeech.IHybridSpeechConfigProvider
import com.aispeech.hybridspeech.Mp3EncodingConfig
import com.aispeech.hybridspeech.NetworkConfig
import com.aispeech.hybridspeech.OpusEncodingConfig
import com.aispeech.hybridspeech.RecordingConfig
import com.aispeech.hybridspeech.TranscriptionResult
import com.aispeech.hybridspeech.UnifiedJson
import com.aispeech.hybridspeech.core.CoroutineScopeManager
import com.aispeech.hybridspeech.storage.RecordingStorageManager
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Job
import kotlinx.coroutines.cancelAndJoin
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharedFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.shareIn
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.launch
import kotlinx.coroutines.withTimeoutOrNull

/**
 * 续传状态枚举
 */
enum class ResumeState {
  NORMAL,        // 正常状态，处理实时PCM流
  RESUMING       // 续传状态，从文件读取PCM数据
}

/**
 * 在线的准备状态
 */
enum class OnlineAsrReadyState {
  NOT_CONNECTED,     // 未连接
  CONNECTING,        // 正在连接（socket）
  CONNECTED,         // socket已连接，但业务未ready
  READY,             // 收到 StartResult，可以发送数据
  ERROR,             // 错误状态
  CLOSED             // 已关闭
}


/**
 * Online ASR Orchestrator - Centralized State Machine Coordinator
 * 
 * This class serves as the main coordinator for the OnlineASR system, implementing a centralized
 * state management approach that replaced the previous distributed volatile field pattern.
 * 
 * ## Architecture Overview
 * 
 * The orchestrator coordinates multiple components:
 * - **Audio Processing Strategies**: Handle different audio formats (Opus/MP3) with unified interface
 * - **Network State Monitoring**: Track connectivity and handle reconnection logic
 * - **Resume Session Management**: Handle ASR continuation across network interruptions
 * - **Socket Client Management**: Coordinate WebSocket connections with the ASR service
 * 
 * ## State Management Design
 * 
 * ### Centralized State Container
 * The orchestrator uses `OrchestratorFlags` as a centralized, immutable state container that
 * replaced numerous scattered `@Volatile` fields throughout the codebase.
 * 
 * **Before (Volatile Fields)**:
 * ```kotlin
 * @Volatile private var isProcessing = false
 * @Volatile private var isResuming = false  
 * @Volatile private var pauseRealTimeProcessing = false
 * @Volatile private var currentReadyState = OnlineAsrReadyState.NOT_CONNECTED
 * // ... many more scattered volatile fields
 * ```
 * 
 * **After (Centralized State)**:
 * ```kotlin
 * private val _state = MutableStateFlow(OrchestratorFlags.initial())
 * val stateFlow: StateFlow<OrchestratorFlags> = _state.asStateFlow()
 * ```
 * 
 * ### Thread-Safe State Updates
 * All state changes go through atomic compare-and-set operations:
 * ```kotlin
 * private fun updateState(transform: (OrchestratorFlags) -> OrchestratorFlags) {
 *     // CAS loop ensures atomicity and consistency
 * }
 * ```
 * 
 * ### Benefits of the New Design
 * 
 * 1. **Atomicity**: Multiple related flags are updated together atomically
 * 2. **Consistency**: Impossible to have contradictory state combinations
 * 3. **Observability**: Single StateFlow for monitoring all state changes
 * 4. **Testability**: Easy to test state transitions with immutable snapshots
 * 5. **Debugging**: Complete state history available through logging
 * 
 * ## State Flow Architecture
 * 
 * ```
 * ┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
 * │ Network Monitor │───▶│  Orchestrator    │───▶│ Audio Strategy  │
 * └─────────────────┘    │  (State Machine) │    └─────────────────┘
 *                        └──────────────────┘
 *                                 │
 *                                 ▼
 *                        ┌─────────────────┐
 *                        │ Socket Client   │
 *                        └─────────────────┘
 * ```
 * 
 * ## Key State Transitions
 * 
 * - **Network Events**: `handleNetworkStateChange()` updates connectivity flags
 * - **Connection Events**: `handleConnectionStatusChange()` manages socket state
 * - **Resume Logic**: `startResumeTransmission()` coordinates continuation behavior
 * - **User Actions**: `pauseOnlineAsr()`/`resumeOnlineAsr()` handle manual control
 * 
 * @see OrchestratorFlags for the centralized state container definition
 * @see OnlineAsrState for the sealed interface state machine alternative
 * @see AudioProcessingStrategy for the strategy pattern audio processing
 */
class OnlineAsrOrchestrator: CoroutineScope {
  companion object {
    private const val TAG = "OnlineAsrOrchestrator"

    // 延迟时间常量
    private const val STATE_SWITCH_DELAY_MS = 200L    // 内部状态切换延迟
    private const val RECONNECT_DELAY_MS = 1000L      // 网络重连延迟
  }

  // 协程作用域委托 - 使用结构化并发
  private val scopeDelegate = CoroutineScopeManager.createModuleScopeDelegate(
    moduleName = TAG,
    parentScope = CoroutineScopeManager.getIOParentScope()
  )

  // 委托CoroutineScope接口到scopeDelegate
  override val coroutineContext = scopeDelegate.coroutineContext

  // Thread-safe state container
  private val _state = MutableStateFlow(OrchestratorFlags.initial())
  val stateFlow: StateFlow<OrchestratorFlags> = _state.asStateFlow()

  /**
   * Internal method to atomically update state with CAS loop until successful
   * Logs every state change for debugging purposes
   */
  private fun updateState(transform: (OrchestratorFlags) -> OrchestratorFlags) {
    while (true) {
      val oldState = _state.value
      val newState = transform(oldState)
      
      // If no change needed, break early
      if (oldState == newState) {
        break
      }
      
      // Try to update the state atomically
      if (_state.compareAndSet(oldState, newState)) {
        // Log the state change
        AILog.d(TAG, "State changed: $oldState -> $newState")
        break
      }
      // If CAS failed, retry with fresh state
    }
  }

  // 音频处理策略
  private var audioProcessingStrategy: AudioProcessingStrategy? = null
  private val asrSocketClient = AsrSocketClientFactory.create(AsrSocketClientFactory.ClientType.OKHTTP)

  // 网络状态监测器
  private var networkStateMonitor: NetworkStateMonitor? = null

  // Audio type is now managed in state

  private val _transcriptionResultFlow = MutableSharedFlow<TranscriptionResult>(
    replay = 0,
    extraBufferCapacity = 100
  )
  val transcriptionResultFlow: SharedFlow<TranscriptionResult> = _transcriptionResultFlow.asSharedFlow()

  private var isRunning = false
  private var resultProcessingJob: Job? = null

  // 续传会话管理
  private val resumeSessionManager = ResumeSessionManager(
    updateState = { transform -> updateState(transform) },
    getState = { stateFlow.value }
  )

  // 网络配置管理相关
  private val networkConfigManager = NetworkConfigManager()

  // Network state and other flags are now managed in state

  // Derived flow for ready state changes - automatically tracks state transitions
  val readyStateFlow: SharedFlow<OnlineAsrReadyState> = stateFlow
    .map { it.readyState }
    .distinctUntilChanged()
    .shareIn(scope = this, started = SharingStarted.Eagerly, replay = 1)



  /**
   * 设置在线ASR配置（包含音频类型）
   */
  fun setConfig(serverUrl: String, audioType: String) {
    updateState { it.withAudioType(AudioType.fromString(audioType)) }
    asrSocketClient.setConfig(serverUrl)

    // 使用工厂模式创建相应的处理策略
    audioProcessingStrategy?.shutdown("Strategy change")
    audioProcessingStrategy = AudioProcessingStrategyFactory.createStrategy(stateFlow.value.audioType)

    AILog.i(TAG, "Online ASR config updated - serverUrl configured, audioType=$audioType, strategy=${audioProcessingStrategy?.javaClass?.simpleName}")
  }

  /**
   * 应用续传配置
   * 包括设置配置和更新WebSocket URL
   */
  private fun applyResumeConfig(config: AsrResumeConfig?) {
    // 设置续传配置
    setAsrResumeConfig(config)

    // 如果有新的WebSocket URL，更新配置
    if (config != null && config.resumeWebSocketUrl.isNotEmpty()) {
      asrSocketClient.setConfig(config.resumeWebSocketUrl)
      AILog.i(TAG, "Updated WebSocket URL for resume: ${config.resumeWebSocketUrl.take(50)}...")
    }
  }


  /**
   * 设置网络配置提供者和录音会话信息
   * @param provider 网络配置提供者
   * @param recordId 录音ID
   * @param userId 用户ID
   * @param audioType 音频类型
   */
  fun setConfigProvider(
    provider: IHybridSpeechConfigProvider?,
    recordId: Long? = null,
    userId: String? = null,
    audioType: String? = null
  ) {
    resumeSessionManager.setConfigProvider(provider, recordId, userId, audioType)
  }

  /**
   * 设置ASR续传配置
   */
  fun setAsrResumeConfig(config: AsrResumeConfig?) {
    resumeSessionManager.setAsrResumeConfig(config)
  }

  /**
   * 设置存储管理器（用于续传时读取PCM文件）
   */
  fun setStorageManager(manager: RecordingStorageManager?) {
    resumeSessionManager.setStorageManager(manager)
  }

  /**
   * 设置网络状态监测器
   */
  fun setNetworkStateMonitor(monitor: NetworkStateMonitor?) {
    // 停止旧的监测器
    networkStateMonitor?.stop()

    this.networkStateMonitor = monitor

    if (monitor != null) {
      AILog.i(TAG, "Network state monitor set")
      // 启动网络监测
      monitor.start()

      // 监听网络状态变化
      launch {
        monitor.networkAvailableFlow.collect { isAvailable ->
          handleNetworkStateChange(isAvailable)
        }
      }

      // 初始化当前网络状态
      updateState { it.withNetwork(monitor.isNetworkAvailable()) }
    } else {
      AILog.i(TAG, "Network state monitor cleared")
      updateState { it.withNetwork(true) } // 默认认为网络可用
    }
  }

  /**
   * 获取网络配置的挂起方法
   * @param config 录音配置
   * @return 网络配置，失败时返回null
   */
  private suspend fun fetchNetworkConfig(config: RecordingConfig): NetworkConfig? {
    val provider = resumeSessionManager.getConfigProvider() ?: return null

    // 从在线ASR配置中获取参数
    val asrConfig = config.onlineAsrConfig ?: return null

    val sessionInfo = NetworkConfigManager.SessionInfo(
      recordId = resumeSessionManager.getCurrentRecordId() ?: asrConfig.recordId,
      userId = resumeSessionManager.getCurrentUserId() ?: asrConfig.userId,
      audioType = resumeSessionManager.getCurrentAudioType()
    )

    return networkConfigManager.fetchNetworkConfig(provider, config, sessionInfo)
  }

  /**
   * 开始在线ASR处理（支持配置和续传）- 内部方法
   */
  private fun startWithAsrResumeConfig(
    pcmDataFlow: SharedFlow<ByteArray>,
    opusConfig: OpusEncodingConfig,
    resumeConfig: AsrResumeConfig?
  ): Boolean {
    try {
      if (resumeConfig != null) {
        AILog.i(TAG, "Starting with resume: sessionId=${resumeConfig.sessionId}, offset=${resumeConfig.resumeFromOffset}ms")
      } else {
        AILog.i(TAG, "Starting fresh session")
      }

      // 记录录音开始时间
      resumeSessionManager.setRecordingStartTime(System.currentTimeMillis())
      // 设置续传配置
      setAsrResumeConfig(resumeConfig)

      // 确保有Opus处理策略
      if (stateFlow.value.audioType != AudioType.OGG_OPUS) {
        audioProcessingStrategy?.shutdown("Strategy change to Opus")
        updateState { it.withAudioType(AudioType.OGG_OPUS) }
        audioProcessingStrategy = AudioProcessingStrategyFactory.createStrategy(stateFlow.value.audioType)
      }

      // 启动音频处理策略
      val config = AudioProcessingConfig.OpusConfig(opusConfig)
      if (!audioProcessingStrategy!!.startProcessing(pcmDataFlow, config, asrSocketClient, readyStateFlow,this)) {
        AILog.e(TAG, "Failed to start Opus processing strategy")
        return false
      }

      AILog.i(TAG, "Online ASR orchestrator started successfully with Opus strategy")
      return true

    } catch (e: Exception) {
      AILog.e(TAG, "Error starting online ASR orchestrator", e)
      return false
    }
  }

  /**
   * 开始在线ASR处理（MP3模式，支持配置和续传）- 内部方法
   */
  private fun startWithMp3Config(
    mp3DataFlow: SharedFlow<ByteArray>,
    mp3Config: Mp3EncodingConfig,
    resumeConfig: AsrResumeConfig?
  ): Boolean {
    try {
      if (resumeConfig != null) {
        AILog.i(TAG, "Starting MP3 mode with resume: sessionId=${resumeConfig.sessionId}, offset=${resumeConfig.resumeFromOffset}ms, chunkIndex=${resumeConfig.resumeFromMp3ChunkIndex}")
      } else {
        AILog.i(TAG, "Starting MP3 mode fresh session")
      }

      // 记录录音开始时间
      resumeSessionManager.setRecordingStartTime(System.currentTimeMillis())

      // 设置续传配置
      setAsrResumeConfig(resumeConfig)

      // 确保有MP3处理策略
      if (stateFlow.value.audioType != AudioType.MP3) {
        audioProcessingStrategy?.shutdown("Strategy change to MP3")
        updateState { it.withAudioType(AudioType.MP3) }
        audioProcessingStrategy = AudioProcessingStrategyFactory.createStrategy(stateFlow.value.audioType)
      }

      // 启动音频处理策略
      val config = AudioProcessingConfig.Mp3Config(mp3Config)
      if (!audioProcessingStrategy!!.startProcessing(mp3DataFlow, config, asrSocketClient,  readyStateFlow,this)) {
        AILog.e(TAG, "Failed to start MP3 processing strategy")
        return false
      }

      AILog.i(TAG, "Online ASR orchestrator started successfully with MP3 strategy")
      return true
    } catch (e: Exception) {
      AILog.e(TAG, "Error starting online ASR orchestrator in MP3 mode", e)
      return false
    }
  }

  /**
   * 启动结果
   */
  data class StartResult(
    val success: Boolean,
    val errorCode: String? = null,
    val errorMessage: String? = null
  ) {
    companion object {
      fun success() = StartResult(true)
      fun failure(errorCode: String, errorMessage: String) = StartResult(false, errorCode, errorMessage)
    }
  }

  /**
   * 使用录音配置启动在线ASR处理（内部处理网络配置获取）
   * @param config 录音配置
   * @param pcmDataFlow PCM数据流（用于Opus模式）
   * @param mp3DataFlow MP3数据流（用于MP3模式）
   * @return 启动结果
   */
  suspend fun startWithRecordingConfig(
    config: RecordingConfig,
    pcmDataFlow: SharedFlow<ByteArray>? = null,
    mp3DataFlow: SharedFlow<ByteArray>? = null
  ): StartResult {
    try {
      if (isRunning) {
        AILog.w(TAG, "Already running")
        return StartResult.success()
      }

      val configAudioType = config.onlineAsrConfig?.audioType ?: "ogg_opus"
      AILog.i(TAG, "Starting with recording config, audioType=$configAudioType")

      isRunning = true
      startResultProcessing()
      startConnectionStatusMonitoring()

      // 获取网络配置
      val networkConfig = fetchNetworkConfig(config)
      val hasNetworkConfig = networkConfigManager.validateNetworkConfig(networkConfig)

      if (hasNetworkConfig) {
        networkConfigManager.logNetworkConfigInfo(networkConfig!!)
        val websocketUrl = networkConfigManager.extractWebSocketUrl(networkConfig)
        if (websocketUrl != null) asrSocketClient.setConfig(websocketUrl)
        if (networkConfigManager.hasResumeConfig(networkConfig)) setAsrResumeConfig(networkConfig.asrResumeConfig)
      }

      // 启动本地音频处理
      val localProcessingStarted = if (configAudioType == "mp3") {
        if (mp3DataFlow == null) return StartResult.failure("MISSING_DATA_FLOW", "MP3 data flow is required")
        startWithMp3Config(mp3DataFlow, config.mp3Config, networkConfig?.asrResumeConfig)
      } else {
        if (pcmDataFlow == null) return StartResult.failure("MISSING_DATA_FLOW", "PCM data flow is required")
        startWithAsrResumeConfig(pcmDataFlow, config.opusConfig, networkConfig?.asrResumeConfig)
      }

      if (!localProcessingStarted) {
        isRunning = false
        return StartResult.failure("ASR_START_FAILED", "Failed to start local ASR processing strategy")
      }

      // 根据网络情况，决定是立即连接还是等待
      if (hasNetworkConfig && stateFlow.value.isNetworkAvailable) {
        AILog.i(TAG, "Attempting initial connection...")
        asrSocketClient.connect() // 异步非阻塞连接
      } else {
        AILog.w(TAG, "No network or config at startup. Will connect when network is available.")
        updateState { it.withRetryOnNetworkRestore(true) }
      }

      return StartResult.success()

    } catch (e: Exception) {
      AILog.e(TAG, "Error starting with recording config, exception=${e.message}")
      isRunning = false
      return StartResult.failure("UNKNOWN_ERROR", "Unexpected error: ${e.message}")
    }
  }

  /**
   * 发送结束信号并等待FINAL_TEXT响应
   */
  private suspend fun gracefulDisconnectAndJoin() {
    try {
      val currentState = asrSocketClient.getCurrentState()
      if (currentState != ConnectionStatus.CONNECTED) {
        AILog.i(TAG, "Not connected, performing direct disconnect")
        return
      }

      AILog.i(TAG, "Performing graceful disconnect...")

      // 发送空的ByteArray作为结束信号
      val endSignalSent = asrSocketClient.sendAudioData(ByteArray(0))
      if (!endSignalSent) {
        AILog.w(TAG, "Failed to send end signal, performing direct disconnect")
        return
      }

      AILog.i(TAG, "End signal sent, waiting for FINAL_TEXT response...")

      // 等待FINAL_TEXT结果，设置3秒超时
      val finalTextReceived = withTimeoutOrNull(3000L) {
        _transcriptionResultFlow.first { result ->
          result is TranscriptionResult.FinalTextResult
        }
      }

      if (finalTextReceived != null) {
        AILog.i(TAG, "Received FINAL_TEXT, graceful disconnect completed")
      } else {
        AILog.w(TAG, "Timeout waiting for FINAL_TEXT, but disconnect will continue")
      }

    } catch (e: Exception) {
      AILog.e(TAG, "Error during graceful disconnect", e)
    } finally {
      AILog.i(TAG, "Finally keep socket disconnected")
      asrSocketClient.disconnectAndJoin()
    }
  }

  /**
   * 停止在线ASR处理
   */
  suspend fun stop() {
    isRunning = false

    // 停止音频处理策略
    audioProcessingStrategy?.stopProcessing()
    audioProcessingStrategy?.stopResumeTransmission()

    // 断开连接并等待
    gracefulDisconnectAndJoin()

    // 停止结果处理
    resultProcessingJob?.cancelAndJoin()
    resultProcessingJob = null

    // 清理续传会话管理器状态
    resumeSessionManager.clear()

    // Reset state
    updateState { it.copy(shouldRetryOnNetworkRestore = false, isPausedByUser = false) }

    AILog.i(TAG, "Online ASR orchestrator stopped")
  }

  /**
   * 停止在线ASR处理 (立即返回，异步停止)
   * 用于需要快速响应的场景
   */
  fun stopAsync() {
    isRunning = false

    // 异步停止数据处理
    launch {
      try {
        // 停止音频处理策略
        audioProcessingStrategy?.stopProcessing()
        audioProcessingStrategy?.stopResumeTransmission()

        // 断开连接并等待
        gracefulDisconnectAndJoin()

        // 停止结果处理
        resultProcessingJob?.cancelAndJoin()
        resultProcessingJob = null

        AILog.d(TAG, "Online ASR orchestrator async stop completed")
      } catch (e: Exception) {
        AILog.e(TAG, "Error during async stop", e)
      }
    }

    // 立即执行的清理
    audioProcessingStrategy?.reset()

    // 清理续传会话管理器状态
    resumeSessionManager.clear()

    // Reset state
    updateState { it.copy(shouldRetryOnNetworkRestore = false, isPausedByUser = false) }

    AILog.i(TAG, "Online ASR orchestrator async stop initiated")
  }

  /**
   * 关闭协调器并等待完成
   * 用于需要确保完全清理的场景
   */
  suspend fun shutdownAndJoin(reason: String = "OnlineAsrOrchestrator shutdown and join") {
    stop()

    // 等待socket客户端完全销毁
    asrSocketClient.destroyAndJoin()

    // 关闭音频处理策略
    audioProcessingStrategy?.shutdown("$reason - strategy")

    // 停止网络监测器
    networkStateMonitor?.stop()

    // 关闭自己的协程作用域
    scopeDelegate.shutdown(reason)

    AILog.i(TAG, "Online ASR orchestrator shut down completely: $reason")
  }

  /**
   * 关闭协调器 (立即返回，异步清理)
   * 用于应用退出等需要快速响应的场景
   */
  fun shutdown(reason: String = "OnlineAsrOrchestrator shutdown") {
    // 立即停止（异步版本）
    stopAsync()

    // 异步销毁socket客户端
    launch {
      try {
        asrSocketClient.destroy()
        AILog.i(TAG, "Online ASR orchestrator async shutdown completed: $reason")
      } catch (e: Exception) {
        AILog.e(TAG, "Error during async shutdown", e)
      }
    }

    // 立即关闭同步资源
    audioProcessingStrategy?.shutdown("$reason - strategy")
    networkStateMonitor?.stop()
    scopeDelegate.shutdown(reason)
    AILog.i(TAG, "Online ASR orchestrator shutdown initiated: $reason")
  }

  /*
  * 获取 MP3 chunk 跟踪状态
  * 只有在使用MP3策略时才返回状态，否则返回null
  */
  fun getMp3ChunkTrackerStatus(): Mp3ChunkTrackerStatus? {
    return if (stateFlow.value.audioType == AudioType.MP3) {
      (audioProcessingStrategy as? Mp3ProcessingStrategy)?.getMp3ChunkTrackerStatus()
    } else {
      null
    }
  }

  /**
   * 启动结果处理流程
   */
  private fun startResultProcessing() {
    // 处理转写结果
    resultProcessingJob = launch {
      asrSocketClient.transcriptionResultFlow.collect { resultText ->
        try {
          val transcriptionResult = UnifiedJson.decodeFromString<TranscriptionResult>(resultText)
          _transcriptionResultFlow.tryEmit(transcriptionResult)

          if (transcriptionResult is TranscriptionResult.StartResult) {
            updateState { it.withReadyState(OnlineAsrReadyState.READY) }
            AILog.i(TAG, "Business state ready (StartResult received)")

            if (stateFlow.value.needsResumeOnReady) {
              updateState { it.withResumeOnReady(false) } // 立即重置，防止重复触发
              AILog.i(TAG, "State is READY, starting resume transmission now.")
              startResumeTransmission()
            }
          }
        } catch (e: Exception) {
          AILog.e(TAG, "Error processing transcription result", e)
        }
      }
    }
  }

  /**
   * 检查是否需要启动续传
   */
  private fun shouldStartResume(): Boolean {
    val strategy = audioProcessingStrategy ?: return false
    val config = resumeSessionManager.getAsrResumeConfig() ?: return false

    return resumeSessionManager.getResumeState() == ResumeState.NORMAL &&
      resumeSessionManager.getStorageManager() != null &&
      !resumeSessionManager.isPauseRealTimeProcessing() &&
      strategy.supportsResume(config)
  }

  /**
   * 启动续传逻辑
   * 委托给音频处理策略执行具体的续传操作
   */
  private fun startResumeTransmission() {
    val strategy = audioProcessingStrategy ?: return
    val config = resumeSessionManager.getAsrResumeConfig() ?: return
    val storage = resumeSessionManager.getStorageManager() ?: return

    AILog.i(TAG, "Starting resume transmission with strategy: ${strategy.javaClass.simpleName}")

    resumeSessionManager.setResumeState(ResumeState.RESUMING)
    resumeSessionManager.setPauseRealTimeProcessing(true)

    strategy.startResumeTransmission(
      resumeConfig = config,
      storageManager = storage,
      asrSocketClient = asrSocketClient,
      scope = this,
      onResumeCompleted = {
        // 续传完成回调
        resumeSessionManager.setResumeState(ResumeState.NORMAL)
        resumeSessionManager.setPauseRealTimeProcessing(false)
        AILog.i(TAG, "Resume transmission completed")
      }
    )
  }

  /**
   * 处理网络状态变化
   */
  private fun handleNetworkStateChange(isAvailable: Boolean) {
val flags = stateFlow.value
    
    // Atomic update of network state and retry flag
    updateState { currentState ->
      when {
        !flags.isNetworkAvailable && isAvailable -> {
          // Network restored - clear retry flag
          currentState.copy(
            isNetworkAvailable = true,
            shouldRetryOnNetworkRestore = false
          )
        }
        flags.isNetworkAvailable && !isAvailable -> {
          // Network lost - set retry flag if connection has problems
          val currentStatus = asrSocketClient.getCurrentState()
          val shouldRetry = currentStatus == ConnectionStatus.ERROR || currentStatus == ConnectionStatus.DISCONNECTED
          currentState.copy(
            isNetworkAvailable = false,
            shouldRetryOnNetworkRestore = shouldRetry
          )
        }
        else -> {
          // Just update network availability
          currentState.copy(isNetworkAvailable = isAvailable)
        }
      }
    }

    AILog.i(TAG, "Network: ${flags.isNetworkAvailable} -> $isAvailable")

    when {
      !flags.isNetworkAvailable && isAvailable -> {
        // 网络从不可用变为可用
        AILog.i(TAG, "Network restored")

        // 如果之前因为网络问题需要重试，现在尝试重连（但不在用户暂停状态下）
        val currentState = stateFlow.value
        if (currentState.shouldRetryOnNetworkRestore && isRunning && !currentState.isPausedByUser) {
          launch {
            // 网络恢复后稍等片刻再重连
            delay(RECONNECT_DELAY_MS)

            val latestState = stateFlow.value
            if (isRunning && latestState.isNetworkAvailable && !latestState.isPausedByUser) {
              attemptReconnection("Network restored")
            }
          }
        }
      }
      flags.isNetworkAvailable && !isAvailable -> {
        // 网络从可用变为不可用
        AILog.i(TAG, "Network lost")
      }
    }
  }

  /**
   * 处理连接状态变化
   */
  private fun handleConnectionStatusChange(status: ConnectionStatus) {
    AILog.d(TAG, "Connection status: $status")

    when (status) {
      ConnectionStatus.CONNECTED -> {
        updateState { it.withReadyState(OnlineAsrReadyState.CONNECTED).withRetryOnNetworkRestore(false) }
        AILog.i(TAG, "Socket connected, wait business ready")

        // 连接成功后，检查是否需要启动续传（但不在用户暂停状态下）
        val currentState = stateFlow.value
        if (!currentState.isPausedByUser && shouldStartResume()) {
          AILog.i(TAG, "Connection is up. Flagging to start resume when READY.")
          updateState { it.withResumeOnReady(true) }
        }
      }
      ConnectionStatus.DISCONNECTED -> {
        updateState { it.withReadyState(OnlineAsrReadyState.NOT_CONNECTED).withResumeOnReady(false) }
        AILog.i(TAG, "Socket disconnected")

        // 重置续传状态
        resumeSessionManager.setResumeState(ResumeState.NORMAL)
        resumeSessionManager.setPauseRealTimeProcessing(false)

        // 停止续传处理
        launch {
          audioProcessingStrategy?.stopResumeTransmission()
        }

        // 重置音频处理策略
        audioProcessingStrategy?.reset()
      }
      ConnectionStatus.ERROR -> {
        updateState { it.withReadyState(OnlineAsrReadyState.ERROR).withResumeOnReady(false) }
        AILog.i(TAG, "Socket error")

        // 如果是用户主动暂停，不进行任何重连尝试
        if (stateFlow.value.isPausedByUser) {
          return
        }

        // 检查网络状态，如果网络不可用，不进行重试
        if (!stateFlow.value.isNetworkAvailable) {
          updateState { it.withRetryOnNetworkRestore(true) }

          // 清理状态但不尝试重连
          if (resumeSessionManager.getAsrResumeConfig() == null) {
            audioProcessingStrategy?.reset()
            resumeSessionManager.setResumeState(ResumeState.NORMAL)
            resumeSessionManager.setPauseRealTimeProcessing(false)
          } else {
            audioProcessingStrategy?.reset()
          }
          return
        }

        if (isRunning) {
          launch {
            delay(RECONNECT_DELAY_MS)

            // 尝试获取续传配置，重新连接 socket
            attemptReconnection("network error reconnect")
          }
        }

        // 清理音频处理策略状态（但保留足够的数据用于续传）
        if (resumeSessionManager.getAsrResumeConfig() == null) {
          // 如果没有续传配置，完全清理
          audioProcessingStrategy?.reset()
          resumeSessionManager.setResumeState(ResumeState.NORMAL)
          resumeSessionManager.setPauseRealTimeProcessing(false)
        } else {
          // 如果有续传配置，只重置处理策略状态
          audioProcessingStrategy?.reset()
        }
      }
      else -> {}
    }
  }

  /**
   * 尝试重新连接
   */
  private suspend fun attemptReconnection(reason: String) {
    AILog.i(TAG, "Attempting reconnection: $reason")

    val currentState = stateFlow.value
    if (!isRunning || !currentState.isNetworkAvailable || currentState.isPausedByUser) {
      AILog.w(TAG, "Cannot reconnect: running=$isRunning, networkAvailable=${currentState.isNetworkAvailable}, isPausedByUser=${currentState.isPausedByUser}")
      return
    }

    try {
      // 尝试获取续传配置
      val resumeConfig = resumeSessionManager.fetchResumeConfig(reason)

      // 应用续传配置
      applyResumeConfig(resumeConfig)

      // 尝试连接
      asrSocketClient.connect()

    } catch (e: Exception) {
      AILog.e(TAG, "Error during reconnection attempt", e)
    }
  }

  /**
   * 启动连接状态监听
   */
  private fun startConnectionStatusMonitoring() {
    // 监听连接状态变化
    launch {
      asrSocketClient.connectionStatusFlow.collect { status ->
        handleConnectionStatusChange(status)
      }
    }
  }

  /**
   * 获取当前状态
   */
  fun getStatus(): OnlineAsrStatus {
    val flags = stateFlow.value
    val strategyStatus = audioProcessingStrategy?.getStatus()

    return OnlineAsrStatus(
      isRunning = isRunning,
      asrReadyState = flags.readyState,
      connectionStatus = asrSocketClient.getCurrentState(),
      resumeState = resumeSessionManager.getResumeState(),
      pauseRealTimeProcessing = strategyStatus?.isPausedRealTime ?: resumeSessionManager.isPauseRealTimeProcessing(),
      hasAsrResumeConfig = resumeSessionManager.getAsrResumeConfig() != null,
      currentAudioOffsetMs = resumeSessionManager.getAudioOffsetMs(),
      isNetworkAvailable = flags.isNetworkAvailable,
      networkType = networkStateMonitor?.getCurrentNetworkType() ?: NetworkStateMonitor.NetworkType.NONE,
      shouldRetryOnNetworkRestore = flags.shouldRetryOnNetworkRestore,
      isPausedByUser = flags.isPausedByUser,
      currentAudioType = flags.audioType,
      audioProcessingStatus = strategyStatus
    )
  }

  /**
   * 暂停在线ASR处理
   * 断开Socket连接，取消续传任务，但保留数据流处理job
   */
  suspend fun pauseOnlineAsr() {
    if (!isRunning) {
      AILog.w(TAG, "Cannot pause: not running")
      return
    }

    if (stateFlow.value.isPausedByUser) {
      AILog.w(TAG, "Already paused by user")
      return
    }

    AILog.i(TAG, "Pausing online ASR processing")

    try {
      updateState { it.withPause(true) }

      // 停止续传任务（如果正在进行）
      audioProcessingStrategy?.stopResumeTransmission()

      // 暂停等待结果
      gracefulDisconnectAndJoin()

      // 重置续传相关状态，但保留ASR配置用于恢复时的续传
      resumeSessionManager.setResumeState(ResumeState.NORMAL)
      resumeSessionManager.setPauseRealTimeProcessing(false)

      // 重置音频处理策略状态
      audioProcessingStrategy?.reset()

      AILog.i(TAG, "Online ASR paused successfully")
    } catch (e: Exception) {
      AILog.e(TAG, "Error during pause operation", e)
      // 如果暂停过程中出现错误，确保状态一致性
      updateState { it.withPause(true) } // 保持暂停状态，避免后续混乱
      throw e
    }
  }

  /**
   * 恢复在线ASR处理
   * 重新连接Socket，启动续传逻辑
   */
  suspend fun resumeOnlineAsr() {
    if (!isRunning) {
      AILog.w(TAG, "Cannot resume: not running")
      return
    }

    if (!stateFlow.value.isPausedByUser) {
      AILog.w(TAG, "Not paused by user")
      return
    }

    AILog.i(TAG, "Resuming ASR processing")

    try {
      updateState { it.withPause(false) }
      // 尝试获取最新的续传配置，重新连接 socket
      attemptReconnection("user resume")
      AILog.i(TAG, "ASR resume initiated")
    } catch (e: Exception) {
      AILog.e(TAG, "Error during resume operation", e)
      // 如果恢复失败，保持暂停状态
      // isPausedByUser 保持为 true
      throw e
    }
  }

  /**
   * 检查是否被用户暂停
   */
  fun isPausedByUser(): Boolean {
    return stateFlow.value.isPausedByUser
  }

  /**
   * 获取当前音频类型
   */
  fun getCurrentAudioType(): AudioType {
    return stateFlow.value.audioType
  }

  /**
   * 检查当前是否为 MP3 模式
   */
  fun isMp3Mode(): Boolean {
    return stateFlow.value.audioType == AudioType.MP3
  }

  /**
   * 检查当前是否为 OGG Opus 模式
   */
  fun isOpusMode(): Boolean {
    return stateFlow.value.audioType == AudioType.OGG_OPUS
  }
}

/**
 * 在线ASR状态
 */
data class OnlineAsrStatus(
  val isRunning: Boolean,
  val connectionStatus: ConnectionStatus,
  val asrReadyState: OnlineAsrReadyState,
  val resumeState: ResumeState,
  val pauseRealTimeProcessing: Boolean,
  val hasAsrResumeConfig: Boolean,
  val currentAudioOffsetMs: Long,
  val isNetworkAvailable: Boolean,
  val networkType: NetworkStateMonitor.NetworkType,
  val shouldRetryOnNetworkRestore: Boolean,
  val isPausedByUser: Boolean,
  val currentAudioType: AudioType,
  val audioProcessingStatus: AudioProcessingStatus? = null
)
