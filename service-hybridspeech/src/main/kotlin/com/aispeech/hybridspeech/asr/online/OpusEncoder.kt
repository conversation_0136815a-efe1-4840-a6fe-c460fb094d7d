package com.aispeech.hybridspeech.asr.online

import com.aispeech.aibase.AILog
import com.aispeech.hybridspeech.OpusEncodingConfig
import com.aispeech.hybridspeech.audio.opus.OggOpusEncoder
import com.aispeech.hybridspeech.core.CoroutineScopeManager
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.SharedFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.launch
import java.io.ByteArrayOutputStream
import java.util.concurrent.ConcurrentLinkedQueue

/**
 * Opus编码器
 * 将PCM数据编码为OGG Opus格式，用于在线ASR传输
 * 使用 lib-duilite SDK 的真实 Opus 编码能力
 */
class OpusEncoder: CoroutineScope {
  companion object {
    private const val TAG = "OpusEncoder"
    private const val SAMPLE_RATE = 16000
    private const val CHANNELS = 1
    private const val FRAME_SIZE = 960 // 60ms at 16kHz
    private const val BIT_RATE = 32000 // 32kbps
  }

  // 协程作用域委托 - 使用结构化并发
  private val scopeDelegate = CoroutineScopeManager.createModuleScopeDelegate(
    moduleName = TAG,
    parentScope = CoroutineScopeManager.getIOParentScope()
  )

  // 委托CoroutineScope接口到scopeDelegate
  override val coroutineContext = scopeDelegate.coroutineContext

  private val _oggDataFlow = MutableSharedFlow<ByteArray>(
    replay = 0,
    extraBufferCapacity = 100
  )
  val oggDataFlow: SharedFlow<ByteArray> = _oggDataFlow.asSharedFlow()

  private var isEncoding = false
  private var encodingJob: Job? = null
  private val pcmQueue = ConcurrentLinkedQueue<ByteArray>()
  private val pcmBuffer = ByteArrayOutputStream()

  // 使用 SDK 的真实 Opus 编码器
  private var encoder: OggOpusEncoder? = null
  private var currentConfig: OpusEncodingConfig? = null

  /**
   * 开始编码（支持配置）
   */
  fun startEncodingWithConfig(config: OpusEncodingConfig): Boolean {
    try {
      if (isEncoding) {
        AILog.w(TAG, "Already encoding")
        return true
      }

      currentConfig = config

      // 创建并初始化 SDK 的 Opus 编码器
      encoder = OggOpusEncoder().apply {
        // 初始化编码器，设置回调来接收编码后的数据
        init(
          SAMPLE_RATE, CHANNELS
        ) { data, size ->
          if (data != null && size > 0) {
            // 将编码后的数据发送到 Flow
            _oggDataFlow.tryEmit(data.copyOf(size))
          }
        }

        // 启动编码器
        start()
      }

      isEncoding = true

      encodingJob = launch {
        encodeLoop()
      }

      AILog.i(
        TAG,
        "Opus encoding started with config: bitRate=${config.bitRate}, frameSize=${config.frameSize}"
      )
      return true

    } catch (e: Exception) {
      AILog.e(TAG, "Failed to start Opus encoding with config", e)
      return false
    }
  }

  /**
   * 开始编码（使用默认配置）
   */
  fun startEncoding(): Boolean {
    return startEncodingWithConfig(OpusEncodingConfig.createDefault())
  }

  /**
   * 停止编码
   */
  fun stopEncoding() {
    isEncoding = false
    encodingJob?.cancel()

    // 停止并获取剩余数据
    encoder?.stop()

    // 销毁编码器
    encoder?.destroy()
    encoder = null
    currentConfig = null

    pcmQueue.clear()
    pcmBuffer.reset()
    AILog.i(TAG, "Opus encoding stopped")
  }

  /**
   * 输入PCM数据进行编码
   */
  fun encodePcmData(pcmData: ByteArray) {
    if (isEncoding) {
      pcmQueue.offer(pcmData)
    }
  }

  /**
   * 关闭编码器协程作用域
   * 取消所有子协程，但不影响其他模块
   */
  fun shutdown(reason: String = "OpusEncoder shutdown") {
    scopeDelegate.shutdown(reason)
    AILog.i(TAG, "Opus encoder scope shut down: $reason")
  }


  /**
   * 编码循环
   */
  private suspend fun encodeLoop() {
    while (isEncoding && !Thread.currentThread().isInterrupted) {
      try {
        val pcmData = pcmQueue.poll()
        if (pcmData != null) {
          // 直接将PCM数据送入SDK编码器
          encoder?.feed(pcmData)
        } else {
          delay(10) // 等待新数据
        }
      } catch (e: Exception) {
        AILog.e(TAG, "Error in encoding loop", e)
        break
      }
    }
  }

  /**
   * 重置编码器（用于网络重连时）
   */
  fun reset() {
    pcmQueue.clear()
    pcmBuffer.reset()

    // 重新初始化编码器
    currentConfig?.let { _ ->
      encoder?.destroy()
      encoder = OggOpusEncoder().apply {
        init(
          SAMPLE_RATE, CHANNELS
        ) { data, size ->
          if (data != null && size > 0) {
            _oggDataFlow.tryEmit(data.copyOf(size))
          }
        }
        start()
      }
    }

    AILog.i(TAG, "Opus encoder reset")
  }
}