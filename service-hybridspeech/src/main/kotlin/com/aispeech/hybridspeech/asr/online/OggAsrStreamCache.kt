package com.aispeech.hybridspeech.asr.online

import android.util.Log
import kotlinx.coroutines.DelicateCoroutinesApi
import kotlinx.coroutines.channels.BufferOverflow
import kotlinx.coroutines.channels.Channel
import kotlinx.coroutines.channels.ReceiveChannel

class OggAsrStreamCache(
  maxCacheBytes: Int = 8 * 1024 * 1024,   // 8 MB （约2分钟音频）
  avgFrameBytes: Int = 1024               // 默认 1 KB/帧
) {

  companion object { private const val TAG = "OggAsrStreamCache" }

  private val capacityFrames = (maxCacheBytes + avgFrameBytes - 1) / avgFrameBytes

  /**
   * 核心缓冲队列：
   *   - capacity = capacityFrames
   *   - DROP_OLDEST：满了直接丢最早的数据（保持实时性）
   */
  private val chan = Channel<ByteArray>(
    capacity = capacityFrames,
    onBufferOverflow = BufferOverflow.DROP_OLDEST
  )

  // 缓存保留时长配置（毫秒）
  private var cacheRetentionMs: Long = 60000L // 默认60秒

  /* ---------- 对外 API ---------- */

  fun asReceiveChannel(): ReceiveChannel<ByteArray> = chan

  /**
   * 设置缓存保留时长（毫秒）
   * 用于ASR续传场景，需要保留足够长的音频数据以支持断线重连后的续传
   */
  fun setCacheRetentionMs(retentionMs: Long) {
    this.cacheRetentionMs = retentionMs
    Log.i(TAG, "Cache retention time updated to ${retentionMs}ms")
  }

  /**
   * 获取缓存保留时长（毫秒）
   */
  fun getCacheRetentionMs(): Long = cacheRetentionMs

  /**
   * 写入一帧 Ogg 数据。
   * 1) 默认复制一份，避免外层复用 ByteArray 带来的脏写
   * 2) Channel 满时会自动丢最旧帧，有续传兜底，不会造成问题
   */
  @OptIn(DelicateCoroutinesApi::class)
  fun writeOggData(frame: ByteArray) {
    if (!chan.trySend(frame.copyOf()).isSuccess) {
      // 只有在channel未关闭但发送失败时，才认为是DROP_OLDEST发生了
      if (!chan.isClosedForSend) {
        Log.w(TAG, "Channel full, DROP_OLDEST occurred. Frame dropped.")
      } else {
        Log.w(TAG, "Channel closed, frame dropped.")
      }
    }
  }

  /**
   * 挂起读取 *一批* 数据，直到至少拿到一帧才返回。
   * 批量上限由 maxBatchBytes 控制，默认 8 KB。
   */
  suspend fun readOggDataBatch(maxBatchBytes: Int = 8 * 1024): List<ByteArray> {
    val batch = mutableListOf<ByteArray>()
    var used = 0

    val first = chan.receive()      // receive() = suspend until data
    batch += first
    used += first.size

    /* ---- 尝试在不挂起的情况下凑满本批次 ---- */
    while (used < maxBatchBytes) {
      val next = chan.tryReceive().getOrNull() ?: break
      if (used + next.size > maxBatchBytes && batch.isNotEmpty()) {
        // 放不下：压回去，供下一个批次处理
        chan.trySend(next)
        break
      }
      batch += next
      used += next.size
    }
    return batch
  }

  /** 是否还有数据（非挂起查询） */
  fun hasData(): Boolean = !chan.isEmpty

  /** 把缓存全部清掉 */
  fun clear() {
    while (chan.tryReceive().isSuccess) { /* drain */ }
  }

  /** 主动关闭，调用后无法再写入 */
  fun close(cause: Throwable? = null) = chan.close(cause)
}