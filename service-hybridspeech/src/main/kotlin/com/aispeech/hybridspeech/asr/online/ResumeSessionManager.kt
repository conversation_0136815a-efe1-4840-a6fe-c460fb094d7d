package com.aispeech.hybridspeech.asr.online

import com.aispeech.aibase.AILog
import com.aispeech.hybridspeech.AsrResumeConfig
import com.aispeech.hybridspeech.IHybridSpeechConfigProvider
import com.aispeech.hybridspeech.storage.RecordingStorageManager

/**
 * 续传会话管理器
 * 统一管理所有续传相关的属性和状态
 */
class ResumeSessionManager(
    private val updateState: ((OrchestratorFlags) -> OrchestratorFlags) -> Unit = {},
    private val getState: () -> OrchestratorFlags = { OrchestratorFlags.initial() }
) {

  companion object {
    private const val TAG = "ResumeSessionManager"
  }

  // 续传配置相关
  private var asrResumeConfig: AsrResumeConfig? = null
  private val networkConfigManager = NetworkConfigManager()

  // 会话状态相关
  private var currentSessionId: String? = null
  private var audioOffsetMs: Long = 0L // 服务端已收到的音频时长（毫秒）
  private var recordingStartTime: Long = 0L // 录音开始时间

  // 录音会话信息
  private var configProvider: IHybridSpeechConfigProvider? = null
  private var currentRecordId: Long? = null
  private var currentUserId: String? = null
  private var currentAudioType: String = "ogg_opus"

  // 存储管理器（用于续传时读取PCM文件）
  private var storageManager: RecordingStorageManager? = null

  private var currentPcmFilePath: String? = null

  // Resume state managed by state interactions now

  /**
   * 设置网络配置提供者和录音会话信息
   */
  fun setConfigProvider(
    provider: IHybridSpeechConfigProvider?,
    recordId: Long? = null,
    userId: String? = null,
    audioType: String? = null
  ) {
    this.configProvider = provider
    this.currentRecordId = recordId
    this.currentUserId = userId
    audioType?.let { this.currentAudioType = it }

    AILog.i(TAG, "Config provider ${if (provider != null) "set" else "cleared"}")
    if (provider != null) {
      AILog.i(TAG, "Recording session info: recordId=$recordId, userId=$userId, audioType=$audioType")
    }
  }

  /**
   * 设置ASR续传配置
   */
  fun setAsrResumeConfig(config: AsrResumeConfig?) {
    this.asrResumeConfig = config
    if (config != null) {
      AILog.i(TAG, "ASR resume config set: sessionId=${config.sessionId}, offset=${config.resumeFromOffset}ms")

      // 更新当前会话信息
      currentSessionId = config.sessionId
      audioOffsetMs = config.resumeFromOffset

    } else {
      AILog.i(TAG, "ASR resume config cleared")
      currentSessionId = null
      audioOffsetMs = 0L
    }
  }

  /**
   * 设置存储管理器（用于续传时读取PCM文件）
   */
  fun setStorageManager(manager: RecordingStorageManager?) {
    this.storageManager = manager
    AILog.i(TAG, "Storage manager ${if (manager != null) "set" else "cleared"}")
  }

  /**
   * 设置当前PCM文件路径
   */
  fun setCurrentPcmFilePath(path: String?) {
    this.currentPcmFilePath = path
    AILog.d(TAG, "Current PCM file path: $path")
  }

  /**
   * 设置录音开始时间
   */
  fun setRecordingStartTime(startTime: Long) {
    this.recordingStartTime = startTime
    AILog.d(TAG, "Recording start time: $startTime")
  }

  /**
   * 更新音频偏移量
   */
  fun updateAudioOffset(offsetMs: Long) {
    this.audioOffsetMs = offsetMs
    AILog.d(TAG, "Audio offset updated: ${offsetMs}ms")
  }

  /**
   * 更新会话ID
   */
  fun updateSessionId(sessionId: String?) {
    this.currentSessionId = sessionId
    AILog.d(TAG, "Session ID updated: $sessionId")
  }

  /**
   * 设置续传状态
   */
  fun setResumeState(state: ResumeState) {
    updateState { it.copy(resumeState = state) }
    AILog.d(TAG, "Resume state: $state")
  }

  /**
   * 设置是否暂停实时处理
   */
  fun setPauseRealTimeProcessing(pause: Boolean) {
    updateState { it.copy(pauseRealTimeProcessing = pause) }
    AILog.d(TAG, "Pause real-time processing: $pause")
  }

  /**
   * 获取续传配置
   */
  suspend fun fetchResumeConfig(reason: String): AsrResumeConfig? {
    val provider = configProvider ?: return null

    val sessionInfo = NetworkConfigManager.SessionInfo(
      recordId = currentRecordId ?: 0L,
      userId = currentUserId ?: "",
      audioType = currentAudioType
    )

    return networkConfigManager.fetchResumeConfig(
      provider = provider,
      sessionInfo = sessionInfo,
      currentSessionId = currentSessionId,
      audioOffsetMs = audioOffsetMs,
      reason = reason
    )
  }

  /**
   * 清理所有状态
   */
  fun clear() {
    AILog.i(TAG, "Clearing all resume session state")

    asrResumeConfig = null
    currentSessionId = null
    audioOffsetMs = 0L
    recordingStartTime = 0L

    configProvider = null
    currentRecordId = null
    currentUserId = null
    currentAudioType = "ogg_opus"

    storageManager = null
    resumeState = ResumeState.NORMAL
    currentPcmFilePath = null
    pauseRealTimeProcessing = false
  }

  // Getters
  fun getAsrResumeConfig(): AsrResumeConfig? = asrResumeConfig
  fun getCurrentSessionId(): String? = currentSessionId
  fun getAudioOffsetMs(): Long = audioOffsetMs
  fun getRecordingStartTime(): Long = recordingStartTime
  fun getConfigProvider(): IHybridSpeechConfigProvider? = configProvider
  fun getCurrentRecordId(): Long? = currentRecordId
  fun getCurrentUserId(): String? = currentUserId
  fun getCurrentAudioType(): String = currentAudioType
  fun getStorageManager(): RecordingStorageManager? = storageManager
  fun getResumeState(): ResumeState = getState().resumeState
  fun getCurrentPcmFilePath(): String? = currentPcmFilePath
  fun isPauseRealTimeProcessing(): Boolean = getState().pauseRealTimeProcessing
}
