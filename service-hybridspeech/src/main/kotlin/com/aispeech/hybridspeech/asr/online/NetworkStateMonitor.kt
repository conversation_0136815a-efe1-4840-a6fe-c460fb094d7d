package com.aispeech.hybridspeech.asr.online

import android.content.Context
import android.net.ConnectivityManager
import android.net.Network
import android.net.NetworkCapabilities
import com.aispeech.aibase.AILog
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch

/**
 * 网络状态监测器
 * 
 * 监测网络连接状态变化，为在线ASR提供网络状态信息：
 * - 网络断开时：暂停重试逻辑，避免无效的连接尝试
 * - 网络恢复时：恢复重试逻辑，继续ASR处理
 */
class NetworkStateMonitor(
    private val context: Context,
    private val coroutineScope: CoroutineScope
) {
    companion object {
        private const val TAG = "NetworkStateMonitor"
    }

    private val connectivityManager = context.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
    
    // 网络状态流
    private val _networkAvailableFlow = MutableStateFlow(isNetworkCurrentlyAvailable())
    val networkAvailableFlow: StateFlow<Boolean> = _networkAvailableFlow.asStateFlow()
    
    // 网络类型流（WiFi, 移动网络等）
    private val _networkTypeFlow = MutableStateFlow(getCurrentNetworkType())
    val networkTypeFlow: StateFlow<NetworkType> = _networkTypeFlow.asStateFlow()
    
    private var networkCallback: ConnectivityManager.NetworkCallback? = null
    private var isMonitoring = false

    /**
     * 网络类型枚举
     */
    enum class NetworkType {
        NONE,           // 无网络
        WIFI,           // WiFi网络
        CELLULAR,       // 移动网络
        ETHERNET,       // 以太网
        OTHER           // 其他类型网络
    }

    /**
     * 开始监测网络状态
     */
    fun start() {
        if (isMonitoring) {
            AILog.w(TAG, "Network monitoring already started")
            return
        }

        AILog.i(TAG, "Starting network state monitoring")
        
        // 初始化当前网络状态
        coroutineScope.launch {
          updateNetworkState()
        }
        
        // 创建网络回调
        networkCallback = createNetworkCallback()
        
        // 注册网络监听
        connectivityManager.registerDefaultNetworkCallback(networkCallback!!)

        isMonitoring = true
        AILog.i(TAG, "Network monitoring started successfully")
    }

    /**
     * 停止监测网络状态
     */
    fun stop() {
        if (!isMonitoring) {
            AILog.w(TAG, "Network monitoring not started")
            return
        }

        AILog.i(TAG, "Stopping network state monitoring")
        
        networkCallback?.let { callback ->
            try {
                connectivityManager.unregisterNetworkCallback(callback)
            } catch (e: Exception) {
                AILog.w(TAG, "Error unregistering network callback e=${e.message}")
            }
        }
        
        networkCallback = null
        isMonitoring = false
        AILog.i(TAG, "Network monitoring stopped")
    }

    /**
     * 获取当前网络是否可用
     */
    fun isNetworkAvailable(): Boolean {
        return _networkAvailableFlow.value
    }

    /**
     * 获取当前网络类型
     */
    fun getCurrentNetworkType(): NetworkType {
        return try {
            val activeNetwork = connectivityManager.activeNetwork ?: return NetworkType.NONE
            val networkCapabilities = connectivityManager.getNetworkCapabilities(activeNetwork) ?: return NetworkType.NONE
            
            when {
                networkCapabilities.hasTransport(NetworkCapabilities.TRANSPORT_WIFI) -> NetworkType.WIFI
                networkCapabilities.hasTransport(NetworkCapabilities.TRANSPORT_CELLULAR) -> NetworkType.CELLULAR
                networkCapabilities.hasTransport(NetworkCapabilities.TRANSPORT_ETHERNET) -> NetworkType.ETHERNET
                else -> NetworkType.OTHER
            }
        } catch (e: Exception) {
            AILog.w(TAG, "Error getting network type e: ${e.message}")
            NetworkType.NONE
        }
    }

    /**
     * 检查当前网络是否可用
     */
    private fun isNetworkCurrentlyAvailable(): Boolean {
        return try {
            val activeNetwork = connectivityManager.activeNetwork ?: return false
            val networkCapabilities = connectivityManager.getNetworkCapabilities(activeNetwork) ?: return false
            
            networkCapabilities.hasCapability(NetworkCapabilities.NET_CAPABILITY_INTERNET) &&
            networkCapabilities.hasCapability(NetworkCapabilities.NET_CAPABILITY_VALIDATED)
        } catch (e: Exception) {
            AILog.w(TAG, "Error checking network availability = ${e.message}")
            false
        }
    }

    /**
     * 创建网络状态回调
     */
    private fun createNetworkCallback(): ConnectivityManager.NetworkCallback {
        return object : ConnectivityManager.NetworkCallback() {
            override fun onAvailable(network: Network) {
                AILog.i(TAG, "Network available: $network")
                coroutineScope.launch {
                    updateNetworkState()
                }
            }

            override fun onLost(network: Network) {
                AILog.i(TAG, "Network lost: $network")
                coroutineScope.launch {
                    updateNetworkState()
                }
            }

            override fun onCapabilitiesChanged(network: Network, networkCapabilities: NetworkCapabilities) {
                AILog.d(TAG, "Network capabilities changed: $network")
                coroutineScope.launch {
                    updateNetworkState()
                }
            }

            override fun onUnavailable() {
                AILog.i(TAG, "Network unavailable")
                coroutineScope.launch {
                    _networkAvailableFlow.value = false
                    _networkTypeFlow.value = NetworkType.NONE
                }
            }
        }
    }

    /**
     * 更新网络状态
     */
    private suspend fun updateNetworkState() {
        val isAvailable = isNetworkCurrentlyAvailable()
        val networkType = getCurrentNetworkType()
        
        val previousAvailable = _networkAvailableFlow.value
        val previousType = _networkTypeFlow.value
        
        _networkAvailableFlow.value = isAvailable
        _networkTypeFlow.value = networkType
        
        // 记录状态变化
        if (previousAvailable != isAvailable) {
            AILog.i(TAG, "Network availability changed: $previousAvailable -> $isAvailable")
        }
        
        if (previousType != networkType) {
            AILog.i(TAG, "Network type changed: $previousType -> $networkType")
        }
    }

    /**
     * 获取网络状态描述
     */
    fun getNetworkStatusDescription(): String {
        val isAvailable = isNetworkAvailable()
        val networkType = getCurrentNetworkType()
        return "Network: ${if (isAvailable) "Available" else "Unavailable"}, Type: $networkType"
    }
}
