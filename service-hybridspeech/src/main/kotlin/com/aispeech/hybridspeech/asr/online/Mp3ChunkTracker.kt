package com.aispeech.hybridspeech.asr.online

import com.aispeech.aibase.AILog
import java.util.concurrent.atomic.AtomicInteger
import java.util.concurrent.atomic.AtomicLong

/**
 * MP3 chunk 跟踪器
 * 用于跟踪 MP3 编码的个数和长度，支持续传功能
 */
class Mp3ChunkTracker {
  companion object {
    private const val TAG = "Mp3ChunkTracker"
  }

  // 原子操作保证线程安全
  private val chunkCount = AtomicInteger(0)
  private val totalBytes = AtomicLong(0L)
  
  // 存储每个chunk的大小，用于续传时计算偏移量
  private val chunkSizes = mutableListOf<Int>()
  private val lock = Any()

  /**
   * 记录一个新的 MP3 chunk
   * @param chunkData MP3 chunk 数据
   * @return 当前 chunk 的索引（从0开始）
   */
  fun recordChunk(chunkData: ByteArray): Int {
    val chunkIndex = chunkCount.getAndIncrement()
    val chunkSize = chunkData.size
    totalBytes.addAndGet(chunkSize.toLong())
    
    synchronized(lock) {
      chunkSizes.add(chunkSize)
    }

    return chunkIndex
  }

  /**
   * 获取当前 chunk 总数
   */
  fun getChunkCount(): Int = chunkCount.get()

  /**
   * 获取总字节数
   */
  fun getTotalBytes(): Long = totalBytes.get()

  /**
   * 根据 chunk 索引计算字节偏移量
   * @param chunkIndex chunk 索引（从0开始）
   * @return 字节偏移量，如果索引无效返回-1
   */
  fun getByteOffsetByChunkIndex(chunkIndex: Int): Long {
    synchronized(lock) {
      if (chunkIndex < 0 || chunkIndex >= chunkSizes.size) {
        AILog.w(TAG, "Invalid chunk index: $chunkIndex, total chunks: ${chunkSizes.size}")
        return -1L
      }
      
      var offset = 0L
      for (i in 0 until chunkIndex) {
        offset += chunkSizes[i]
      }
      
      AILog.d(TAG, "Chunk index $chunkIndex corresponds to byte offset: $offset")
      return offset
    }
  }

  /**
   * 根据字节偏移量计算 chunk 索引
   * @param byteOffset 字节偏移量
   * @return chunk 索引，如果偏移量无效返回-1
   */
  fun getChunkIndexByByteOffset(byteOffset: Long): Int {
    synchronized(lock) {
      if (byteOffset < 0) {
        return -1
      }
      
      var currentOffset = 0L
      for (i in chunkSizes.indices) {
        if (currentOffset >= byteOffset) {
          AILog.d(TAG, "Byte offset $byteOffset corresponds to chunk index: $i")
          return i
        }
        currentOffset += chunkSizes[i]
      }
      
      // 如果偏移量超过了总大小，返回最后一个chunk的下一个索引
      val lastIndex = chunkSizes.size
      AILog.d(TAG, "Byte offset $byteOffset exceeds total size, returning chunk index: $lastIndex")
      return lastIndex
    }
  }

  /**
   * 获取从指定 chunk 索引开始的所有 chunk 大小
   * @param startChunkIndex 起始 chunk 索引
   * @return chunk 大小列表
   */
  fun getChunkSizesFromIndex(startChunkIndex: Int): List<Int> {
    synchronized(lock) {
      if (startChunkIndex < 0 || startChunkIndex >= chunkSizes.size) {
        return emptyList()
      }
      return chunkSizes.subList(startChunkIndex, chunkSizes.size).toList()
    }
  }

  /**
   * 重置跟踪器
   */
  fun reset() {
    chunkCount.set(0)
    totalBytes.set(0L)
    synchronized(lock) {
      chunkSizes.clear()
    }
    AILog.i(TAG, "MP3 chunk tracker reset")
  }

  /**
   * 获取跟踪器状态信息
   */
  fun getStatus(): Mp3ChunkTrackerStatus {
    synchronized(lock) {
      return Mp3ChunkTrackerStatus(
        chunkCount = chunkCount.get(),
        totalBytes = totalBytes.get(),
        chunkSizes = chunkSizes.toList()
      )
    }
  }
}

/**
 * MP3 chunk 跟踪器状态
 */
data class Mp3ChunkTrackerStatus(
  val chunkCount: Int,
  val totalBytes: Long,
  val chunkSizes: List<Int>
)
