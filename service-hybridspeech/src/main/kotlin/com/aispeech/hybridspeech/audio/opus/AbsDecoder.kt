package com.aispeech.hybridspeech.audio.opus
/**
 * User: zhengrong.shen
 * Date: 2022/9/1 10:55
 * Description:
 */
abstract class AbsDecoder {

  protected var mLength: Int = 3200
  protected var mOutput: ByteArray = ByteArray(mLength)
  protected var mMaxLength: Int = 0

  /** 若需要更大的输出缓冲区则扩容 */
  protected fun checkMaxOutput(outSize: Int) {
    if (outSize > mOutput.size) {
      mOutput = ByteArray(outSize)
    }
  }

  /** 与 checkMaxOutput 保持一致，名字来源于旧实现 */
  protected fun checkOutput(outSize: Int) {
    if (outSize > mOutput.size) {
      mOutput = ByteArray(outSize)
    }
  }

  /** 记录出现过的最大长度 */
  protected fun resetMaxLength(size: Int) {
    if (size > mMaxLength) {
      mMaxLength = size
    }
  }

  /** ************************* 抽象接口 ************************* */
  abstract fun init(cfg: String): Boolean
  abstract fun start(params: String)
  abstract fun feed(input: ByteArray, size: Int): ByteArray
  abstract fun stop()
  abstract fun stopAndGetAudio(): List<ByteArray>
  abstract fun release()

  /**
   * stop 后一次性返回完整的音频字节
   */
  fun stopAndGetAudioWithByte(): ByteArray {
    val endByteList = stopAndGetAudio()
    var endData = 0
    endByteList.forEach { endData += it.size }

    return if (endData > 0) {
      val audio = ByteArray(endData)
      var pos = 0
      for (bytes in endByteList) {
        System.arraycopy(bytes, 0, audio, pos, bytes.size)
        pos += bytes.size
      }
      audio
    } else {
      ByteArray(0)
    }
  }
}