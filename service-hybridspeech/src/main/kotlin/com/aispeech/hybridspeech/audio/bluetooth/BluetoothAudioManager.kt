package com.aispeech.hybridspeech.audio.bluetooth

import android.Manifest
import android.annotation.SuppressLint
import android.bluetooth.*
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.os.Build
import androidx.core.app.ActivityCompat
import androidx.core.content.PermissionChecker
import com.aispeech.aibase.AILog
import com.aispeech.tablet.lib.system.bluetooth.utils.BtHelper
import kotlinx.coroutines.*
import kotlinx.coroutines.channels.awaitClose
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.callbackFlow
import kotlinx.coroutines.sync.Mutex
import kotlinx.coroutines.sync.withLock
import java.io.IOException
import java.util.concurrent.atomic.AtomicBoolean

/**
 * 蓝牙音频管理器
 * 管理蓝牙HFP录音、A2DP播放，自动处理SCO音频链路
 */
class BluetoothAudioManager private constructor() {

  companion object {
    private const val TAG = "BluetoothAudioManager"

    @JvmStatic
    val instance: BluetoothAudioManager by lazy(LazyThreadSafetyMode.SYNCHRONIZED) {
      BluetoothAudioManager()
    }
  }

  private var bluetoothAdapter: BluetoothAdapter? = null
  private var bluetoothA2dp: BluetoothA2dp? = null
  private var bluetoothHeadset: BluetoothHeadset? = null

  private val isInitialized = AtomicBoolean(false)
  private val scoMutex = Mutex()

  // 监听器
  private var initListener: IInitListener? = null
  private var deviceStateListener: IDeviceConnectStateChangeListener? = null
  private var broadcastReceiver: HFPBroadcastReceiver? = null

  /**
   * 初始化监听器
   */
  interface IInitListener {
    fun onInitSuccess()
    fun onInitFailed(errCode: Int, errMsg: String?)
  }

  /**
   * 设备连接状态监听器
   */
  interface IDeviceConnectStateChangeListener {
    fun onDeviceConnectStateChange(device: BluetoothDevice?, state: Int)
  }

  /**
   * SCO连接监听器
   */
  interface ISCOConnectionListener {
    fun onSCOConnectionSuccess(device: BluetoothDevice)
    fun onSCOConnectionFailed(device: BluetoothDevice, reason: String)
  }

  /**
   * 初始化蓝牙管理器
   */
  fun init(context: Context, listener: IInitListener?) {
    initListener = listener

    bluetoothAdapter = BluetoothAdapter.getDefaultAdapter()

    if (isInitialized.get()) {
      AILog.i(TAG, "BluetoothAudioManager already initialized")
      listener?.onInitSuccess()
      return
    }

    if (isInitialized.compareAndSet(false, true)) {
      AILog.i(TAG, "Initializing BluetoothAudioManager")
      bindHeadsetProxy(context)
    }
  }

  /**
   * 是否支持HFP
   */
  fun isSupportHFP(): Boolean = bluetoothHeadset != null

  /**
   * 销毁管理器
   */
  fun destroy(context: Context) {
    AILog.i(TAG, "Destroying BluetoothAudioManager")

    isInitialized.set(false)

    bluetoothAdapter?.let { adapter ->
      bluetoothHeadset?.let { headset ->
        adapter.closeProfileProxy(BluetoothProfile.HEADSET, headset)
      }
      bluetoothA2dp?.let { a2dp ->
        adapter.closeProfileProxy(BluetoothProfile.A2DP, a2dp)
      }
    }

    bluetoothHeadset = null
    bluetoothA2dp = null
    unsubscribe(context)
  }

  /**
   * 订阅设备状态变化
   */
  fun subscribe(context: Context, listener: IDeviceConnectStateChangeListener?) {
    deviceStateListener = listener

    if (broadcastReceiver == null) {
      broadcastReceiver = HFPBroadcastReceiver()
      val intentFilter = IntentFilter().apply {
        addAction(BluetoothAdapter.ACTION_CONNECTION_STATE_CHANGED)
        addAction(BluetoothHeadset.ACTION_AUDIO_STATE_CHANGED)
        addAction(BluetoothHeadset.ACTION_CONNECTION_STATE_CHANGED)
      }

      try {
        context.registerReceiver(broadcastReceiver, intentFilter)
        AILog.i(TAG, "Bluetooth broadcast receiver registered")
      } catch (e: Exception) {
        AILog.e(TAG, "Failed to register broadcast receiver: ${e.message}")
        broadcastReceiver = null
      }
    }
  }

  /**
   * 取消订阅设备状态变化
   */
  fun unsubscribe(context: Context) {
    broadcastReceiver?.let { receiver ->
      try {
        context.unregisterReceiver(receiver)
        AILog.i(TAG, "Bluetooth broadcast receiver unregistered")
      } catch (e: Exception) {
        AILog.e(TAG, "Failed to unregister broadcast receiver: ${e.message}")
      }
    }
    broadcastReceiver = null
    deviceStateListener = null
  }

  /**
   * 根据Mac地址连接蓝牙设备
   */
  @Throws(IOException::class)
  fun connect(address: String) {
    if (address.isEmpty()) {
      throw IOException("Bluetooth address cannot be empty")
    }

    bluetoothAdapter?.let { adapter ->
      val device = adapter.getRemoteDevice(address)
      connect(device)
    } ?: throw IOException("Bluetooth adapter is not available")
  }

  /**
   * 连接蓝牙设备
   */
  fun connect(bluetoothDevice: BluetoothDevice?) {
    bluetoothDevice?.let { device ->
      connectHfp(device)
      connectA2dp(device)
    } ?: AILog.e(TAG, "Cannot connect to null device")
  }

  /**
   * 检查设备是否已连接
   */
  @SuppressLint("MissingPermission")
  fun isConnect(bluetoothDevice: BluetoothDevice?): Boolean {
    return bluetoothHeadset?.let { headset ->
      bluetoothDevice?.let { device ->
        headset.getConnectionState(device) == BluetoothProfile.STATE_CONNECTED
      } ?: false
    } ?: false
  }

  /**
   * 开始建立蓝牙SCO链路（异步版本）
   */
  suspend fun startBluetoothSCO(bluetoothDevice: BluetoothDevice?): Boolean {
    if (bluetoothHeadset == null || bluetoothDevice == null) {
      return false
    }

    return scoMutex.withLock {
      withContext(Dispatchers.IO) {
        startBluetoothSCOInternal(bluetoothDevice)
      }
    }
  }

  /**
   * 开始建立蓝牙SCO链路（带监听器）
   */
  fun startBluetoothSCO(bluetoothDevice: BluetoothDevice?, listener: ISCOConnectionListener?) {

    if (bluetoothHeadset == null || bluetoothDevice == null) {
      listener?.onSCOConnectionFailed(bluetoothDevice ?: return, "Headset service or device is null")
      return
    }

    CoroutineScope(Dispatchers.IO).launch {
      val success = startBluetoothSCO(bluetoothDevice)
      withContext(Dispatchers.Main) {
        if (success) {
          listener?.onSCOConnectionSuccess(bluetoothDevice)
        } else {
          listener?.onSCOConnectionFailed(bluetoothDevice, "SCO connection failed")
        }
      }
    }
  }

  /**
   * 断开蓝牙SCO链路
   */
  @SuppressLint("MissingPermission")
  suspend fun stopBluetoothSCO(bluetoothDevice: BluetoothDevice?): Boolean {
    return if (bluetoothHeadset != null && bluetoothDevice != null) {
      try {
        val result = bluetoothHeadset!!.stopVoiceRecognition(bluetoothDevice)
        AILog.i(TAG, "Stop voice recognition result: $result")
        result
      } catch (e: Exception) {
        AILog.e(TAG, "Failed to stop voice recognition: ${e.message}")
        false
      }
    } else false
  }

  /**
   * 获取已配对的设备
   */
  @SuppressLint("MissingPermission")
  fun getBondedDevices(context: Context): Set<BluetoothDevice>? {
    val bluetoothManager = context.getSystemService(Context.BLUETOOTH_SERVICE) as BluetoothManager

    if (bluetoothHeadset == null) {
      AILog.w(TAG, "Headset Profile is null")
      return null
    }

    // 权限检查
    if (Build.VERSION.SDK_INT > Build.VERSION_CODES.R &&
      ActivityCompat.checkSelfPermission(context, Manifest.permission.BLUETOOTH_CONNECT) != PermissionChecker.PERMISSION_GRANTED) {
      AILog.w(TAG, "BLUETOOTH_CONNECT permission not granted")
      return null
    }

    return try {
      val boundDevices = bluetoothManager.adapter.bondedDevices
      val sb = StringBuilder("Bonded devices: ")

      boundDevices.forEach { device ->
        val headSetState = getConnectionState(device)
        sb.append("{${BtHelper.printDevice(device)}, state=${BtHelper.printDeviceState(headSetState)}}, ")
      }

      AILog.i(TAG, sb.toString())
      boundDevices
    } catch (e: Exception) {
      AILog.e(TAG, "Failed to get bonded devices: ${e.message}")
      null
    }
  }

  /**
   * 获取设备连接状态
   */
  @SuppressLint("MissingPermission")
  fun getConnectionState(device: BluetoothDevice?): Int {
    return bluetoothHeadset?.let { headset ->
      device?.let { headset.getConnectionState(it) }
    } ?: BluetoothProfile.STATE_DISCONNECTED
  }

  /**
   * 监听设备状态变化的 Flow
   */
  fun listenDeviceChange(): Flow<BluetoothDevice?> = callbackFlow {
    AILog.i(TAG, "Starting device change listener")

    val listener = object : IDeviceConnectStateChangeListener {
      override fun onDeviceConnectStateChange(device: BluetoothDevice?, state: Int) {
        when (state) {
          BluetoothProfile.STATE_CONNECTED -> {
            AILog.i(TAG, "Device connected: ${BtHelper.printDevice(device)}")
            trySend(device)
          }
          BluetoothProfile.STATE_DISCONNECTED -> {
            AILog.i(TAG, "Device disconnected: ${BtHelper.printDevice(device)}")
            trySend(null)
          }
          BluetoothHeadset.STATE_AUDIO_CONNECTED -> {
            AILog.i(TAG, "Audio connected: ${BtHelper.printDevice(device)}")
            trySend(device)
          }
          BluetoothHeadset.STATE_AUDIO_DISCONNECTED -> {
            AILog.i(TAG, "Audio disconnected: ${BtHelper.printDevice(device)}")
            // 延迟发送断开事件，避免临时断开
            CoroutineScope(Dispatchers.IO).launch {
              delay(1000)
              trySend(null)
            }
          }
        }
      }
    }

    deviceStateListener = listener

    awaitClose {
      deviceStateListener = null
      AILog.i(TAG, "Device change listener stopped")
    }
  }

  // =================== 私有方法 ===================

  @SuppressLint("MissingPermission")
  private suspend fun startBluetoothSCOInternal(bluetoothDevice: BluetoothDevice): Boolean {
    val headset = bluetoothHeadset ?: return false
    var needStartedVoiceRecognition = false
    var isAudioConnected = false

    for (time in arrayOf(100, 200, 400, 800, 1600, 3200)) {
      if (headset != bluetoothHeadset) {
        AILog.e(TAG, "startBluetoothSCO failed!!! headset became null during loop.")
        return false
      }

      isAudioConnected = headset.isAudioConnected(bluetoothDevice)

      if (!isAudioConnected || !needStartedVoiceRecognition) {
        if (headset != bluetoothHeadset) {
          AILog.e(TAG, "startBluetoothSCO failed!!! headset became null before startVoiceRecognition.")
          return false
        }

        needStartedVoiceRecognition = headset.startVoiceRecognition(bluetoothDevice)
        AILog.i(TAG, "needStartedVoiceRecognition = $needStartedVoiceRecognition")
      }

      AILog.i(TAG, "isAudioConnected = $isAudioConnected, needStartedVoiceRecognition = $needStartedVoiceRecognition, time = $time")

      if (isAudioConnected && needStartedVoiceRecognition) {
        break
      }

      try {
        delay(time.toLong())
      } catch (e: InterruptedException) {
        Thread.currentThread().interrupt()
        return false
      }
    }

    AILog.i(TAG, "startVoiceRecognition end, isAudioConnected = $isAudioConnected, needStartedVoiceRecognition = $needStartedVoiceRecognition")

    return if (isAudioConnected) {
      true
    } else {
      AILog.e(TAG, "startVoiceRecognition failed!!!")
      false
    }
  }

  private fun bindHeadsetProxy(context: Context) {
    val adapter = bluetoothAdapter
    if (adapter == null) {
      initListener?.onInitFailed(-1, "Bluetooth adapter is null")
      return
    }

    AILog.i(TAG, "Binding headset proxy services")

    try {
      adapter.closeProfileProxy(BluetoothProfile.HEADSET, bluetoothHeadset)
      adapter.closeProfileProxy(BluetoothProfile.A2DP, bluetoothA2dp)

      val headsetResult = adapter.getProfileProxy(
        context.applicationContext,
        HeadsetServiceListener(),
        BluetoothProfile.HEADSET
      )

      val a2dpResult = adapter.getProfileProxy(
        context.applicationContext,
        HeadsetServiceListener(),
        BluetoothProfile.A2DP
      )

      if (!headsetResult) {
        initListener?.onInitFailed(-2, "Failed to get HEADSET profile proxy")
        return
      }

      AILog.i(TAG, "Profile proxy requests submitted - HEADSET: $headsetResult, A2DP: $a2dpResult")

    } catch (e: Exception) {
      AILog.e(TAG, "Exception binding headset proxy: ${e.message}")
      initListener?.onInitFailed(-3, "Exception: ${e.message}")
    }
  }

  private fun connectHfp(device: BluetoothDevice) {
    val headset = bluetoothHeadset
    if (headset == null) {
      AILog.e(TAG, "Cannot connect HFP - headset service is null")
      return
    }

    try {
      val connectMethod = headset.javaClass.getMethod("connect", BluetoothDevice::class.java)
      connectMethod.invoke(headset, device)
      AILog.i(TAG, "HFP connection initiated for: ${BtHelper.printDevice(device)}")
    } catch (e: Exception) {
      AILog.e(TAG, "Failed to connect HFP: ${e.message}")
    }
  }

  private fun connectA2dp(device: BluetoothDevice) {
    val a2dp = bluetoothA2dp
    if (a2dp == null) {
      AILog.e(TAG, "Cannot connect A2DP - service is null")
      return
    }

    try {
      val connectMethod = a2dp.javaClass.getMethod("connect", BluetoothDevice::class.java)
      connectMethod.invoke(a2dp, device)
      AILog.i(TAG, "A2DP connection initiated for: ${BtHelper.printDevice(device)}")
    } catch (e: Exception) {
      AILog.e(TAG, "Failed to connect A2DP: ${e.message}")
    }
  }

  // =================== 内部类 ===================

  private inner class HeadsetServiceListener : BluetoothProfile.ServiceListener {
    override fun onServiceConnected(profile: Int, proxy: BluetoothProfile) {
      AILog.i(TAG, "Bluetooth service connected, profile: $profile")

      when (profile) {
        BluetoothProfile.HEADSET -> {
          bluetoothHeadset = proxy as BluetoothHeadset
          initListener?.onInitSuccess()
        }
        BluetoothProfile.A2DP -> {
          bluetoothA2dp = proxy as BluetoothA2dp
        }
      }
    }

    override fun onServiceDisconnected(profile: Int) {
      AILog.i(TAG, "Bluetooth service disconnected, profile: $profile")
      when (profile) {
        BluetoothProfile.HEADSET -> bluetoothHeadset = null
        BluetoothProfile.A2DP -> bluetoothA2dp = null
      }
    }
  }

  private inner class HFPBroadcastReceiver : BroadcastReceiver() {
    override fun onReceive(context: Context, intent: Intent) {
      val action = intent.action ?: return

      when (action) {
        BluetoothAdapter.ACTION_CONNECTION_STATE_CHANGED,
        BluetoothHeadset.ACTION_CONNECTION_STATE_CHANGED -> {
          val device = intent.getParcelableExtra<BluetoothDevice>(BluetoothDevice.EXTRA_DEVICE)
          val state = intent.getIntExtra(BluetoothAdapter.EXTRA_CONNECTION_STATE, BluetoothProfile.STATE_DISCONNECTED)

          AILog.i(TAG, "Connection state changed - Device: ${BtHelper.printDevice(device)}, State: ${BtHelper.printDeviceState(state)}")
          deviceStateListener?.onDeviceConnectStateChange(device, state)
        }

        BluetoothHeadset.ACTION_AUDIO_STATE_CHANGED -> {
          val device = intent.getParcelableExtra<BluetoothDevice>(BluetoothDevice.EXTRA_DEVICE)
          val state = intent.getIntExtra(BluetoothHeadset.EXTRA_STATE, BluetoothHeadset.STATE_AUDIO_DISCONNECTED)

          AILog.i(TAG, "Audio state changed - Device: ${BtHelper.printDevice(device)}, Audio State: $state")
          deviceStateListener?.onDeviceConnectStateChange(device, state)
        }
      }
    }
  }
}
