package com.aispeech.hybridspeech.audio.opus

import android.os.Handler
import android.os.HandlerThread
import com.aispeech.aibase.AILog

/**
 * opus 编码，复用 duilite sdk 能力
 */
class OggOpusEncoder {

  companion object {
    private const val TAG = "OggOpusEncoder"
  }

  /**
   * 编码回调函数
   */
  fun interface IOpusEncoderCallback {
    fun onEncoderBuffer(data: ByteArray, size: Int)
  }

  private var mSampleRate = 16000
  private var mChannel = 1
  private var bitrate = 32000

  private var mPcm2Opus: Pcm2Opus? = null
  private var iOpusEncoderCallback: IOpusEncoderCallback? = null

  @Volatile
  private var mHandler: Handler? = null

  /**
   * 获取工作线程的 Handler（懒加载 + 双重校验）
   */
  private fun getWorker(): Handler {
    var handler = mHandler
    if (handler == null) {
      synchronized(this) {
        handler = mHandler
        if (handler == null) {
          val thread = HandlerThread("OggOpus-WorkerThread")
          thread.start()
          handler = Handler(thread.looper)
          mHandler = handler
          AILog.i(TAG, "init ogg opus encoder thread")
        }
      }
    }
    return handler!!
  }

  /**
   * 初始化
   */
  fun init(sampleRate: Int, channel: Int, callback: IOpusEncoderCallback?) {
    getWorker().post {
      mChannel = channel
      mSampleRate = sampleRate
      iOpusEncoderCallback = callback

      val cfg = """{
              "samplerate": $mSampleRate,
              "channels": $mChannel,
              "bitrate": $bitrate,
              "framesize": 20,
              "nframe": 2,
              "complexity": 8
            }""".trimIndent()

      AILog.i(TAG, "init, cfg: $cfg, callback: $iOpusEncoderCallback")
      mPcm2Opus = Pcm2Opus(true).apply { init(cfg) }
    }
  }

  /**
   * 开始编码
   */
  fun start() {
    getWorker().post {
      val opus = mPcm2Opus
      if (opus == null) {
        AILog.e(TAG, "invalid start !!!")
        return@post
      }
      AILog.i(TAG, "start")
      opus.start("{}")
    }
  }

  /**
   * 写入 PCM 数据
   */
  fun feed(buffer: ByteArray) {
    getWorker().post {
      val opus = mPcm2Opus
      if (opus == null) {
        AILog.e(TAG, "invalid feed ${buffer.size} !!!")
        return@post
      }
      val output = opus.feed(buffer, buffer.size)
      if (output != null && output.isNotEmpty()) {
        iOpusEncoderCallback?.onEncoderBuffer(output, output.size)
      }
    }
  }

  /**
   * 停止编码并返回剩余数据
   */
  fun stop() {
    AILog.i(TAG, "stop begin")
    getWorker().post {
      val opus = mPcm2Opus
      if (opus == null) {
        AILog.e(TAG, "invalid stop!!!")
        return@post
      }
      val dataList: List<ByteArray> = opus.stopAndGetAudio()
      for (output in dataList) {
        if (output.isNotEmpty()) {
          iOpusEncoderCallback?.onEncoderBuffer(output, output.size)
        }
      }
      AILog.i(TAG, "stop end")
    }
  }

  /**
   * 释放资源
   */
  fun destroy() {
    AILog.i(TAG, "destroy begin")
    getWorker().post {
      mPcm2Opus?.let {
        AILog.i(TAG, "destroy ogg opus encoder")
        it.release()
        mPcm2Opus = null
      }

      mHandler?.looper?.quitSafely()
      mHandler = null
      AILog.i(TAG, "destroy end")
    }
  }
}