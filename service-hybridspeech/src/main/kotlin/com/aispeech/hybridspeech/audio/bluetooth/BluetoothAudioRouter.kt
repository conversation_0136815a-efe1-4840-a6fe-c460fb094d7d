package com.aispeech.hybridspeech.audio.bluetooth

import android.bluetooth.BluetoothDevice
import android.bluetooth.BluetoothHeadset
import android.bluetooth.BluetoothProfile
import android.content.Context
import com.aispeech.aibase.AILog
import com.aispeech.tablet.core.common.Tablet
import com.aispeech.tablet.lib.system.bluetooth.utils.BtHelper
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.channels.awaitClose
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.callbackFlow
import kotlinx.coroutines.launch
import kotlinx.coroutines.sync.Mutex
import kotlinx.coroutines.sync.withLock
import kotlinx.coroutines.withContext
import java.util.concurrent.atomic.AtomicReference

object BluetoothAudioRouter {
  private const val TAG = "BluetoothAudioRouter"

  private val connectedDevice = AtomicReference<BluetoothDevice?>()
  private val bluetoothManager = BluetoothAudioManager.instance
  private val deviceMutex = Mutex()

  private var disconnectJob: Job? = null

  /**
   * 初始化蓝牙音频路由器
   */
  fun initWith(onInitialized: ((Boolean, String?) -> Unit)? = null) {
    bluetoothManager.init(Tablet.application, object : BluetoothAudioManager.IInitListener {
      override fun onInitSuccess() {
        AILog.i(TAG, "蓝牙音频路由器初始化成功")
        onInitialized?.invoke(true, null)
      }

      override fun onInitFailed(errCode: Int, errMsg: String?) {
        AILog.e(TAG, "蓝牙音频路由器初始化失败: $errCode, $errMsg")
        onInitialized?.invoke(false, errMsg)
      }
    })
  }

  /**
   * 是否支持蓝牙音频
   */
  fun support(): Boolean = bluetoothManager.isSupportHFP()

  /**
   * 开始语音识别（建立SCO连接）
   */
  @Suppress("UNUSED_PARAMETER")
  suspend fun startVoiceRecognition(forceRefresh: Boolean = false): Boolean = withContext(Dispatchers.IO) {
    deviceMutex.withLock {
      val bondedDevices = bluetoothManager.getBondedDevices(Tablet.application)
      val matchDevice = matchDevice(bondedDevices)

      if (matchDevice != null) {
        connectedDevice.set(matchDevice)
        AILog.i(TAG, "startVoiceRecognition device = ${BtHelper.printDevice(matchDevice)}, bondedDevices = ${BtHelper.printDeviceList(bondedDevices)}")
        bluetoothManager.startBluetoothSCO(matchDevice)
      } else {
        connectedDevice.set(null)
        AILog.w(TAG, "not found matchDevice, ignore start sco, bondedDevices = ${BtHelper.printDeviceList(bondedDevices)}")
        false
      }
    }
  }

  /**
   * 停止语音识别（断开SCO连接）
   */
  suspend fun stopVoiceRecognition() {
    val device = connectedDevice.get()
    if (device != null) {
      AILog.i(TAG, "stopVoiceRecognition device = ${BtHelper.printDevice(device)}")
      bluetoothManager.stopBluetoothSCO(device)
    } else {
      AILog.w(TAG, "not found matchDevice, ignore stop sco")
    }
  }

  /**
   * 检查SCO是否激活
   */
  fun isScoActive(): Boolean {
    // 这里可以添加实际的SCO状态检查逻辑
    return connectedDevice.get() != null
  }

  /**
   * 获取当前设备
   */
  fun getCurrentDevice(): BluetoothDevice? = connectedDevice.get()

  /**
   * 监听设备变化
   */
  fun listenDeviceChange() = callbackFlow {
    AILog.i(TAG, "listenDeviceChange, subscribe")
    var currentDevice: BluetoothDevice? = null

    bluetoothManager.subscribe(Tablet.application, object : BluetoothAudioManager.IDeviceConnectStateChangeListener {
      override fun onDeviceConnectStateChange(device: BluetoothDevice?, state: Int) {
        runCatching {
          disconnectJob?.cancel()

          when (state) {
            BluetoothProfile.STATE_CONNECTED -> {
              currentDevice = device
              connectedDevice.set(device)
              trySend(device)
            }
            BluetoothProfile.STATE_DISCONNECTED -> {
              currentDevice = null
              connectedDevice.set(null)
              trySend(null)
            }
            BluetoothHeadset.STATE_AUDIO_DISCONNECTED -> {
              disconnectJob = CoroutineScope(Dispatchers.IO).launch {
                delay(1000)
                currentDevice?.let { dev ->
                  AILog.i(TAG, "Force disconnect due to audio disconnect: ${BtHelper.printDevice(dev)}")
                  connectedDevice.set(null)
                  trySend(null)
                }
                currentDevice = null
              }
            }
          }

          AILog.i(
            TAG, "onDeviceConnectStateChange, state = ${BtHelper.printDeviceState(state)}, " +
            "dev: ${BtHelper.printDevice(device)}, " +
            "connectedDevice: ${BtHelper.printDevice(connectedDevice.get())}")
        }
      }
    })

    awaitClose {
      bluetoothManager.unsubscribe(Tablet.application)
      AILog.i(TAG, "listenDeviceChange, unsubscribe")
    }
  }

  /**
   * 销毁资源
   */
  fun destroy(context: Context) {
    disconnectJob?.cancel()
    connectedDevice.set(null)
    bluetoothManager.destroy(context)
  }

  private fun matchDevice(devices: Set<BluetoothDevice>?): BluetoothDevice? {
    val currentConnected = connectedDevice.get()

    // 如果当前有连接的设备，优先使用它（如果仍然连接）
    if (currentConnected != null) {
      return devices?.filter { bluetoothManager.getConnectionState(it) == BluetoothProfile.STATE_CONNECTED }
        ?.find { it == currentConnected }
        ?: devices?.firstOrNull { bluetoothManager.getConnectionState(it) == BluetoothProfile.STATE_CONNECTED }
    }

    // 返回第一个已连接的设备
    return devices?.firstOrNull { bluetoothManager.getConnectionState(it) == BluetoothProfile.STATE_CONNECTED }
  }
}
