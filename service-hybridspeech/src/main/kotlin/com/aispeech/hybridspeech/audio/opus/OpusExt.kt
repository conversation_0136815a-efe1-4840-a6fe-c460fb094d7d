package com.aispeech.hybridspeech.audio.opus

import com.aispeech.aibase.AILog
import com.aispeech.kernel.Opus
import org.json.JSONObject

/**
 * sdk 不支持 opus_encode_enc2 传入 type = 2（OPUS_OGG_EMBED），
 * 与内核沟通后扩展出的封装类
 */
class OpusExt : Opus() {

  companion object {
    private const val TAG = "OpusExt"

    /** 思必驰新增的自定义类型 */
    const val PCM_TO_OPUS_OGG_EMBED = 2
  }

  private var engineId: Long = 0
  private var mType: Int = PCM_TO_OPUS_OGG_EMBED
  private var mEngineId: Long = 0

  /**
   * 初始化实例; 调用 start/stop/cancel 等操作前必须调用
   *
   * @param standard true -> ogg_opus; false -> 内部非标准 opus
   * @param callback Opus 原生回调
   */
  override fun init(standard: Boolean, callback: opus_callback?): Long {
    engineId = opus_encode_new(if (standard) 0 else 1, callback)
    AILog.d(TAG, "init: $engineId")
    return engineId
  }

  /**
   * 开始请求
   */
  override fun start(
    channels: Int,
    samplerate: Int,
    bitrate: Int,
    complexity: Int,
    framesize: Int
  ): Int {

    val jsonObj = JSONObject().apply {
      put("channels", channels)
      put("samplerate", samplerate)
      put("bitrate", bitrate)
      put("complexity", complexity)
      put("framesize", framesize)
    }

    AILog.d(TAG, "start: $engineId")
    val ret = opus_encode_start(engineId, jsonObj.toString())
    if (ret < 0) {
      AILog.e(TAG, "start failed! Error code: $ret")
      return -1
    }
    AILog.d(TAG, "start ret: $ret")
    return ret
  }

  /** PCM/OPUS 数据写入 */
  override fun feed(buffer: ByteArray): Int = opus_encode_feed(engineId, buffer)

  /** 结束写入 */
  override fun stop(): Int {
    AILog.d(TAG, "stop: $engineId")
    return opus_encode_stop(engineId)
  }

  /** 释放资源 */
  override fun destroy() {
    AILog.d(TAG, "destroy: $engineId")
    opus_encode_delete(engineId)
    engineId = 0
    AILog.d(TAG, "destroy finished: $engineId")
  }

  /**
   * 通用 DDS 初始化
   *
   * @param normal  兼容旧接口，无实际作用
   * @param type    OPUS_TO_PCM / PCM_TO_OPUS / PCM_TO_OPUS_OGG_EMBED
   * @param params  初始化 json
   */
  override fun ddsInit(normal: Boolean, type: Int, params: String): Long {
    if (!isSoValid()) {
      AILog.e(TAG, "load libopusogg library error! ddsInit -> return!")
      return 0L
    }

    mType = type
    mEngineId = when (mType) {
      OPUS_TO_PCM -> {
        AILog.d(TAG, "ddsInit opus2pcm...$type")
        opus_decode_new2(type, params)
      }
      PCM_TO_OPUS, PCM_TO_OPUS_OGG_EMBED -> {
        AILog.d(TAG, "ddsInit pcm2opus...$type")
        opus_encode_new2(type, params)
      }
      else -> 0
    }
    AILog.d(TAG, "ddsInit result = $mEngineId")
    return mEngineId
  }

  /**
   * 通用数据写入
   *
   * @param input  输入数据
   * @param size   数据长度（可根据需要决定是否使用）
   * @param output 输出缓冲区
   * @return ret   内核返回值
   */
  override fun ddsFeed(input: ByteArray, size: Int, output: ByteArray): Int {
    if (!isSoValid()) {
      AILog.e(TAG, "load libopusogg library error! ddsFeed -> return!")
      return -1
    }

    return when (mType) {
      OPUS_TO_PCM -> opus_decode_dec2(mEngineId, input, output)
      PCM_TO_OPUS, PCM_TO_OPUS_OGG_EMBED -> opus_encode_enc2(mEngineId, input, output)
      else -> -1
    }
  }

  /** 释放 DDS 相关资源 */
  override fun ddsDestroy() {
    if (!isSoValid()) {
      AILog.e(TAG, "load libopusogg library error! ddsDestroy -> return!")
      return
    }

    AILog.d(TAG, "ddsDestroy..., type = $mType")
    when (mType) {
      OPUS_TO_PCM -> opus_decode_del2(mEngineId)
      PCM_TO_OPUS, PCM_TO_OPUS_OGG_EMBED -> opus_encode_del2(mEngineId)
    }
  }
}