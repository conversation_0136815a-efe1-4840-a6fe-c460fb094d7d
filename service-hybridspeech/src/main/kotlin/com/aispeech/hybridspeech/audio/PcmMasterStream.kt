package com.aispeech.hybridspeech.audio

import com.aispeech.aibase.AILog
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.SharedFlow
import kotlinx.coroutines.flow.asSharedFlow
import java.util.concurrent.ConcurrentLinkedQueue

/**
 * 原始PCM主数据流
 * 负责接收来自AudioRecorderDevice的PCM数据，并分发给各个消费者
 */
class PcmMasterStream {
    companion object {
        private const val TAG = "PcmMasterStream"
        private const val MAX_BUFFER_SIZE = 1024 * 1024 // 1MB缓存
    }

    private val _pcmDataFlow = MutableSharedFlow<ByteArray>(
        replay = 0,
        extraBufferCapacity = 200
    )
    val pcmDataFlow: SharedFlow<ByteArray> = _pcmDataFlow.asSharedFlow()

    // 用于存储PCM数据的环形缓冲区
    private val pcmBuffer = ConcurrentLinkedQueue<PcmChunk>()
    private var totalBufferSize = 0
    private var currentTimestamp = 0L

    /**
     * 接收PCM数据
     */
    fun feedPcmData(data: ByteArray) {
        try {
            val timestamp = System.currentTimeMillis()
            val chunk = PcmChunk(data, timestamp, currentTimestamp)
            
            // 添加到缓冲区
            pcmBuffer.offer(chunk)
            totalBufferSize += data.size
            currentTimestamp += calculateDurationMs(data.size)

            // 清理过多的缓存数据
            while (totalBufferSize > MAX_BUFFER_SIZE && pcmBuffer.isNotEmpty()) {
                val removedChunk = pcmBuffer.poll()
                if (removedChunk != null) {
                    totalBufferSize -= removedChunk.data.size
                }
            }

            // 分发给订阅者
            _pcmDataFlow.tryEmit(data)

        } catch (e: Exception) {
            AILog.e(TAG, "Error feeding PCM data", e)
        }
    }

    /**
     * 获取指定时间范围的PCM数据
     */
    fun getPcmDataByTimeRange(startTimeMs: Long, endTimeMs: Long): ByteArray? {
        try {
            val chunks = pcmBuffer.filter { chunk ->
              chunk.relativeTimestamp in startTimeMs..endTimeMs
            }

            if (chunks.isEmpty()) {
                return null
            }

            val totalSize = chunks.sumOf { it.data.size }
            val result = ByteArray(totalSize)
            var offset = 0

            chunks.forEach { chunk ->
                System.arraycopy(chunk.data, 0, result, offset, chunk.data.size)
                offset += chunk.data.size
            }

            return result

        } catch (e: Exception) {
            AILog.e(TAG, "Error getting PCM data by time range", e)
            return null
        }
    }

    /**
     * 获取最近的PCM数据
     */
    fun getRecentPcmData(durationMs: Long): ByteArray? {
        val endTime = currentTimestamp
        val startTime = maxOf(0, endTime - durationMs)
        return getPcmDataByTimeRange(startTime, endTime)
    }

    /**
     * 清空缓冲区
     */
    fun clearBuffer() {
        pcmBuffer.clear()
        totalBufferSize = 0
        currentTimestamp = 0L
        AILog.i(TAG, "PCM buffer cleared")
    }

    /**
     * 计算音频数据的时长（毫秒）
     * 假设16kHz采样率，16位，单声道
     */
    private fun calculateDurationMs(dataSize: Int): Long {
        val sampleRate = 16000
        val bytesPerSample = 2 // 16位 = 2字节
        val channels = 1
        
        val samples = dataSize / (bytesPerSample * channels)
        return (samples * 1000L) / sampleRate
    }

    /**
     * 获取当前缓冲区状态
     */
    fun getBufferStatus(): BufferStatus {
        return BufferStatus(
            chunkCount = pcmBuffer.size,
            totalSizeBytes = totalBufferSize,
            durationMs = currentTimestamp
        )
    }
}

/**
 * PCM数据块
 */
data class PcmChunk(
    val data: ByteArray,
    val absoluteTimestamp: Long, // 绝对时间戳
    val relativeTimestamp: Long  // 相对于录音开始的时间戳
) {
    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (javaClass != other?.javaClass) return false

        other as PcmChunk

        if (!data.contentEquals(other.data)) return false
        if (absoluteTimestamp != other.absoluteTimestamp) return false
        if (relativeTimestamp != other.relativeTimestamp) return false

        return true
    }

    override fun hashCode(): Int {
        var result = data.contentHashCode()
        result = 31 * result + absoluteTimestamp.hashCode()
        result = 31 * result + relativeTimestamp.hashCode()
        return result
    }
}

/**
 * 缓冲区状态
 */
data class BufferStatus(
    val chunkCount: Int,
    val totalSizeBytes: Int,
    val durationMs: Long
)
