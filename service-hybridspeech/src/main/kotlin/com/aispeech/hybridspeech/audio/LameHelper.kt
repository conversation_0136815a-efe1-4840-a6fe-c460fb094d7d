package com.aispeech.hybridspeech.audio

import android.os.Handler
import android.os.HandlerThread
import com.aispeech.aibase.AILog
import com.aispeech.lame.LameEncode

/**
 * LAME MP3编码器辅助类
 * 维持单例，队列处理，线程安全
 */
object LameHelper {
    private var mHandler: Handler? = null
    private var mSampleRate: Int = 16000
    private var mChannel: Int = 1
    private var mBuffer: ByteArray? = null

    private var isInit: Boolean = false

    private const val TAG = "LameHelper"

    /**
     * 使用指定参数创建编码器
     */
    fun createWithParam(sampleRate: Int, channel: Int, bitRate: Int = 32, quality: Int = 3) {
        if (mHandler == null) {
            val handlerThread = HandlerThread("lame-handlerThread")
            handlerThread.start()
            mHandler = Handler(handlerThread.looper)
        }

        mHandler?.post {
            if (isInit.not() || mSampleRate != sampleRate || mChannel != channel) {
                mSampleRate = sampleRate
                mChannel = channel
                mBuffer = ByteArray(((mSampleRate * 2 / 10) / 2 * 1.25 + 7200).toInt())

                if (isInit) {
                    AILog.i(TAG, "Close old LameEncode")
                    LameEncode.close()
                }

                // 使用传入的bitRate和quality参数
                LameEncode.initForRecord(mSampleRate, mChannel, bitRate, quality)
                isInit = true
                AILog.i(TAG, "Init with sampleRate: $mSampleRate, channel: $mChannel, bitRate: $bitRate, quality: $quality")
            }
        }
    }

    /**
     * 编码PCM数据为MP3
     */
    fun encoder(input: ShortArray, callback: (ByteArray, Int) -> Unit) {
        mHandler?.post {
            if (isInit && mBuffer != null) {
                val size = LameEncode.encoderForRecord(input, mBuffer, input.size)
                callback.invoke(mBuffer!!, size)
            }
        }
    }

    /**
     * 刷新编码器，获取剩余数据
     */
    fun flush(callback: (ByteArray, Int) -> Unit) {
        mHandler?.post {
            if (isInit) {
                val cache = ByteArray(10 * 1024)
                val size = LameEncode.flush(cache)
                callback.invoke(cache, size)
            }
        }
    }

    /**
     * 销毁编码器
     */
    fun destroy() {
        mHandler?.post {
            AILog.i(TAG, "destroy. isInit = $isInit")
            if (isInit) {
                LameEncode.close()
            }
            isInit = false
            mHandler?.looper?.quit()
            mHandler = null
        }
    }

    /**
     * 检查是否已初始化
     */
    fun isInit() = isInit
}
