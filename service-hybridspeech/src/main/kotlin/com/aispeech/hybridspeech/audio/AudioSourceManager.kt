package com.aispeech.hybridspeech.audio

import android.annotation.SuppressLint
import android.bluetooth.BluetoothDevice
import android.media.MediaRecorder
import com.aispeech.aibase.AILog
import com.aispeech.hybridspeech.audio.bluetooth.BluetoothAudioRouter
import kotlinx.coroutines.TimeoutCancellationException
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.runBlocking
import kotlinx.coroutines.sync.Mutex
import kotlinx.coroutines.sync.withLock
import kotlinx.coroutines.withTimeout
import java.util.concurrent.atomic.AtomicBoolean

/**
 * 音频源接口
 */
interface AudioSource {
  /**
   * 准备音频源
   * @return 准备是否成功
   */
  suspend fun prepare(): Boolean

  /**
   * 释放音频源
   */
  fun release()

  /**
   * 获取AudioRecord使用的音频源类型
   */
  fun getAudioSource(): Int

  /**
   * 获取音频源描述
   */
  fun getDescription(): String

  /**
   * 检查音频源是否激活
   */
  fun isActive(): Boolean

  /**
   * 获取优先级（数值越大优先级越高）
   */
  fun getPriority(): Int

  /**
   * 获取准备超时时间（毫秒）
   */
  fun getPrepareTimeout(): Long
}

/**
 * 默认音频源实现
 */
class DefaultAudioSource : AudioSource {
  private val isActive = AtomicBoolean(false)

  override suspend fun prepare(): Boolean {
    isActive.set(true)
    AILog.i("DefaultAudioSource", "默认音频源准备完成")
    return true
  }

  override fun release() {
    isActive.set(false)
    AILog.i("DefaultAudioSource", "默认音频源已释放")
  }

  override fun getAudioSource(): Int = MediaRecorder.AudioSource.MIC
  override fun getDescription(): String = "默认麦克风"
  override fun isActive(): Boolean = isActive.get()
  override fun getPriority(): Int = 0 // 最低优先级
  override fun getPrepareTimeout(): Long = 100L
}

/**
 * 蓝牙音频源实现
 */
class BluetoothAudioSource : AudioSource {
  companion object {
    private const val TAG = "BluetoothAudioSource"
    private const val BLUETOOTH_CONNECT_TIMEOUT = 5000L
    private const val SCO_VERIFY_TIMEOUT = 2000L
  }

  private val bluetoothRouter = BluetoothAudioRouter
  private val isActive = AtomicBoolean(false)
  private var connectedDevice: BluetoothDevice? = null

  @SuppressLint("MissingPermission")
  override suspend fun prepare(): Boolean {
    if (!bluetoothRouter.support()) {
      AILog.i(TAG, "蓝牙不支持，跳过")
      return false
    }

    return try {
      AILog.i(TAG, "开始准备蓝牙音频源")

      // 1. 启动蓝牙连接
      val connected = withTimeout(BLUETOOTH_CONNECT_TIMEOUT) {
        bluetoothRouter.startVoiceRecognition()
      }

      if (!connected) {
        AILog.w(TAG, "蓝牙连接失败")
        return false
      }

      // 2. 验证SCO连接状态
      val scoActive = withTimeout(SCO_VERIFY_TIMEOUT) {
        var retryCount = 0
        while (retryCount < 10 && !bluetoothRouter.isScoActive()) {
          delay(200)
          retryCount++
          AILog.d(TAG, "等待SCO激活... ($retryCount/10)")
        }
        bluetoothRouter.isScoActive()
      }

      if (scoActive) {
        connectedDevice = bluetoothRouter.getCurrentDevice()
        isActive.set(true)
        AILog.i(TAG, "蓝牙音频源准备成功: ${connectedDevice?.name}")
        true
      } else {
        AILog.w(TAG, "SCO连接未激活")
        // 清理连接
        bluetoothRouter.stopVoiceRecognition()
        false
      }

    } catch (e: TimeoutCancellationException) {
      AILog.w(TAG, "蓝牙音频源准备超时")
      false
    } catch (e: Exception) {
      AILog.e(TAG, "蓝牙音频源准备异常: ${e.message}", e)
      false
    }
  }

  override fun release() {
    if (isActive.get()) {
      AILog.i(TAG, "释放蓝牙音频源")
      runBlocking {
        try {
          bluetoothRouter.stopVoiceRecognition()
        } catch (e: Exception) {
          AILog.e(TAG, "释放蓝牙音频源异常: ${e.message}")
        }
      }
      isActive.set(false)
      connectedDevice = null
    }
  }

  override fun getAudioSource(): Int = MediaRecorder.AudioSource.VOICE_RECOGNITION

  @SuppressLint("MissingPermission")
  override fun getDescription(): String {
    return if (connectedDevice != null) {
      "蓝牙耳机: ${connectedDevice?.name ?: "未知设备"}"
    } else {
      "蓝牙耳机"
    }
  }

  override fun isActive(): Boolean = isActive.get()
  override fun getPriority(): Int = 10 // 高优先级
  override fun getPrepareTimeout(): Long = BLUETOOTH_CONNECT_TIMEOUT + SCO_VERIFY_TIMEOUT + 500L
}

/**
 * 音频源状态信息
 */
data class AudioSourceInfo(
  val type: String,
  val description: String,
  val audioSource: Int,
  val isActive: Boolean,
  val bluetoothDevice: BluetoothDevice? = null
)

/**
 * 音频源管理器
 */
class AudioSourceManager private constructor() {
  companion object {
    private const val TAG = "AudioSourceManager"

    @JvmStatic
    val instance: AudioSourceManager by lazy(LazyThreadSafetyMode.SYNCHRONIZED) {
      AudioSourceManager()
    }
  }

  private val audioSources = mutableListOf<AudioSource>()
  private var currentSource: AudioSource? = null
  private val sourceMutex = Mutex()
  private val isInitialized = AtomicBoolean(false)

  // 音频源状态流
  private val _currentAudioSourceFlow = MutableStateFlow<AudioSourceInfo?>(null)
  val currentAudioSourceFlow: StateFlow<AudioSourceInfo?> = _currentAudioSourceFlow.asStateFlow()

  /**
   * 初始化音频源管理器
   */
  fun initialize() {
    if (isInitialized.compareAndSet(false, true)) {
      AILog.i(TAG, "初始化音频源管理器")

      registerAudioSource(BluetoothAudioSource())

      AILog.i(TAG, "音频源管理器初始化完成，共注册 ${audioSources.size} 个音频源")
    }
  }

  /**
   * 注册音频源
   */
  fun registerAudioSource(source: AudioSource) {
    audioSources.add(source)
    audioSources.sortByDescending { it.getPriority() }
    AILog.i(TAG, "注册音频源: ${source.getDescription()}, 优先级: ${source.getPriority()}")
  }

  /**
   * 选择最佳音频源
   * @param timeout 总超时时间
   * @return 选择的音频源
   */
  suspend fun selectBestAudioSource(timeout: Long = 8000L): AudioSource {
    return sourceMutex.withLock {
      try {
        withTimeout(timeout) {
          selectBestAudioSourceInternal()
        }
      } catch (e: TimeoutCancellationException) {
        AILog.w(TAG, "选择音频源超时，使用默认音频源")
        selectDefaultAudioSource()
      }
    }
  }

  private suspend fun selectBestAudioSourceInternal(): AudioSource {
    AILog.i(TAG, "开始选择最佳音频源")

    // 检查当前音频源是否仍然有效
    currentSource?.let { current ->
      if (current.isActive()) {
        AILog.i(TAG, "当前音频源仍然有效: ${current.getDescription()}")
        return current
      } else {
        AILog.i(TAG, "当前音频源已失效，释放: ${current.getDescription()}")
        current.release()
        currentSource = null
      }
    }

    // 按优先级尝试每个音频源
    for (source in audioSources) {
      AILog.i(TAG, "尝试准备音频源: ${source.getDescription()}")

      try {
        val prepared = withTimeout(source.getPrepareTimeout()) {
          source.prepare()
        }

        if (prepared) {
          AILog.i(TAG, "音频源准备成功: ${source.getDescription()}")
          setCurrentSource(source)
          return source
        } else {
          AILog.i(TAG, "音频源准备失败: ${source.getDescription()}")
        }
      } catch (e: TimeoutCancellationException) {
        AILog.w(TAG, "音频源准备超时: ${source.getDescription()}")
      } catch (e: Exception) {
        AILog.e(TAG, "音频源准备异常: ${source.getDescription()}, ${e.message}")
      }
    }

    // 如果所有音频源都失败，使用默认音频源
    AILog.w(TAG, "所有音频源都不可用，强制使用默认音频源")
    return selectDefaultAudioSource()
  }

  private suspend fun selectDefaultAudioSource(): AudioSource {
    val defaultSource = audioSources.find { it is DefaultAudioSource } ?: DefaultAudioSource()
    defaultSource.prepare()
    setCurrentSource(defaultSource)
    return defaultSource
  }

  private fun setCurrentSource(source: AudioSource) {
    // 释放之前的音频源
    currentSource?.let { oldSource ->
      if (oldSource != source) {
        AILog.i(TAG, "释放之前的音频源: ${oldSource.getDescription()}")
        oldSource.release()
      }
    }

    currentSource = source

    // 更新状态流
    val sourceInfo = AudioSourceInfo(
      type = when (source) {
        is BluetoothAudioSource -> "bluetooth"
        is DefaultAudioSource -> "default"
        else -> "unknown"
      },
      description = source.getDescription(),
      audioSource = source.getAudioSource(),
      isActive = source.isActive(),
      bluetoothDevice = if (source is BluetoothAudioSource) {
        BluetoothAudioRouter.getCurrentDevice()
      } else null
    )

    _currentAudioSourceFlow.tryEmit(sourceInfo)
    AILog.i(TAG, "当前音频源已设置: ${source.getDescription()}")
  }

  /**
   * 获取当前音频源
   */
  fun getCurrentSource(): AudioSource? = currentSource

  /**
   * 释放当前音频源
   */
  fun releaseCurrentSource() {
    currentSource?.let { source ->
      AILog.i(TAG, "释放当前音频源: ${source.getDescription()}")
      source.release()
      currentSource = null
      _currentAudioSourceFlow.tryEmit(null)
    }
  }

  /**
   * 强制刷新音频源
   */
  suspend fun refreshAudioSource(): AudioSource {
    AILog.i(TAG, "强制刷新音频源")
    return sourceMutex.withLock {
      // 释放当前音频源
      currentSource?.release()
      currentSource = null

      // 重新选择
      selectBestAudioSourceInternal()
    }
  }

  /**
   * 获取所有已注册的音频源
   */
  fun getRegisteredSources(): List<AudioSource> = audioSources.toList()

  /**
   * 销毁管理器
   */
  fun destroy() {
    if (isInitialized.compareAndSet(true, false)) {
      AILog.i(TAG, "销毁音频源管理器")

      runBlocking {
        releaseCurrentSource()
      }

      audioSources.clear()
      _currentAudioSourceFlow.tryEmit(null)
    }
  }
}
