package com.aispeech.hybridspeech.audio.opus

import com.aispeech.aibase.AILog

/**
 * 将 PCM 编码为 Ogg-Opus 的简单封装
 */
class Pcm2Opus(private val mNorm: Boolean) : AbsDecoder() {

  companion object {
    private const val TAG = "Pcm2Opus"
  }

  private val mOpus = OpusExt()

  /** 初始化 */
  override fun init(cfg: String): Boolean {
    val ret: Long = mOpus.ddsInit(mNorm, OpusExt.PCM_TO_OPUS_OGG_EMBED, cfg)
    return ret != 0L
  }

  /** 向内核推送 pcm，取得编码后的 opus */
  override fun feed(input: ByteArray, size: Int): ByteArray {
    if (size == 0) return ByteArray(0)

    resetMaxLength(size)
    val decodeOutSize = size / 7          // 对应旧实现的预估长度
    checkOutput(decodeOutSize)

    val outSize = mOpus.ddsFeed(input, input.size, mOutput)
    // AILog.d(TAG, "feed outSize = $outSize, input size = $size")

    return if (outSize <= 0) {
      ByteArray(0)
    } else {
      ByteArray(outSize).apply {
        System.arraycopy(mOutput, 0, this, 0, outSize)
      }
    }
  }

  /** 启动（这里只是向内核写一个空包与旧实现保持一致） */
  override fun start(params: String) {
    mOpus.ddsFeed(ByteArray(0), 0, mOutput)
  }

  /** 停止（同样写一个空包） */
  override fun stop() {
    mOpus.ddsFeed(ByteArray(0), 0, mOutput)
  }

  /** 请求结束后，将剩余 opus 数据全部返回 */
  override fun stopAndGetAudio(): List<ByteArray> {
    val endByteList = mutableListOf<ByteArray>()
    val decodeOutSize = mMaxLength
    checkOutput(decodeOutSize)

    val endByte = ByteArray(0)
    while (true) {
      val outSize = mOpus.ddsFeed(endByte, 0, mOutput)
      AILog.i(TAG, "stopAndGetAudio outSize = $outSize")
      if (outSize == 0) {
        return endByteList
      }
      val temp = ByteArray(outSize)
      System.arraycopy(mOutput, 0, temp, 0, outSize)
      endByteList.add(temp)
    }
  }

  /** 释放 JNI 资源 */
  override fun release() {
    mOpus.ddsDestroy()
  }
}