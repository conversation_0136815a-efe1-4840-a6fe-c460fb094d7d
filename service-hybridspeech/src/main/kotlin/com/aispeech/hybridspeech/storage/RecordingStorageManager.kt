package com.aispeech.hybridspeech.storage

import com.aispeech.aibase.AILog
import com.aispeech.hybridspeech.Mp3EncodingConfig
import com.aispeech.hybridspeech.core.CoroutineScopeManager
import com.aispeech.hybridspeech.core.safeCollect
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Job
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.SharedFlow
import kotlinx.coroutines.launch

/**
 * 录音存储管理器
 * 订阅PCM和MP3流，提供本地存储功能
 * 提供按时长/片段获取PCM数据的接口
 */
class RecordingStorageManager: CoroutineScope {
  companion object {
    private const val TAG = "RecordingStorageManager"
  }

  // 协程作用域委托 - 使用结构化并发
  private val scopeDelegate = CoroutineScopeManager.createModuleScopeDelegate(
    moduleName = TAG,
    parentScope = CoroutineScopeManager.getIOParentScope()
  )

  // 委托CoroutineScope接口到scopeDelegate
  override val coroutineContext = scopeDelegate.coroutineContext

  private val pcmFileWriter = PcmFileWriter()
  private val mp3FileWriter = Mp3FileWriter()

  private var isRunning = false
  private var pcmSubscriptionJob: Job? = null
  private var mp3SubscriptionJob: Job? = null

  /**
   * 开始订阅音频流并存储（支持指定文件路径）
   */
  fun startRecordingWithPaths(
    pcmFilePath: String,
    mp3FilePath: String,
    pcmDataFlow: SharedFlow<ByteArray>,
    mp3DataFlow: SharedFlow<ByteArray>,
    mp3Config: Mp3EncodingConfig
  ): Boolean {
    try {
      if (isRunning) {
        AILog.w(TAG, "Already running")
        return true
      }

      // 启动PCM文件写入
      if (!pcmFileWriter.startWriting(pcmFilePath)) {
        AILog.e(TAG, "Failed to start PCM file writing")
        return false
      }

      // 启动MP3文件写入（使用指定路径和配置）
      if (!mp3FileWriter.startWritingWithPath(mp3FilePath, mp3Config)) {
        AILog.e(TAG, "Failed to start MP3 file writing with path")
        pcmFileWriter.stopWriting()
        return false
      }

      // 订阅PCM数据流
      pcmSubscriptionJob = launch {
        pcmDataFlow.safeCollect(
          tag = TAG,
          onError = { error ->
            AILog.e(TAG, "Error in PCM data flow", error)
          }
        ) { pcmData ->
          pcmFileWriter.writePcmData(pcmData)
        }
      }

      // 订阅MP3数据流
      mp3SubscriptionJob = launch {
        mp3DataFlow.safeCollect(
          tag = TAG,
          onError = { error ->
            AILog.e(TAG, "Error in MP3 data flow", error)
          }
        ) { mp3Data ->
          mp3FileWriter.writeMp3Data(mp3Data)
        }
      }

      isRunning = true
      AILog.i(TAG, "Started recording with specified paths:")
      AILog.i(TAG, "  PCM: $pcmFilePath")
      AILog.i(TAG, "  MP3: $mp3FilePath")
      return true

    } catch (e: Exception) {
      AILog.e(TAG, "Error starting recording with specified paths", e)
      return false
    }
  }



  /**
   * 停止录制
   */
  fun stopRecording(): RecordingResult? {
    isRunning = false

    // 停止订阅
    pcmSubscriptionJob?.cancel()
    mp3SubscriptionJob?.cancel()

    // 停止文件写入
    val pcmFilePath = pcmFileWriter.stopWriting()
    val mp3FilePath = mp3FileWriter.stopWriting()

    AILog.i(TAG, "Recording stopped")

    return if (pcmFilePath != null && mp3FilePath != null) {
      RecordingResult(pcmFilePath, mp3FilePath)
    } else {
      null
    }
  }

  /**
   * 获取指定时间段的PCM数据
   */
  fun getPcmDataByTimeRange(filePath: String, startTimeMs: Long, endTimeMs: Long): ByteArray? {
    return pcmFileWriter.readPcmDataByTimeRange(filePath, startTimeMs, endTimeMs)
  }

  /**
   * 获取从指定偏移量开始的PCM数据（用于续传）
   */
  fun getPcmDataFromOffset(filePath: String, offsetMs: Long): ByteArray? {
    return pcmFileWriter.readPcmDataFromOffset(filePath, offsetMs)
  }

  /**
   * 获取从指定 chunk 索引开始的 MP3 数据（用于续传）
   * @param filePath MP3 文件路径
   * @param startChunkIndex 起始 chunk 索引
   * @param chunkSizes chunk 大小列表，用于计算偏移量
   * @return MP3 数据，如果读取失败返回null
   */
  fun getMp3DataFromChunkIndex(filePath: String, startChunkIndex: Int, chunkSizes: List<Int>): ByteArray? {
    return mp3FileWriter.readMp3DataFromChunkIndex(filePath, startChunkIndex, chunkSizes)
  }

  /**
   * 创建从指定 chunk 索引开始的 MP3 数据流（用于续传）
   * @param filePath MP3 文件路径
   * @param startChunkIndex 起始 chunk 索引
   * @param chunkSizes chunk 大小列表，用于计算偏移量
   * @param chunkSizeBytes 每次读取的 chunk 大小（字节）
   * @return MP3 数据流，如果创建失败返回null
   */
  fun createMp3DataStreamFromChunkIndex(
    filePath: String,
    startChunkIndex: Int,
    chunkSizes: List<Int>,
    chunkSizeBytes: Int = 1024
  ): Flow<ByteArray>? {
    return mp3FileWriter.createMp3DataStreamFromChunkIndex(filePath, startChunkIndex, chunkSizes, chunkSizeBytes)
  }


  /**
   * 创建从指定偏移量开始的PCM数据流（用于续传）
   * @param filePath PCM文件路径
   * @param offsetMs 开始偏移量（毫秒）
   * @param chunkSizeMs 每次读取的时长（毫秒）
   * @return PCM数据流
   */
  fun createPcmDataStreamFromOffset(
    filePath: String,
    offsetMs: Long,
    chunkSizeMs: Long = 100L
  ): Flow<ByteArray>? {
    return pcmFileWriter.createPcmDataStreamFromOffset(filePath, offsetMs, chunkSizeMs)
  }

  /**
   * 获取当前录制的PCM文件路径（如果正在录制）
   */
  fun getCurrentPcmFilePath(): String? {
    return if (isRunning) {
      pcmFileWriter.getCurrentFilePath()
    } else {
      null
    }
  }

  /**
   * 获取当前录制的总时长（毫秒）
   */
  fun getCurrentRecordingDurationMs(): Long {
    return pcmFileWriter.getCurrentDurationMs()
  }

  /**
   * 获取当前录制的MP3文件路径（如果正在录制）
   */
  fun getCurrentMp3FilePath(): String? {
    return if (isRunning) {
      mp3FileWriter.getCurrentFilePath()
    } else {
      null
    }
  }


  /**
   * 获取管理器状态
   */
  fun getStatus(): RecordingStorageManagerStatus {
    return RecordingStorageManagerStatus(
      isRecording = isRunning,
      pcmWritingStatus = pcmFileWriter.getWritingStatus(),
      mp3WritingStatus = mp3FileWriter.getWritingStatus()
    )
  }

  /**
   * 检查是否正在录制
   */
  fun isRecording(): Boolean {
    return isRunning
  }

  /**
   * 关闭存储管理器协程作用域
   */
  fun shutdown(reason: String = "RecordingStorageManager shutdown") {
    // 先关闭子组件
    pcmFileWriter.shutdown("Storage manager shutdown")
    mp3FileWriter.shutdown("Storage manager shutdown")

    // 再关闭自己的作用域
    scopeDelegate.shutdown(reason)
    AILog.i(TAG, "Recording storage manager scope shut down: $reason")
  }
}

/**
 * 录制结果
 */
data class RecordingResult(
  val pcmFilePath: String,
  val mp3FilePath: String
)

/**
 * 录音存储管理器状态
 */
data class RecordingStorageManagerStatus(
  val isRecording: Boolean,
  val pcmWritingStatus: PcmWritingStatus,
  val mp3WritingStatus: Mp3WritingStatus
)
