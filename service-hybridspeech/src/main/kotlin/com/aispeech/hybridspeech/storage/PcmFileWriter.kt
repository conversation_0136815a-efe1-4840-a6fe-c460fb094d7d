package com.aispeech.hybridspeech.storage

import com.aispeech.aibase.AILog
import com.aispeech.hybridspeech.core.CoroutineScopeManager
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.launch
import java.io.BufferedOutputStream
import java.io.File
import java.io.FileInputStream
import java.io.FileOutputStream
import java.util.concurrent.ConcurrentLinkedQueue

/**
 * PCM文件写入器
 * 负责将PCM数据写入文件进行存储
 */
class PcmFileWriter: CoroutineScope {
  companion object {
    private const val TAG = "PcmFileWriter"
    private const val BUFFER_SIZE = 8192
  }

  // 协程作用域委托 - 使用结构化并发
  private val scopeDelegate = CoroutineScopeManager.createModuleScopeDelegate(
    moduleName = TAG,
    parentScope = CoroutineScopeManager.getIOParentScope()
  )

  // 委托CoroutineScope接口到scopeDelegate
  override val coroutineContext = scopeDelegate.coroutineContext

  private var isWriting = false
  private var writingJob: Job? = null
  private val dataQueue = ConcurrentLinkedQueue<ByteArray>()

  private var outputFile: File? = null
  private var fileOutputStream: FileOutputStream? = null
  private var bufferedOutputStream: BufferedOutputStream? = null

  private var totalBytesWritten = 0L
  private var startTime = 0L

  /**
   * 开始写入PCM文件
   */
  fun startWriting(filePath: String): Boolean {
    try {
      if (isWriting) {
        AILog.w(TAG, "Already writing")
        return true
      }

      val file = File(filePath)
      val parentDir = file.parentFile

      // 确保父目录存在
      if (parentDir != null && !parentDir.exists()) {
        parentDir.mkdirs()
      }

      outputFile = file
      fileOutputStream = FileOutputStream(outputFile!!)
      bufferedOutputStream = BufferedOutputStream(fileOutputStream!!, BUFFER_SIZE)

      isWriting = true
      startTime = System.currentTimeMillis()
      totalBytesWritten = 0L

      // 启动写入循环
      writingJob = launch {
        writingLoop()
      }

      AILog.i(TAG, "Started writing PCM to specified path: ${outputFile!!.absolutePath}")
      return true

    } catch (e: Exception) {
      AILog.e(TAG, "Error starting PCM file writing", e)
      return false
    }
  }

  /**
   * 停止写入PCM文件
   */
  fun stopWriting(): String? {
    isWriting = false
    writingJob?.cancel()

    // 写入剩余数据
    flushRemainingData()

    // 关闭文件流
    try {
      bufferedOutputStream?.flush()
      bufferedOutputStream?.close()
      fileOutputStream?.close()
    } catch (e: Exception) {
      AILog.e(TAG, "Error closing file streams", e)
    }

    val filePath = outputFile?.absolutePath
    val duration = System.currentTimeMillis() - startTime

    AILog.i(TAG, "Stopped writing PCM. File: $filePath, Size: $totalBytesWritten bytes, Duration: ${duration}ms")

    // 清理资源
    bufferedOutputStream = null
    fileOutputStream = null
    dataQueue.clear()

    return filePath
  }

  /**
   * 写入PCM数据
   */
  fun writePcmData(data: ByteArray) {
    if (isWriting) {
      dataQueue.offer(data)
    }
  }

  /**
   * 写入循环
   */
  private suspend fun writingLoop() {
    while (isWriting && !Thread.currentThread().isInterrupted) {
      try {
        val data = dataQueue.poll()
        if (data != null) {
          bufferedOutputStream?.write(data)
          totalBytesWritten += data.size

          // 定期刷新缓冲区
          if (totalBytesWritten % (BUFFER_SIZE * 10) == 0L) {
            bufferedOutputStream?.flush()
          }
        } else {
          delay(10) // 等待新数据
        }
      } catch (e: Exception) {
        AILog.e(TAG, "Error in writing loop", e)
        break
      }
    }
  }

  /**
   * 刷新剩余数据
   */
  private fun flushRemainingData() {
    try {
      while (dataQueue.isNotEmpty()) {
        val data = dataQueue.poll()
        if (data != null) {
          bufferedOutputStream?.write(data)
          totalBytesWritten += data.size
        }
      }
      bufferedOutputStream?.flush()
    } catch (e: Exception) {
      AILog.e(TAG, "Error flushing remaining data", e)
    }
  }

  /**
   * 获取写入状态
   */
  fun getWritingStatus(): PcmWritingStatus {
    return PcmWritingStatus(
      isWriting = isWriting,
      filePath = outputFile?.absolutePath ?: "",
      bytesWritten = totalBytesWritten,
      durationMs = if (startTime > 0) System.currentTimeMillis() - startTime else 0L,
      queueSize = dataQueue.size
    )
  }

  /**
   * 读取PCM文件的指定时间段数据
   */
  fun readPcmDataByTimeRange(filePath: String, startTimeMs: Long, endTimeMs: Long): ByteArray? {
    try {
      val file = File(filePath)
      if (!file.exists()) {
        AILog.e(TAG, "PCM file not found: $filePath")
        return null
      }

      // 计算字节偏移量（假设16kHz, 16位, 单声道）
      val sampleRate = 16000
      val bytesPerSample = 2
      val bytesPerMs = sampleRate * bytesPerSample / 1000

      val startOffset = (startTimeMs * bytesPerMs).toLong()
      val endOffset = (endTimeMs * bytesPerMs).toLong()
      val dataSize = (endOffset - startOffset).toInt()

      if (dataSize <= 0 || startOffset >= file.length()) {
        return null
      }

      val actualDataSize = minOf(dataSize, (file.length() - startOffset).toInt())
      val buffer = ByteArray(actualDataSize)

      FileInputStream(file).use { fis ->
        fis.skip(startOffset)
        val bytesRead = fis.read(buffer)
        if (bytesRead > 0) {
          return buffer.copyOf(bytesRead)
        }
      }

    } catch (e: Exception) {
      AILog.e(TAG, "Error reading PCM data by time range", e)
    }

    return null
  }

  /**
   * 从指定偏移量开始读取PCM数据（用于续传）
   */
  fun readPcmDataFromOffset(filePath: String, offsetMs: Long): ByteArray? {
    try {
      val file = File(filePath)
      if (!file.exists()) {
        AILog.e(TAG, "PCM file not found: $filePath")
        return null
      }

      // 计算字节偏移量（假设16kHz, 16位, 单声道）
      val sampleRate = 16000
      val bytesPerSample = 2
      val bytesPerMs = sampleRate * bytesPerSample / 1000

      val startOffset = (offsetMs * bytesPerMs).toLong()

      if (startOffset >= file.length()) {
        AILog.w(TAG, "Offset beyond file length: $offsetMs ms")
        return null
      }

      val remainingBytes = (file.length() - startOffset).toInt()
      val buffer = ByteArray(remainingBytes)

      FileInputStream(file).use { fis ->
        fis.skip(startOffset)
        val bytesRead = fis.read(buffer)
        if (bytesRead > 0) {
          return buffer.copyOf(bytesRead)
        }
      }

    } catch (e: Exception) {
      AILog.e(TAG, "Error reading PCM data from offset", e)
    }

    return null
  }

  /**
   * 创建从指定偏移量开始的PCM数据流（用于续传）
   */
  fun createPcmDataStreamFromOffset(
    filePath: String,
    offsetMs: Long,
    chunkSizeMs: Long = 100L,
    throttleOutput: Boolean = false
  ): Flow<ByteArray>? {
    val file = File(filePath)
    if (!file.exists()) {
      AILog.e(TAG, "PCM file not found for streaming: $filePath")
      return null
    }

    AILog.i(TAG, "Creating PCM data stream: file=$filePath, offsetMs=$offsetMs, chunkSizeMs=$chunkSizeMs, throttleOutput=$throttleOutput")
    if (!throttleOutput) {
      AILog.i(TAG, "Stream configured for aggressive (non-yielding) reading.")
    }

    return flow {
      try {
        // 计算字节偏移量和块大小
        val sampleRate = 16000
        val bytesPerSample = 2 // 16-bit
        val channels = 1 // Mono
        val bytesPerMs = sampleRate * bytesPerSample * channels / 1000

        val startOffsetBytes = (offsetMs * bytesPerMs).toLong()
        val chunkSizeBytes = (chunkSizeMs * bytesPerMs).toInt()

        if (chunkSizeBytes <= 0) {
          AILog.e(TAG, "Invalid chunkSizeBytes ($chunkSizeBytes) calculated from chunkSizeMs ($chunkSizeMs). Aborting stream.")
          return@flow
        }

        AILog.d(TAG, "Calculated stream params: startOffsetBytes=$startOffsetBytes, chunkSizeBytes=$chunkSizeBytes")

        if (startOffsetBytes >= file.length()) {
          AILog.w(TAG, "Start offset ($startOffsetBytes bytes) is beyond file length (${file.length()} bytes). No data to stream from offset $offsetMs ms.")
          return@flow
        }

        var totalBytesRead = 0L
        var chunkNumber = 0
        FileInputStream(file).use { fis ->
          val skippedBytes = fis.skip(startOffsetBytes)
          if (skippedBytes < startOffsetBytes) {
            AILog.w(TAG, "Attempted to skip $startOffsetBytes bytes to reach offset $offsetMs ms, but only skipped $skippedBytes bytes. File might be shorter than expected after offset.")
          } else {
            AILog.d(TAG, "Successfully skipped $skippedBytes bytes to reach offset $offsetMs ms.")
          }


          val buffer = ByteArray(chunkSizeBytes)

          while (true) {
            val readStartTime = System.currentTimeMillis()
            val bytesRead = fis.read(buffer)
            val readDuration = System.currentTimeMillis() - readStartTime

            if (bytesRead <= 0) {
              AILog.i(TAG, "End of file reached or error reading. Bytes read: $bytesRead. Total chunks emitted: $chunkNumber")
              break
            }
            chunkNumber++
            totalBytesRead += bytesRead

            val chunk = if (bytesRead == buffer.size) {
              buffer.copyOf()
            } else {
              buffer.copyOf(bytesRead)
            }

            val emitStartTime = System.currentTimeMillis()
            emit(chunk)
            val emitDuration = System.currentTimeMillis() - emitStartTime
            AILog.v(TAG, "Emitted chunk #$chunkNumber: $bytesRead bytes. Read took ${readDuration}ms, Emit took ${emitDuration}ms.")


            if (throttleOutput) {
              val delayStartTime = System.currentTimeMillis()
              delay(chunkSizeMs) // 控制发送速度
              val delayDuration = System.currentTimeMillis() - delayStartTime
              AILog.v(TAG, "Throttling applied: delayed ${chunkSizeMs}ms (actual: ${delayDuration}ms) after chunk #$chunkNumber.")
            }
          }
        }
        AILog.i(TAG, "PCM data stream from offset completed. Total chunks: $chunkNumber, total bytes read from stream: $totalBytesRead")
      } catch (e: Exception) {
        AILog.e(TAG, "Error creating PCM data stream from offset: file=$filePath, offsetMs=$offsetMs", e)
      }
    }.flowOn(Dispatchers.IO)
  }


  /**
   * 获取当前文件路径（如果正在写入）
   */
  fun getCurrentFilePath(): String? {
    return if (isWriting) outputFile?.absolutePath else null
  }

  /**
   * 获取当前录制时长（毫秒）
   */
  fun getCurrentDurationMs(): Long {
    return if (isWriting && startTime > 0) {
      System.currentTimeMillis() - startTime
    } else {
      0L
    }
  }


  /**
   * 关闭写入器协程作用域
   */
  fun shutdown(reason: String = "PCMFileWriter shutdown") {
    scopeDelegate.shutdown(reason)
    AILog.i(TAG, "PCM file writer scope shut down: $reason")
  }
}

/**
 * PCM写入状态
 */
data class PcmWritingStatus(
  val isWriting: Boolean,
  val filePath: String,
  val bytesWritten: Long,
  val durationMs: Long,
  val queueSize: Int
)
