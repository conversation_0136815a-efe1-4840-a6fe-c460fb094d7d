package com.aispeech.hybridspeech.storage

import com.aispeech.aibase.AILog
import com.aispeech.hybridspeech.Mp3EncodingConfig
import com.aispeech.hybridspeech.core.CoroutineScopeManager
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.launch
import java.io.BufferedOutputStream
import java.io.File
import java.io.FileInputStream
import java.io.FileOutputStream
import java.util.concurrent.ConcurrentLinkedQueue

/**
 * MP3文件写入器
 * 负责将MP3数据写入文件进行存储
 */
class Mp3FileWriter: CoroutineScope {
  companion object {
    private const val TAG = "Mp3FileWriter"
    private const val BUFFER_SIZE = 8192
  }

  // 协程作用域委托 - 使用结构化并发
  private val scopeDelegate = CoroutineScopeManager.createModuleScopeDelegate(
    moduleName = TAG,
    parentScope = CoroutineScopeManager.getIOParentScope()
  )

  // 委托CoroutineScope接口到scopeDelegate
  override val coroutineContext = scopeDelegate.coroutineContext

  private var isWriting = false
  private var writingJob: Job? = null
  private val dataQueue = ConcurrentLinkedQueue<ByteArray>()

  private var outputFile: File? = null
  private var fileOutputStream: FileOutputStream? = null
  private var bufferedOutputStream: BufferedOutputStream? = null

  private var totalBytesWritten = 0L
  private var startTime = 0L

  /**
   * 开始写入MP3文件（支持指定文件路径）
   */
  fun startWritingWithPath(filePath: String, config: Mp3EncodingConfig): Boolean {
    try {
      if (isWriting) {
        AILog.w(TAG, "Already writing")
        return true
      }

      val file = File(filePath)
      val parentDir = file.parentFile

      // 确保父目录存在
      if (parentDir != null && !parentDir.exists()) {
        parentDir.mkdirs()
      }

      outputFile = file
      fileOutputStream = FileOutputStream(outputFile!!)
      bufferedOutputStream = BufferedOutputStream(fileOutputStream!!, BUFFER_SIZE)

      isWriting = true
      startTime = System.currentTimeMillis()
      totalBytesWritten = 0L

      // 启动写入循环
      writingJob = launch {
        writingLoop()
      }

      AILog.i(TAG, "Started writing MP3 to specified path: ${outputFile!!.absolutePath}")
      AILog.i(TAG, "MP3 config: bitRate=${config.bitRate}, quality=${config.quality}")
      return true

    } catch (e: Exception) {
      AILog.e(TAG, "Error starting MP3 file writing with specified path", e)
      return false
    }
  }

  /**
   * 开始写入MP3文件（支持指定文件路径，使用默认配置）
   */
  fun startWriting(filePath: String): Boolean {
    return startWritingWithPath(filePath, Mp3EncodingConfig.createDefault())
  }



  /**
   * 停止写入MP3文件
   */
  fun stopWriting(): String? {
    isWriting = false
    writingJob?.cancel()

    // 写入剩余数据
    flushRemainingData()

    // 关闭文件流
    try {
      bufferedOutputStream?.flush()
      bufferedOutputStream?.close()
      fileOutputStream?.close()
    } catch (e: Exception) {
      AILog.e(TAG, "Error closing file streams", e)
    }

    val filePath = outputFile?.absolutePath
    val duration = System.currentTimeMillis() - startTime

    AILog.i(TAG, "Stopped writing MP3. File: $filePath, Size: $totalBytesWritten bytes, Duration: ${duration}ms")

    // 清理资源
    bufferedOutputStream = null
    fileOutputStream = null
    dataQueue.clear()

    return filePath
  }

  /**
   * 写入MP3数据
   */
  fun writeMp3Data(data: ByteArray) {
    if (isWriting) {
      dataQueue.offer(data)
    }
  }

  /**
   * 写入循环
   */
  private suspend fun writingLoop() {
    while (isWriting && !Thread.currentThread().isInterrupted) {
      try {
        val data = dataQueue.poll()
        if (data != null) {
          bufferedOutputStream?.write(data)
          totalBytesWritten += data.size

          // 定期刷新缓冲区
          if (totalBytesWritten % (BUFFER_SIZE * 10) == 0L) {
            bufferedOutputStream?.flush()
          }
        } else {
          delay(10) // 等待新数据
        }
      } catch (e: Exception) {
        AILog.e(TAG, "Error in writing loop", e)
        break
      }
    }
  }

  /**
   * 刷新剩余数据
   */
  private fun flushRemainingData() {
    try {
      while (dataQueue.isNotEmpty()) {
        val data = dataQueue.poll()
        if (data != null) {
          bufferedOutputStream?.write(data)
          totalBytesWritten += data.size
        }
      }
      bufferedOutputStream?.flush()
    } catch (e: Exception) {
      AILog.e(TAG, "Error flushing remaining data", e)
    }
  }

  /**
   * 获取写入状态
   */
  fun getWritingStatus(): Mp3WritingStatus {
    return Mp3WritingStatus(
      isWriting = isWriting,
      filePath = outputFile?.absolutePath ?: "",
      bytesWritten = totalBytesWritten,
      durationMs = if (startTime > 0) System.currentTimeMillis() - startTime else 0L,
      queueSize = dataQueue.size
    )
  }

  /**
   * 获取当前文件路径
   */
  fun getCurrentFilePath(): String? {
    return outputFile?.absolutePath
  }


  /**
   * 从指定 chunk 索引开始读取 MP3 数据（用于续传）
   * @param filePath MP3 文件路径
   * @param startChunkIndex 起始 chunk 索引
   * @param chunkSizes chunk 大小列表，用于计算偏移量
   * @return MP3 数据，如果读取失败返回null
   */
  fun readMp3DataFromChunkIndex(filePath: String, startChunkIndex: Int, chunkSizes: List<Int>): ByteArray? {
    try {
      val file = File(filePath)
      if (!file.exists()) {
        AILog.w(TAG, "MP3 file does not exist: $filePath")
        return null
      }

      // 计算字节偏移量
      if (startChunkIndex < 0 || startChunkIndex >= chunkSizes.size) {
        AILog.w(TAG, "Invalid chunk index: $startChunkIndex, total chunks: ${chunkSizes.size}")
        return null
      }

      var byteOffset = 0L
      for (i in 0 until startChunkIndex) {
        byteOffset += chunkSizes[i]
      }

      // 计算需要读取的总字节数
      var totalBytesToRead = 0L
      for (i in startChunkIndex until chunkSizes.size) {
        totalBytesToRead += chunkSizes[i]
      }

      if (totalBytesToRead <= 0) {
        AILog.w(TAG, "No data to read from chunk index: $startChunkIndex")
        return null
      }

      // 读取文件数据
      FileInputStream(file).use { fis ->
        fis.skip(byteOffset)
        val buffer = ByteArray(totalBytesToRead.toInt())
        val bytesRead = fis.read(buffer)

        if (bytesRead > 0) {
          AILog.i(TAG, "Read MP3 data from chunk index $startChunkIndex: $bytesRead bytes")
          return if (bytesRead == buffer.size) buffer else buffer.copyOf(bytesRead)
        }
      }

      AILog.w(TAG, "Failed to read MP3 data from file: $filePath")
      return null

    } catch (e: Exception) {
      AILog.e(TAG, "Error reading MP3 data from chunk index", e)
      return null
    }
  }

  /**
   * 创建从指定 chunk 索引开始的 MP3 数据流（用于续传）
   * @param filePath MP3 文件路径
   * @param startChunkIndex 起始 chunk 索引
   * @param chunkSizes chunk 大小列表，用于计算偏移量
   * @param chunkSizeBytes 每次读取的 chunk 大小（字节）
   * @return MP3 数据流，如果创建失败返回null
   */
  fun createMp3DataStreamFromChunkIndex(
    filePath: String,
    startChunkIndex: Int,
    chunkSizes: List<Int>,
    chunkSizeBytes: Int = 1024
  ): Flow<ByteArray>? {
    try {
      val file = File(filePath)
      if (!file.exists()) {
        AILog.w(TAG, "MP3 file does not exist: $filePath")
        return null
      }

      // 计算字节偏移量
      if (startChunkIndex < 0 || startChunkIndex >= chunkSizes.size) {
        AILog.w(TAG, "Invalid chunk index: $startChunkIndex, total chunks: ${chunkSizes.size}")
        return null
      }

      var byteOffset = 0L
      for (i in 0 until startChunkIndex) {
        byteOffset += chunkSizes[i]
      }

      // 计算需要读取的总字节数
      var totalBytesToRead = 0L
      for (i in startChunkIndex until chunkSizes.size) {
        totalBytesToRead += chunkSizes[i]
      }

      if (totalBytesToRead <= 0) {
        AILog.w(TAG, "No data to read from chunk index: $startChunkIndex")
        return null
      }

      AILog.i(TAG, "Creating MP3 data stream from chunk index $startChunkIndex, offset: $byteOffset, total: $totalBytesToRead bytes")

      return flow {
        FileInputStream(file).use { fis ->
          fis.skip(byteOffset)

          val buffer = ByteArray(chunkSizeBytes)
          var remainingBytes = totalBytesToRead

          while (remainingBytes > 0) {
            val bytesToRead = minOf(chunkSizeBytes.toLong(), remainingBytes).toInt()
            val bytesRead = fis.read(buffer, 0, bytesToRead)

            if (bytesRead > 0) {
              emit(buffer.copyOf(bytesRead))
              remainingBytes -= bytesRead
            } else {
              break
            }
          }
        }
      }.flowOn(Dispatchers.IO)

    } catch (e: Exception) {
      AILog.e(TAG, "Error creating MP3 data stream from chunk index", e)
      return null
    }
  }


  /**
   * 关闭写入器协程作用域
   */
  fun shutdown(reason: String = "Mp3FileWriter shutdown") {
    scopeDelegate.shutdown(reason)
    AILog.i(TAG, "MP3 file writer scope shut down: $reason")
  }
}

/**
 * MP3写入状态
 */
data class Mp3WritingStatus(
  val isWriting: Boolean,
  val filePath: String,
  val bytesWritten: Long,
  val durationMs: Long,
  val queueSize: Int
)
