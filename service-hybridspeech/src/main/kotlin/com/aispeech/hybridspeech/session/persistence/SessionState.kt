package com.aispeech.hybridspeech.session.persistence

import com.aispeech.hybridspeech.RecordingConfig
import com.aispeech.hybridspeech.RecordingResultInfo
import com.aispeech.hybridspeech.TranscriptionResult
import kotlinx.serialization.Serializable

/**
 * 会话状态数据类
 * 
 * 用于持久化会话的完整状态，支持崩溃恢复
 */
@Serializable
data class SessionState(
  // 基本信息
  val sessionId: String,
  val status: Int, // ServiceStatus 常量
  val creationTime: Long,
  val lastUpdateTime: Long,
  
  // 配置信息
  val config: RecordingConfig?,
  
  // 运行时状态
  val transcriptionResults: List<TranscriptionResult> = emptyList(),
  val finalResult: RecordingResultInfo? = null,
  val errorMessage: String? = null,
  
  // 恢复相关
  val canRestore: Boolean = true,
  val restoreAttempts: Int = 0,
  val maxRestoreAttempts: Int = 3
) {
  
  /**
   * 检查会话是否已过期
   */
  fun isExpired(timeoutMs: Long = 30 * 60 * 1000L): Boolean {
    return System.currentTimeMillis() - lastUpdateTime > timeoutMs
  }
  
  /**
   * 检查是否可以尝试恢复
   */
  fun canAttemptRestore(): Boolean {
    return canRestore && restoreAttempts < maxRestoreAttempts && !isExpired()
  }
  
  /**
   * 创建恢复尝试的新状态
   */
  fun withRestoreAttempt(): SessionState {
    return copy(
      restoreAttempts = restoreAttempts + 1,
      lastUpdateTime = System.currentTimeMillis()
    )
  }
  
  /**
   * 标记为不可恢复
   */
  fun markAsNonRestorable(): SessionState {
    return copy(
      canRestore = false,
      lastUpdateTime = System.currentTimeMillis()
    )
  }
  
  /**
   * 获取状态摘要
   */
  fun getSummary(): String {
    return "SessionState(id=$sessionId, status=$status, " +
      "created=$creationTime, updated=$lastUpdateTime, " +
      "results=${transcriptionResults.size}, canRestore=$canRestore, " +
      "attempts=$restoreAttempts/$maxRestoreAttempts)"
  }
  
  /**
   * 检查是否为活跃状态
   */
  fun isActive(): Boolean {
    return status == ServiceStatus.RECORDING || 
           status == ServiceStatus.PAUSED || 
           status == ServiceStatus.PROCESSING
  }
  
  /**
   * 检查是否为终止状态
   */
  fun isTerminated(): Boolean {
    return status == ServiceStatus.IDLE || 
           status == ServiceStatus.ERROR ||
           finalResult != null
  }
}

/**
 * 会话恢复信息
 */
@Serializable
data class SessionRecoveryInfo(
  val sessionId: String,
  val lastKnownStatus: Int,
  val crashTime: Long,
  val recoveryAttempts: Int = 0,
  val maxRecoveryAttempts: Int = 3,
  val autoRecoveryEnabled: Boolean = true
) {
  
  /**
   * 检查是否可以自动恢复
   */
  fun canAutoRecover(): Boolean {
    return autoRecoveryEnabled && 
           recoveryAttempts < maxRecoveryAttempts &&
           System.currentTimeMillis() - crashTime < 5 * 60 * 1000L // 5分钟内
  }
  
  /**
   * 创建新的恢复尝试
   */
  fun withRecoveryAttempt(): SessionRecoveryInfo {
    return copy(recoveryAttempts = recoveryAttempts + 1)
  }
}

/**
 * 服务状态常量（复制到这里避免循环依赖）
 */
object ServiceStatus {
  const val IDLE = 0
  const val RECORDING = 1
  const val PROCESSING = 2
  const val ERROR = 3
  const val INITIALIZING = 4
  const val PAUSED = 5
}
