package com.aispeech.hybridspeech.session

import com.aispeech.aibase.AILog
import com.aispeech.hybridspeech.IRecordingSessionCallback
import com.aispeech.hybridspeech.IStartRecordingCallback
import com.aispeech.hybridspeech.IPauseRecordingCallback
import com.aispeech.hybridspeech.IResumeRecordingCallback
import com.aispeech.hybridspeech.IStopRecordingCallback
import com.aispeech.hybridspeech.RecordingResultInfo

/**
 * 会话启动回调处理器
 */
class SessionStartCallbackHandler(
  private val sessionId: String,
  private val clientCallback: IRecordingSessionCallback,
  private val onStatusChanged: (String, Int) -> Unit,
  private val onSessionStarted: () -> Unit,
  private val onSessionFailed: () -> Unit
) : IStartRecordingCallback.Stub() {
  
  private val TAG = "SessionStartCallback-$sessionId"

  override fun onStartRecordingSuccess() {
    try {
      AILog.i(TAG, "Session started successfully")
      onStatusChanged(sessionId, ServiceStatus.RECORDING)
      onSessionStarted()
      // 注意：onSessionCreated 在会话创建时已经调用，这里不需要重复调用
    } catch (e: Exception) {
      AILog.e(TAG, "Error handling start success", e)
    }
  }

  override fun onStartRecordingError(errorCode: Int, message: String?) {
    try {
      AILog.e(TAG, "Session start failed: $message")
      onStatusChanged(sessionId, ServiceStatus.ERROR)
      clientCallback.onError(errorCode, message ?: "Unknown start error")
      onSessionFailed()
    } catch (e: Exception) {
      AILog.e(TAG, "Error handling start error", e)
    }
  }
}

/**
 * 会话暂停回调处理器
 */
class SessionPauseCallbackHandler(
  private val sessionId: String,
  private val clientCallback: IRecordingSessionCallback,
  private val onStatusChanged: (String, Int) -> Unit
) : IPauseRecordingCallback.Stub() {
  
  private val TAG = "SessionPauseCallback-$sessionId"

  override fun onPauseRecordingSuccess() {
    try {
      AILog.i(TAG, "Session paused successfully")
      onStatusChanged(sessionId, ServiceStatus.PAUSED)
      clientCallback.onPaused()
    } catch (e: Exception) {
      AILog.e(TAG, "Error handling pause success", e)
    }
  }

  override fun onPauseRecordingError(errorCode: Int, message: String?) {
    try {
      AILog.e(TAG, "Session pause failed: $message")
      onStatusChanged(sessionId, ServiceStatus.ERROR)
      clientCallback.onError(errorCode, message ?: "Unknown pause error")
    } catch (e: Exception) {
      AILog.e(TAG, "Error handling pause error", e)
    }
  }
}

/**
 * 会话恢复回调处理器
 */
class SessionResumeCallbackHandler(
  private val sessionId: String,
  private val clientCallback: IRecordingSessionCallback,
  private val onStatusChanged: (String, Int) -> Unit
) : IResumeRecordingCallback.Stub() {
  
  private val TAG = "SessionResumeCallback-$sessionId"

  override fun onResumeRecordingSuccess() {
    try {
      AILog.i(TAG, "Session resumed successfully")
      onStatusChanged(sessionId, ServiceStatus.RECORDING)
      clientCallback.onResumed()
    } catch (e: Exception) {
      AILog.e(TAG, "Error handling resume success", e)
    }
  }

  override fun onResumeRecordingError(errorCode: Int, message: String?) {
    try {
      AILog.e(TAG, "Session resume failed: $message")
      onStatusChanged(sessionId, ServiceStatus.ERROR)
      clientCallback.onError(errorCode, message ?: "Unknown resume error")
    } catch (e: Exception) {
      AILog.e(TAG, "Error handling resume error", e)
    }
  }
}

/**
 * 会话停止回调处理器
 */
class SessionStopCallbackHandler(
  private val sessionId: String,
  private val clientCallback: IRecordingSessionCallback,
  private val onStatusChanged: (String, Int) -> Unit,
  private val onSessionStopped: (RecordingResultInfo?) -> Unit
) : IStopRecordingCallback.Stub() {
  
  private val TAG = "SessionStopCallback-$sessionId"

  override fun onStopRecordingSuccess(result: RecordingResultInfo?) {
    try {
      AILog.i(TAG, "Session stopped successfully")
      onStatusChanged(sessionId, ServiceStatus.IDLE)
      clientCallback.onStopped(result)
      onSessionStopped(result)
    } catch (e: Exception) {
      AILog.e(TAG, "Error handling stop success", e)
    }
  }

  override fun onStopRecordingError(errorCode: Int, message: String?) {
    try {
      AILog.e(TAG, "Session stop failed: $message")
      onStatusChanged(sessionId, ServiceStatus.ERROR)
      clientCallback.onError(errorCode, message ?: "Unknown stop error")
      onSessionStopped(null) // 即使失败也要清理会话
    } catch (e: Exception) {
      AILog.e(TAG, "Error handling stop error", e)
    }
  }
}
