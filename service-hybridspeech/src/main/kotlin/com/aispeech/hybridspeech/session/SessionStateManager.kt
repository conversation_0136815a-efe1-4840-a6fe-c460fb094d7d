package com.aispeech.hybridspeech.session

import com.aispeech.aibase.AILog
import com.aispeech.hybridspeech.RecordingConfig
import com.aispeech.hybridspeech.RecordingResultInfo
import com.aispeech.hybridspeech.TranscriptionResult
import com.aispeech.hybridspeech.session.persistence.SessionPersistenceManager
import com.aispeech.hybridspeech.session.persistence.SessionState
import java.util.concurrent.atomic.AtomicReference

/**
 * 会话状态管理器
 * 
 * 职责：
 * 1. 管理单个会话的状态变化
 * 2. 协调状态持久化
 * 3. 提供状态查询接口
 * 4. 处理状态恢复逻辑
 */
class SessionStateManager(
  private val sessionId: String,
  private val persistenceManager: SessionPersistenceManager
) {
  private val TAG = "SessionStateManager-$sessionId"
  
  // 当前状态的原子引用
  private val currentState = AtomicReference<SessionState?>(null)
  
  /**
   * 保存初始状态
   */
  fun saveInitialState(config: RecordingConfig?, creationTime: Long) {
    val initialState = SessionState(
      sessionId = sessionId,
      status = ServiceStatus.IDLE,
      config = config,
      creationTime = creationTime,
      lastUpdateTime = System.currentTimeMillis(),
      transcriptionResults = emptyList(),
      finalResult = null,
      errorMessage = null
    )
    
    currentState.set(initialState)
    persistenceManager.saveSessionState(initialState)
    AILog.d(TAG, "Initial state saved")
  }
  
  /**
   * 更新配置
   */
  fun updateConfig(config: RecordingConfig) {
    updateState { currentState ->
      currentState.copy(
        config = config,
        lastUpdateTime = System.currentTimeMillis()
      )
    }
    AILog.d(TAG, "Config updated")
  }
  
  /**
   * 标记为已启动
   */
  fun markAsStarted() {
    updateState { currentState ->
      currentState.copy(
        status = ServiceStatus.RECORDING,
        lastUpdateTime = System.currentTimeMillis()
      )
    }
    AILog.d(TAG, "Marked as started")
  }
  
  /**
   * 标记为暂停
   */
  fun markAsPaused() {
    updateState { currentState ->
      currentState.copy(
        status = ServiceStatus.PAUSED,
        lastUpdateTime = System.currentTimeMillis()
      )
    }
    AILog.d(TAG, "Marked as paused")
  }
  
  /**
   * 标记为恢复
   */
  fun markAsResumed() {
    updateState { currentState ->
      currentState.copy(
        status = ServiceStatus.RECORDING,
        lastUpdateTime = System.currentTimeMillis()
      )
    }
    AILog.d(TAG, "Marked as resumed")
  }
  
  /**
   * 标记为停止中
   */
  fun markAsStopping() {
    updateState { currentState ->
      currentState.copy(
        status = ServiceStatus.PROCESSING,
        lastUpdateTime = System.currentTimeMillis()
      )
    }
    AILog.d(TAG, "Marked as stopping")
  }
  
  /**
   * 标记为完成
   */
  fun markAsCompleted(result: RecordingResultInfo?) {
    updateState { currentState ->
      currentState.copy(
        status = ServiceStatus.IDLE,
        finalResult = result,
        lastUpdateTime = System.currentTimeMillis()
      )
    }
    AILog.d(TAG, "Marked as completed")
  }
  
  /**
   * 标记为失败
   */
  fun markAsFailed(errorMessage: String? = null) {
    updateState { currentState ->
      currentState.copy(
        status = ServiceStatus.ERROR,
        errorMessage = errorMessage,
        lastUpdateTime = System.currentTimeMillis()
      )
    }
    AILog.d(TAG, "Marked as failed: $errorMessage")
  }
  
  /**
   * 添加转写结果
   */
  fun addTranscriptionResult(result: TranscriptionResult) {
    updateState { currentState ->
      val updatedResults = currentState.transcriptionResults + result
      currentState.copy(
        transcriptionResults = updatedResults,
        lastUpdateTime = System.currentTimeMillis()
      )
    }
    AILog.d(TAG, "Transcription result added")
  }
  
  /**
   * 更新状态的通用方法
   */
  fun updateState(updater: (SessionState) -> SessionState) {
    val oldState = currentState.get()
    if (oldState != null) {
      val newState = updater(oldState)
      currentState.set(newState)
      persistenceManager.saveSessionState(newState)
    } else {
      AILog.w(TAG, "Cannot update null state")
    }
  }
  
  /**
   * 直接更新状态
   */
  fun updateState(newState: SessionState) {
    currentState.set(newState)
    persistenceManager.saveSessionState(newState)
    AILog.d(TAG, "State updated directly")
  }
  
  /**
   * 获取当前状态
   */
  fun getCurrentState(): SessionState? = currentState.get()
  
  /**
   * 恢复状态
   */
  fun restoreState(state: SessionState): Boolean {
    return try {
      currentState.set(state)
      AILog.i(TAG, "State restored: ${state.status}")
      true
    } catch (e: Exception) {
      AILog.e(TAG, "Failed to restore state", e)
      false
    }
  }
  
  /**
   * 检查是否有有效状态
   */
  fun hasValidState(): Boolean {
    val state = currentState.get()
    return state != null && state.status != ServiceStatus.ERROR
  }
  
  /**
   * 清理状态
   */
  fun cleanup() {
    val state = currentState.get()
    if (state != null) {
      // 标记为已清理
      val cleanedState = state.copy(
        status = ServiceStatus.IDLE,
        lastUpdateTime = System.currentTimeMillis()
      )
      persistenceManager.saveSessionState(cleanedState)
      
      // 延迟删除持久化数据（给恢复留一些时间）
      persistenceManager.scheduleCleanup(sessionId, delayMs = 30_000) // 30秒后清理
    }
    
    currentState.set(null)
    AILog.d(TAG, "State cleaned up")
  }
  
  /**
   * 获取状态摘要（用于日志和调试）
   */
  fun getStateSummary(): String {
    val state = currentState.get()
    return if (state != null) {
      "SessionState(id=${state.sessionId}, status=${state.status}, " +
        "created=${state.creationTime}, updated=${state.lastUpdateTime}, " +
        "results=${state.transcriptionResults.size})"
    } else {
      "SessionState(null)"
    }
  }
}
