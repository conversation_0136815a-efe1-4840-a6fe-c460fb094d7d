package com.aispeech.hybridspeech.session.persistence

import android.content.Context
import com.aispeech.aibase.AILog
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.sync.Mutex
import kotlinx.coroutines.sync.withLock
import kotlinx.serialization.encodeToString
import kotlinx.serialization.json.Json
import java.io.File
import java.util.concurrent.ConcurrentHashMap

/**
 * 会话持久化管理器
 * 
 * 职责：
 * 1. 管理会话状态的持久化存储
 * 2. 提供崩溃恢复功能
 * 3. 处理状态清理和过期管理
 * 4. 支持批量操作和性能优化
 */
class SessionPersistenceManager(
  private val context: Context,
  private val scope: CoroutineScope
) {
  private val TAG = "SessionPersistenceManager"
  
  // JSON 序列化器
  private val json = Json {
    prettyPrint = true
    ignoreUnknownKeys = true
  }
  
  // 存储目录
  private val storageDir = File(context.filesDir, "session_states")
  
  // 内存缓存
  private val stateCache = ConcurrentHashMap<String, SessionState>()
  
  // 清理任务
  private val cleanupJobs = ConcurrentHashMap<String, Job>()
  
  // 文件操作锁
  private val fileMutex = Mutex()
  
  init {
    // 确保存储目录存在
    if (!storageDir.exists()) {
      storageDir.mkdirs()
    }
    
    // 启动时加载所有状态到缓存
    loadAllStatesFromDisk()
    
    // 启动定期清理任务
    startPeriodicCleanup()
  }
  
  /**
   * 保存会话状态
   */
  suspend fun saveSessionState(state: SessionState) {
    try {
      // 更新内存缓存
      stateCache[state.sessionId] = state
      
      // 异步写入磁盘
      scope.launch(Dispatchers.IO) {
        saveStateToDisk(state)
      }
      
      AILog.d(TAG, "Session state saved: ${state.sessionId}")
    } catch (e: Exception) {
      AILog.e(TAG, "Failed to save session state: ${state.sessionId}", e)
    }
  }
  
  /**
   * 加载会话状态
   */
  suspend fun loadSessionState(sessionId: String): SessionState? {
    return try {
      // 先从缓存获取
      stateCache[sessionId] ?: loadStateFromDisk(sessionId)?.also { state ->
        stateCache[sessionId] = state
      }
    } catch (e: Exception) {
      AILog.e(TAG, "Failed to load session state: $sessionId", e)
      null
    }
  }
  
  /**
   * 获取所有活跃会话状态
   */
  suspend fun getAllActiveStates(): List<SessionState> {
    return try {
      stateCache.values.filter { it.isActive() }
    } catch (e: Exception) {
      AILog.e(TAG, "Failed to get active states", e)
      emptyList()
    }
  }
  
  /**
   * 获取可恢复的会话状态
   */
  suspend fun getRecoverableStates(): List<SessionState> {
    return try {
      stateCache.values.filter { it.canAttemptRestore() }
    } catch (e: Exception) {
      AILog.e(TAG, "Failed to get recoverable states", e)
      emptyList()
    }
  }
  
  /**
   * 删除会话状态
   */
  suspend fun deleteSessionState(sessionId: String) {
    try {
      // 从缓存移除
      stateCache.remove(sessionId)
      
      // 取消清理任务
      cleanupJobs.remove(sessionId)?.cancel()
      
      // 删除磁盘文件
      scope.launch(Dispatchers.IO) {
        deleteStateFromDisk(sessionId)
      }
      
      AILog.d(TAG, "Session state deleted: $sessionId")
    } catch (e: Exception) {
      AILog.e(TAG, "Failed to delete session state: $sessionId", e)
    }
  }
  
  /**
   * 安排清理任务
   */
  fun scheduleCleanup(sessionId: String, delayMs: Long = 30_000L) {
    // 取消之前的清理任务
    cleanupJobs[sessionId]?.cancel()
    
    // 创建新的清理任务
    val cleanupJob = scope.launch {
      delay(delayMs)
      deleteSessionState(sessionId)
    }
    
    cleanupJobs[sessionId] = cleanupJob
    AILog.d(TAG, "Cleanup scheduled for session: $sessionId in ${delayMs}ms")
  }
  
  /**
   * 清理过期状态
   */
  suspend fun cleanupExpiredStates(timeoutMs: Long = 30 * 60 * 1000L) {
    try {
      val expiredSessions = stateCache.values
        .filter { it.isExpired(timeoutMs) }
        .map { it.sessionId }
      
      expiredSessions.forEach { sessionId ->
        deleteSessionState(sessionId)
      }
      
      if (expiredSessions.isNotEmpty()) {
        AILog.i(TAG, "Cleaned up ${expiredSessions.size} expired sessions")
      }
    } catch (e: Exception) {
      AILog.e(TAG, "Failed to cleanup expired states", e)
    }
  }
  
  /**
   * 从磁盘保存状态
   */
  private suspend fun saveStateToDisk(state: SessionState) {
    fileMutex.withLock {
      try {
        val file = File(storageDir, "${state.sessionId}.json")
        val jsonString = json.encodeToString(state)
        file.writeText(jsonString)
      } catch (e: Exception) {
        AILog.e(TAG, "Failed to save state to disk: ${state.sessionId}", e)
      }
    }
  }
  
  /**
   * 从磁盘加载状态
   */
  private suspend fun loadStateFromDisk(sessionId: String): SessionState? {
    return fileMutex.withLock {
      try {
        val file = File(storageDir, "$sessionId.json")
        if (file.exists()) {
          val jsonString = file.readText()
          json.decodeFromString<SessionState>(jsonString)
        } else {
          null
        }
      } catch (e: Exception) {
        AILog.e(TAG, "Failed to load state from disk: $sessionId", e)
        null
      }
    }
  }
  
  /**
   * 从磁盘删除状态
   */
  private suspend fun deleteStateFromDisk(sessionId: String) {
    fileMutex.withLock {
      try {
        val file = File(storageDir, "$sessionId.json")
        if (file.exists()) {
          file.delete()
        }
      } catch (e: Exception) {
        AILog.e(TAG, "Failed to delete state from disk: $sessionId", e)
      }
    }
  }
  
  /**
   * 加载所有状态到缓存
   */
  private fun loadAllStatesFromDisk() {
    scope.launch(Dispatchers.IO) {
      try {
        storageDir.listFiles()?.forEach { file ->
          if (file.name.endsWith(".json")) {
            val sessionId = file.nameWithoutExtension
            loadStateFromDisk(sessionId)?.let { state ->
              stateCache[sessionId] = state
            }
          }
        }
        AILog.i(TAG, "Loaded ${stateCache.size} session states from disk")
      } catch (e: Exception) {
        AILog.e(TAG, "Failed to load states from disk", e)
      }
    }
  }
  
  /**
   * 启动定期清理任务
   */
  private fun startPeriodicCleanup() {
    scope.launch {
      while (true) {
        delay(5 * 60 * 1000L) // 每5分钟清理一次
        cleanupExpiredStates()
      }
    }
  }
  
  /**
   * 获取统计信息
   */
  fun getStatistics(): SessionPersistenceStatistics {
    val states = stateCache.values
    return SessionPersistenceStatistics(
      totalSessions = states.size,
      activeSessions = states.count { it.isActive() },
      recoverableSessions = states.count { it.canAttemptRestore() },
      expiredSessions = states.count { it.isExpired() }
    )
  }
}

/**
 * 持久化统计信息
 */
data class SessionPersistenceStatistics(
  val totalSessions: Int,
  val activeSessions: Int,
  val recoverableSessions: Int,
  val expiredSessions: Int
)
