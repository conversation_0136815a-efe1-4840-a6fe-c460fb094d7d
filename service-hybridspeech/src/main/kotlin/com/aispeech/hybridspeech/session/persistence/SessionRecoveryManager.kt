package com.aispeech.hybridspeech.session.persistence

import android.content.Context
import com.aispeech.aibase.AILog
import com.aispeech.hybridspeech.session.RecordingSessionImpl
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.launch
import java.util.concurrent.ConcurrentHashMap

/**
 * 会话恢复管理器
 * 
 * 职责：
 * 1. 检测服务崩溃和会话中断
 * 2. 自动恢复可恢复的会话
 * 3. 管理恢复策略和重试逻辑
 * 4. 提供手动恢复接口
 */
class SessionRecoveryManager(
  private val context: Context,
  private val persistenceManager: SessionPersistenceManager,
  private val scope: CoroutineScope,
  private val sessionFactory: (String, SessionState) -> RecordingSessionImpl?
) {
  private val TAG = "SessionRecoveryManager"
  
  // 恢复中的会话
  private val recoveringSessions = ConcurrentHashMap<String, SessionRecoveryInfo>()
  
  /**
   * 启动时检查并恢复会话
   */
  suspend fun performStartupRecovery(): SessionRecoveryResult {
    AILog.i(TAG, "Starting session recovery check...")
    
    val recoverableStates = persistenceManager.getRecoverableStates()
    val recoveryResult = SessionRecoveryResult()
    
    if (recoverableStates.isEmpty()) {
      AILog.i(TAG, "No recoverable sessions found")
      return recoveryResult
    }
    
    AILog.i(TAG, "Found ${recoverableStates.size} recoverable sessions")
    
    for (state in recoverableStates) {
      try {
        val result = attemptSessionRecovery(state)
        when (result) {
          SessionRecoveryStatus.SUCCESS -> {
            recoveryResult.successfulRecoveries++
            AILog.i(TAG, "Successfully recovered session: ${state.sessionId}")
          }
          SessionRecoveryStatus.FAILED -> {
            recoveryResult.failedRecoveries++
            AILog.w(TAG, "Failed to recover session: ${state.sessionId}")
          }
          SessionRecoveryStatus.SKIPPED -> {
            recoveryResult.skippedRecoveries++
            AILog.d(TAG, "Skipped recovery for session: ${state.sessionId}")
          }
        }
      } catch (e: Exception) {
        recoveryResult.failedRecoveries++
        AILog.e(TAG, "Error recovering session: ${state.sessionId}", e)
      }
    }
    
    AILog.i(TAG, "Recovery completed: ${recoveryResult.getSummary()}")
    return recoveryResult
  }
  
  /**
   * 尝试恢复单个会话
   */
  private suspend fun attemptSessionRecovery(state: SessionState): SessionRecoveryStatus {
    val sessionId = state.sessionId
    
    // 检查是否已在恢复中
    if (recoveringSessions.containsKey(sessionId)) {
      AILog.w(TAG, "Session $sessionId is already being recovered")
      return SessionRecoveryStatus.SKIPPED
    }
    
    // 检查是否可以恢复
    if (!state.canAttemptRestore()) {
      AILog.w(TAG, "Session $sessionId cannot be restored")
      markSessionAsNonRecoverable(state)
      return SessionRecoveryStatus.SKIPPED
    }
    
    // 创建恢复信息
    val recoveryInfo = SessionRecoveryInfo(
      sessionId = sessionId,
      lastKnownStatus = state.status,
      crashTime = System.currentTimeMillis(),
      recoveryAttempts = state.restoreAttempts
    )
    
    recoveringSessions[sessionId] = recoveryInfo
    
    return try {
      // 尝试创建新的会话实例
      val sessionImpl = sessionFactory(sessionId, state)
      
      if (sessionImpl != null) {
        // 尝试恢复会话状态
        val restored = sessionImpl.restoreFromState(state)
        
        if (restored) {
          // 更新恢复尝试次数
          val updatedState = state.withRestoreAttempt()
          persistenceManager.saveSessionState(updatedState)
          
          recoveringSessions.remove(sessionId)
          SessionRecoveryStatus.SUCCESS
        } else {
          recoveringSessions.remove(sessionId)
          markSessionAsNonRecoverable(state)
          SessionRecoveryStatus.FAILED
        }
      } else {
        recoveringSessions.remove(sessionId)
        SessionRecoveryStatus.FAILED
      }
    } catch (e: Exception) {
      recoveringSessions.remove(sessionId)
      AILog.e(TAG, "Exception during session recovery: $sessionId", e)
      SessionRecoveryStatus.FAILED
    }
  }
  
  /**
   * 手动恢复指定会话
   */
  suspend fun recoverSession(sessionId: String): SessionRecoveryStatus {
    val state = persistenceManager.loadSessionState(sessionId)
    
    return if (state != null) {
      attemptSessionRecovery(state)
    } else {
      AILog.w(TAG, "No state found for session: $sessionId")
      SessionRecoveryStatus.SKIPPED
    }
  }
  
  /**
   * 获取所有可恢复的会话ID
   */
  suspend fun getRecoverableSessionIds(): List<String> {
    return persistenceManager.getRecoverableStates().map { it.sessionId }
  }
  
  /**
   * 标记会话为不可恢复
   */
  private suspend fun markSessionAsNonRecoverable(state: SessionState) {
    val updatedState = state.markAsNonRestorable()
    persistenceManager.saveSessionState(updatedState)
    
    // 安排清理
    persistenceManager.scheduleCleanup(state.sessionId, delayMs = 60_000L) // 1分钟后清理
  }
  
  /**
   * 清理所有不可恢复的会话
   */
  suspend fun cleanupNonRecoverableSessions() {
    val allStates = persistenceManager.getAllActiveStates()
    val nonRecoverableStates = allStates.filter { !it.canAttemptRestore() }
    
    for (state in nonRecoverableStates) {
      persistenceManager.deleteSessionState(state.sessionId)
    }
    
    if (nonRecoverableStates.isNotEmpty()) {
      AILog.i(TAG, "Cleaned up ${nonRecoverableStates.size} non-recoverable sessions")
    }
  }
  
  /**
   * 获取恢复统计信息
   */
  fun getRecoveryStatistics(): SessionRecoveryStatistics {
    return SessionRecoveryStatistics(
      recoveringSessionsCount = recoveringSessions.size,
      recoveringSessionIds = recoveringSessions.keys.toList()
    )
  }
  
  /**
   * 检查是否有会话正在恢复中
   */
  fun hasRecoveringSession(): Boolean {
    return recoveringSessions.isNotEmpty()
  }
}

/**
 * 会话恢复状态
 */
enum class SessionRecoveryStatus {
  SUCCESS,    // 恢复成功
  FAILED,     // 恢复失败
  SKIPPED     // 跳过恢复
}

/**
 * 会话恢复结果
 */
data class SessionRecoveryResult(
  var successfulRecoveries: Int = 0,
  var failedRecoveries: Int = 0,
  var skippedRecoveries: Int = 0
) {
  fun getTotalAttempts(): Int = successfulRecoveries + failedRecoveries + skippedRecoveries
  
  fun getSummary(): String {
    return "Recovery Result - Success: $successfulRecoveries, " +
      "Failed: $failedRecoveries, Skipped: $skippedRecoveries, " +
      "Total: ${getTotalAttempts()}"
  }
}

/**
 * 会话恢复统计信息
 */
data class SessionRecoveryStatistics(
  val recoveringSessionsCount: Int,
  val recoveringSessionIds: List<String>
)
