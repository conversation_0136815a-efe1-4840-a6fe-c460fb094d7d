package com.aispeech.hybridspeech.debug

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.selection.selectable
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.Button
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.OutlinedButton
import androidx.compose.material3.OutlinedTextField
import androidx.compose.material3.RadioButton
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.window.Dialog
import com.aispeech.hybridspeech.RecordingConfig
import com.aispeech.hybridspeech.TranscriptionResult

/**
 * 录音配置对话框
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun RecordingConfigDialog(
  currentConfig: RecordingConfig,
  onConfigUpdate: (RecordingConfig) -> Unit,
  onDismiss: () -> Unit
) {
  var config by remember { mutableStateOf(currentConfig) }

  Dialog(onDismissRequest = onDismiss) {
    Card(
      modifier = Modifier
        .fillMaxWidth()
        .padding(16.dp),
      elevation = CardDefaults.cardElevation(defaultElevation = 8.dp)
    ) {
      Column(
        modifier = Modifier
          .padding(16.dp)
          .verticalScroll(rememberScrollState())
      ) {
        Text(
          text = "录音配置",
          style = MaterialTheme.typography.headlineSmall,
          fontWeight = FontWeight.Bold
        )

        Spacer(modifier = Modifier.height(16.dp))

        // 模式选择
        Text(
          text = "识别模式",
          style = MaterialTheme.typography.titleMedium,
          fontWeight = FontWeight.Medium
        )

        Spacer(modifier = Modifier.height(8.dp))

        Row(
          modifier = Modifier.fillMaxWidth()
        ) {
          Row(
            modifier = Modifier
              .selectable(
                selected = config.useOnlineMode,
                onClick = { config = config.copy(useOnlineMode = true) }
              )
              .weight(1f),
            verticalAlignment = Alignment.CenterVertically
          ) {
            RadioButton(
              selected = config.useOnlineMode,
              onClick = { config = config.copy(useOnlineMode = true) }
            )
            Text("在线模式")
          }

          Row(
            modifier = Modifier
              .selectable(
                selected = !config.useOnlineMode,
                onClick = { config = config.copy(useOnlineMode = false) }
              )
              .weight(1f),
            verticalAlignment = Alignment.CenterVertically
          ) {
            RadioButton(
              selected = !config.useOnlineMode,
              onClick = { config = config.copy(useOnlineMode = false) }
            )
            Text("离线模式")
          }
        }

        Spacer(modifier = Modifier.height(16.dp))

        // 文件路径配置
        Text(
          text = "文件路径配置",
          style = MaterialTheme.typography.titleMedium,
          fontWeight = FontWeight.Medium
        )

        Spacer(modifier = Modifier.height(8.dp))

        OutlinedTextField(
          value = config.pcmFilePath,
          onValueChange = { config = config.copy(pcmFilePath = it) },
          label = { Text("PCM文件路径") },
          modifier = Modifier.fillMaxWidth()
        )

        Spacer(modifier = Modifier.height(8.dp))

        OutlinedTextField(
          value = config.mp3FilePath,
          onValueChange = { config = config.copy(mp3FilePath = it) },
          label = { Text("MP3文件路径") },
          modifier = Modifier.fillMaxWidth()
        )

        Spacer(modifier = Modifier.height(16.dp))

        // 音频配置
        Text(
          text = "音频配置",
          style = MaterialTheme.typography.titleMedium,
          fontWeight = FontWeight.Medium
        )

        Spacer(modifier = Modifier.height(8.dp))

        Row(
          modifier = Modifier.fillMaxWidth(),
          horizontalArrangement = Arrangement.spacedBy(8.dp)
        ) {
          OutlinedTextField(
            value = config.audioConfig.sampleRate.toString(),
            onValueChange = {
              it.toIntOrNull()?.let { rate ->
                config = config.copy(
                  audioConfig = config.audioConfig.copy(sampleRate = rate)
                )
              }
            },
            label = { Text("采样率") },
            modifier = Modifier.weight(1f)
          )

          OutlinedTextField(
            value = config.audioConfig.channels.toString(),
            onValueChange = {
              it.toIntOrNull()?.let { channels ->
                config = config.copy(
                  audioConfig = config.audioConfig.copy(channels = channels)
                )
              }
            },
            label = { Text("声道数") },
            modifier = Modifier.weight(1f)
          )
        }

        Spacer(modifier = Modifier.height(16.dp))

        // 预设配置
        Text(
          text = "预设配置",
          style = MaterialTheme.typography.titleMedium,
          fontWeight = FontWeight.Medium
        )

        Spacer(modifier = Modifier.height(8.dp))

        Row(
          modifier = Modifier.fillMaxWidth(),
          horizontalArrangement = Arrangement.spacedBy(8.dp)
        ) {
          OutlinedButton(
            onClick = { config = RecordingConfigPresets.DEFAULT_ONLINE },
            modifier = Modifier.weight(1f)
          ) {
            Text("默认在线")
          }

          OutlinedButton(
            onClick = { config = RecordingConfigPresets.DEFAULT_OFFLINE },
            modifier = Modifier.weight(1f)
          ) {
            Text("默认离线")
          }

          OutlinedButton(
            onClick = { config = RecordingConfigPresets.HIGH_QUALITY },
            modifier = Modifier.weight(1f)
          ) {
            Text("高质量")
          }
        }

        Spacer(modifier = Modifier.height(24.dp))

        // 按钮
        Row(
          modifier = Modifier.fillMaxWidth(),
          horizontalArrangement = Arrangement.spacedBy(8.dp)
        ) {
          OutlinedButton(
            onClick = onDismiss,
            modifier = Modifier.weight(1f)
          ) {
            Text("取消")
          }

          Button(
            onClick = {
              onConfigUpdate(config)
              onDismiss()
            },
            modifier = Modifier.weight(1f)
          ) {
            Text("确定")
          }
        }
      }
    }
  }
}

/**
 * 转写结果详情对话框
 */
@Composable
fun TranscriptionResultDetailsDialog(
  result: TranscriptionResult,
  onDismiss: () -> Unit
) {
  Dialog(onDismissRequest = onDismiss) {
    Card(
      modifier = Modifier
        .fillMaxWidth()
        .padding(16.dp),
      elevation = CardDefaults.cardElevation(defaultElevation = 8.dp)
    ) {
      Column(
        modifier = Modifier
          .padding(16.dp)
          .verticalScroll(rememberScrollState())
      ) {
        Text(
          text = "转写结果详情",
          style = MaterialTheme.typography.headlineSmall,
          fontWeight = FontWeight.Bold
        )

        Spacer(modifier = Modifier.height(16.dp))
        Spacer(modifier = Modifier.height(8.dp))

        // 结果类型
        DetailItem("结果类型", getResultTypeName(result))

        // 具体内容根据类型显示
        when (result) {
          is TranscriptionResult.StartResult -> {
            DetailItem("消息", result.message)
            result.sendAllSuccessCount?.let {
              DetailItem("成功发送数", it.toString())
            }
            result.sendAllSuccessLength?.let {
              DetailItem("发送长度", it.toString())
            }
            result.finalTimestamp?.let {
              DetailItem("时间戳", it.toString())
            }
          }

          is TranscriptionResult.IntermediateResult -> {
            DetailItem("来源", if (result.isOnline) "在线" else "离线")
            DetailItem("开始时间", "${result.begin}ms")
            DetailItem("角色", result.role.toString())
            DetailItem("变量内容", result.`var`)
          }

          is TranscriptionResult.ProcessingTextResult -> {
            DetailItem("来源", if (result.isOnline) "在线" else "离线")
            DetailItem("开始时间", "${result.begin}ms")
            DetailItem("结束时间", "${result.end}ms")
            DetailItem("角色", result.role.toString())
            DetailItem("文本内容", result.text)
          }

          is TranscriptionResult.FinalTextResult -> {
            DetailItem("开始时间", "${result.begin}ms")
            DetailItem("结束时间", "${result.end}ms")
            DetailItem("角色", result.role.toString())
            DetailItem("文本内容", result.text ?: "")
          }

          is TranscriptionResult.AgendaResult -> {
            DetailItem("开始时间", "${result.begin}ms")
            DetailItem("结束时间", "${result.end}ms")
            DetailItem("序号", result.seq.toString())
            DetailItem("状态", result.status.toString())
            DetailItem("是否最后", if (result.isLast) "是" else "否")
            DetailItem("时间戳", result.timestamp.toString())
            DetailItem("议程内容", result.text ?: "")
          }

          is TranscriptionResult.InitializationResult -> {

          }
        }

        Spacer(modifier = Modifier.height(16.dp))

        Button(
          onClick = onDismiss,
          modifier = Modifier.fillMaxWidth()
        ) {
          Text("关闭")
        }
      }
    }
  }
}

@Composable
private fun DetailItem(
  label: String,
  value: String
) {
  Row(
    modifier = Modifier
      .fillMaxWidth()
      .padding(vertical = 2.dp),
    horizontalArrangement = Arrangement.SpaceBetween
  ) {
    Text(
      text = "$label:",
      style = MaterialTheme.typography.bodyMedium,
      fontWeight = FontWeight.Medium,
      modifier = Modifier.weight(1f)
    )
    Text(
      text = value,
      style = MaterialTheme.typography.bodyMedium,
      modifier = Modifier.weight(2f)
    )
  }
}

/**
 * 获取结果类型名称
 */
private fun getResultTypeName(result: TranscriptionResult): String {
  return when (result) {
    is TranscriptionResult.StartResult -> "连接建立"
    is TranscriptionResult.IntermediateResult -> if (result.isOnline) "在线中间结果" else "离线中间结果"
    is TranscriptionResult.ProcessingTextResult -> if (result.isOnline) "在线处理结果" else "离线处理结果"
    is TranscriptionResult.FinalTextResult -> "最终文本结果"
    is TranscriptionResult.AgendaResult -> "议程结果"
    is TranscriptionResult.InitializationResult -> "初始化"
  }
}

