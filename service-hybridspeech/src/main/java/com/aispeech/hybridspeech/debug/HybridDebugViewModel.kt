package com.aispeech.hybridspeech.debug

import android.content.ComponentName
import android.content.Context
import android.content.Intent
import android.content.ServiceConnection
import android.os.IBinder
import android.os.RemoteException
import android.util.Log
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.aispeech.hybridspeech.HybridSpeechService
import com.aispeech.hybridspeech.IHybridSpeechService
import com.aispeech.hybridspeech.IStartRecordingCallback
import com.aispeech.hybridspeech.IStopRecordingCallback
import com.aispeech.hybridspeech.ITranscriptionCallback
import com.aispeech.hybridspeech.RecordingConfig
import com.aispeech.hybridspeech.RecordingResultInfo
import com.aispeech.hybridspeech.ServiceStatus
import com.aispeech.hybridspeech.TranscriptionResult
import kotlinx.collections.immutable.persistentListOf
import kotlinx.collections.immutable.toImmutableList
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import java.util.UUID

/**
 * 混合语音服务调试ViewModel
 * 用于测试和调试HybridSpeechService的各种接口
 */
class HybridDebugViewModel(
  private val context: Context
) : ViewModel() {

  companion object {
    private const val TAG = "HybridDebugViewModel"
    private const val MAX_RESULTS = 100
    private const val MAX_LOGS = 200
  }

  private val _uiState = MutableStateFlow(HybridDebugUiState())
  val uiState: StateFlow<HybridDebugUiState> = _uiState.asStateFlow()

  private var hybridSpeechService: IHybridSpeechService? = null
  private var serviceConnection: ServiceConnection? = null

  // AIDL回调实现
  private val transcriptionCallback = object : ITranscriptionCallback.Stub() {
    override fun onTranscriptionResult(result: TranscriptionResult?) {
      result?.let {
        addTranscriptionResult(it)
        addDebugLog(LogLevel.INFO, "Callback", "收到转写结果: ${getResultDisplayText(it)}")
      }
    }

    override fun onError(errorMessage: String?) {
      errorMessage?.let {
        updateRecordingError(it)
        addDebugLog(LogLevel.ERROR, "Callback", "转写错误: $it")
      }
    }

    override fun onStatusChanged(status: Int) {
      updateServiceStatus(status)
      addDebugLog(LogLevel.INFO, "Callback", "状态变化: ${ServiceStatusInfo.fromStatus(status).statusText}")
    }
  }

  init {
    addDebugLog(LogLevel.INFO, TAG, "DebugViewModel初始化完成")
  }

  /**
   * 连接到混合语音服务
   */
  fun connectToService() {
    if (_uiState.value.isServiceConnected || _uiState.value.isServiceBinding) {
      addDebugLog(LogLevel.WARN, TAG, "服务已连接或正在连接中")
      return
    }

    addDebugLog(LogLevel.INFO, TAG, "开始连接服务...")
    _uiState.value = _uiState.value.copy(
      isServiceBinding = true,
      serviceConnectionError = null
    )

    val intent = Intent(context, HybridSpeechService::class.java)
    serviceConnection = object : ServiceConnection {
      override fun onServiceConnected(name: ComponentName?, service: IBinder?) {
        addDebugLog(LogLevel.INFO, TAG, "服务连接成功")
        hybridSpeechService = IHybridSpeechService.Stub.asInterface(service)

        _uiState.value = _uiState.value.copy(
          isServiceConnected = true,
          isServiceBinding = false,
          serviceConnectionError = null
        )

        // 注册回调
        registerCallback()

        // 获取当前状态
        getCurrentStatus()
      }

      override fun onServiceDisconnected(name: ComponentName?) {
        addDebugLog(LogLevel.WARN, TAG, "服务连接断开")
        hybridSpeechService = null
        _uiState.value = _uiState.value.copy(
          isServiceConnected = false,
          isServiceBinding = false
        )
      }
    }

    try {
      val bound = context.bindService(intent, serviceConnection!!, Context.BIND_AUTO_CREATE)
      if (!bound) {
        addDebugLog(LogLevel.ERROR, TAG, "绑定服务失败")
        _uiState.value = _uiState.value.copy(
          isServiceBinding = false,
          serviceConnectionError = "绑定服务失败"
        )
      }
    } catch (e: Exception) {
      addDebugLog(LogLevel.ERROR, TAG, "连接服务异常: ${e.message}")
      _uiState.value = _uiState.value.copy(
        isServiceBinding = false,
        serviceConnectionError = e.message
      )
    }
  }

  /**
   * 断开服务连接
   */
  fun disconnectFromService() {
    addDebugLog(LogLevel.INFO, TAG, "断开服务连接")

    // 取消注册回调
    unregisterCallback()

    serviceConnection?.let {
      try {
        context.unbindService(it)
      } catch (e: Exception) {
        addDebugLog(LogLevel.WARN, TAG, "解绑服务异常: ${e.message}")
      }
    }

    hybridSpeechService = null
    serviceConnection = null

    _uiState.value = _uiState.value.copy(
      isServiceConnected = false,
      isServiceBinding = false,
      isRecording = false,
      currentServiceStatus = ServiceStatus.IDLE
    )
  }

  /**
   * 开始录音
   */
  fun startRecording(config: RecordingConfig = _uiState.value.currentConfig) {
    val service = hybridSpeechService
    if (service == null) {
      addDebugLog(LogLevel.ERROR, TAG, "服务未连接，无法开始录音")
      return
    }

    addDebugLog(LogLevel.INFO, TAG, "开始录音，配置: ${config}")

    try {
      val callback = object : IStartRecordingCallback.Stub() {
        override fun onStartRecordingSuccess() {
          _uiState.value = _uiState.value.copy(
            isRecording = true,
            recordingError = null,
            currentConfig = config
          )
        }

        override fun onStartRecordingError(errorCode: Int, errorMessage: String?) {
          addDebugLog(LogLevel.ERROR, TAG, "录音启动失败")
          updateRecordingError("录音启动失败")
        }

      }
      service.startRecordingWithConfigAsync(config, callback)
    } catch (e: RemoteException) {
      addDebugLog(LogLevel.ERROR, TAG, "启动录音异常: ${e.message}")
      updateRecordingError("启动录音异常: ${e.message}")
    }
  }

  /**
   * 停止录音
   */
  fun stopRecording() {
    val service = hybridSpeechService
    if (service == null) {
      addDebugLog(LogLevel.ERROR, TAG, "服务未连接，无法停止录音")
      return
    }

    addDebugLog(LogLevel.INFO, TAG, "停止录音")

    try {
      // 创建异步停止录音回调
      val stopRecordingCallback = object : IStopRecordingCallback.Stub() {
        override fun onStopRecordingSuccess(result: RecordingResultInfo?) {
          _uiState.value = _uiState.value.copy(
            isRecording = false,
            recordingError = null
          )
          addDebugLog(LogLevel.INFO, TAG, "录音停止成功")

          result?.let {
            addDebugLog(LogLevel.INFO, TAG, "录音结果信息:")
            addDebugLog(LogLevel.INFO, TAG, "  PCM文件: ${it.pcmFilePath}")
            addDebugLog(LogLevel.INFO, TAG, "  MP3文件: ${it.mp3FilePath}")
            addDebugLog(LogLevel.INFO, TAG, "  时长: ${it.durationMs}ms")
            addDebugLog(LogLevel.INFO, TAG, "  文件大小: ${it.fileSizeBytes}字节")
          }
        }

        override fun onStopRecordingError(errorCode: Int, errorMessage: String?) {
          val error = errorMessage ?: "停止录音失败，错误代码: $errorCode"
          updateRecordingError(error)
          addDebugLog(LogLevel.ERROR, TAG, "停止录音失败: $error")
        }
      }

      // 调用异步停止录音
      service.stopRecordingWithResultAsync(stopRecordingCallback)
      addDebugLog(LogLevel.INFO, TAG, "异步停止录音请求已发送")

    } catch (e: RemoteException) {
      addDebugLog(LogLevel.ERROR, TAG, "停止录音异常: ${e.message}")
      updateRecordingError("停止录音异常: ${e.message}")
    }
  }

  /**
   * 获取当前服务状态
   */
  fun getCurrentStatus() {
    val service = hybridSpeechService ?: return

    try {
      val status = service.currentStatus
      updateServiceStatus(status)
      addDebugLog(LogLevel.DEBUG, TAG, "获取状态: ${ServiceStatusInfo.fromStatus(status).statusText}")
    } catch (e: RemoteException) {
      addDebugLog(LogLevel.ERROR, TAG, "获取状态异常: ${e.message}")
    }
  }

  /**
   * 更新录音配置
   */
  fun updateRecordingConfig(config: RecordingConfig) {
    _uiState.value = _uiState.value.copy(
      currentConfig = config,
      configError = null
    )
    addDebugLog(LogLevel.INFO, TAG, "更新录音配置")
  }

  /**
   * 清空转写结果
   */
  fun clearTranscriptionResults() {
    _uiState.value = _uiState.value.copy(transcriptionResults = persistentListOf())
    addDebugLog(LogLevel.INFO, TAG, "清空转写结果")
  }

  /**
   * 清空调试日志
   */
  fun clearDebugLogs() {
    _uiState.value = _uiState.value.copy(debugLogs = persistentListOf())
  }

  /**
   * 显示配置对话框
   */
  fun showConfigDialog() {
    _uiState.value = _uiState.value.copy(showConfigDialog = true)
  }

  /**
   * 隐藏配置对话框
   */
  fun hideConfigDialog() {
    _uiState.value = _uiState.value.copy(showConfigDialog = false)
  }

  /**
   * 显示结果详情
   */
  fun showResultDetails(result: TranscriptionResult) {
    _uiState.value = _uiState.value.copy(
      showResultDetails = true,
      selectedResultForDetails = result
    )
  }

  /**
   * 隐藏结果详情
   */
  fun hideResultDetails() {
    _uiState.value = _uiState.value.copy(
      showResultDetails = false,
      selectedResultForDetails = null
    )
  }

  // 私有辅助方法
  private fun registerCallback() {
    val service = hybridSpeechService ?: return
    try {
      service.registerCallback(transcriptionCallback)
      addDebugLog(LogLevel.DEBUG, TAG, "注册回调成功")
    } catch (e: RemoteException) {
      addDebugLog(LogLevel.ERROR, TAG, "注册回调异常: ${e.message}")
    }
  }

  private fun unregisterCallback() {
    val service = hybridSpeechService ?: return
    try {
      service.unregisterCallback(transcriptionCallback)
      addDebugLog(LogLevel.DEBUG, TAG, "取消注册回调成功")
    } catch (e: RemoteException) {
      addDebugLog(LogLevel.ERROR, TAG, "取消注册回调异常: ${e.message}")
    }
  }

  private fun addTranscriptionResult(result: TranscriptionResult) {
    viewModelScope.launch {
      val resultItem = TranscriptionResultItem(
        id = UUID.randomUUID().toString(),
        timestamp = System.currentTimeMillis(),
        result = result,
        displayText = getResultDisplayText(result),
        resultType = getResultType(result),
        sourceType = ""
      )

      val currentResults = _uiState.value.transcriptionResults
      val newResults = (listOf(resultItem) + currentResults).take(MAX_RESULTS)

      _uiState.value = _uiState.value.copy(
        transcriptionResults = newResults.toImmutableList(),
        lastTranscriptionResult = result
      )
    }
  }

  private fun updateServiceStatus(status: Int) {
    _uiState.value = _uiState.value.copy(
      currentServiceStatus = status,
      isRecording = status == ServiceStatus.RECORDING
    )
  }

  private fun updateRecordingError(error: String) {
    _uiState.value = _uiState.value.copy(recordingError = error)
  }

  private fun addDebugLog(level: LogLevel, tag: String, message: String) {
    viewModelScope.launch {
      val logItem = DebugLogItem(
        id = UUID.randomUUID().toString(),
        timestamp = System.currentTimeMillis(),
        level = level,
        tag = tag,
        message = message
      )

      val currentLogs = _uiState.value.debugLogs
      val newLogs = (listOf(logItem) + currentLogs).take(MAX_LOGS)

      _uiState.value = _uiState.value.copy(debugLogs = newLogs.toImmutableList())

      // 同时输出到系统日志
      when (level) {
        LogLevel.DEBUG -> Log.d(tag, message)
        LogLevel.INFO -> Log.i(tag, message)
        LogLevel.WARN -> Log.w(tag, message)
        LogLevel.ERROR -> Log.e(tag, message)
      }
    }
  }

  private fun getResultDisplayText(result: TranscriptionResult): String {
    return when (result) {
      is TranscriptionResult.StartResult -> {
        val details = mutableListOf<String>()
        details.add("建立连接")
        result.sendAllSuccessCount?.let { details.add("成功发送数: $it") }
        result.sendAllSuccessLength?.let { details.add("发送长度: $it") }
        result.finalTimestamp?.let { details.add("时间戳: $it") }
        if (result.message != "success") {
          details.add("消息: ${result.message}")
        }
        details.joinToString(" | ")
      }

      is TranscriptionResult.IntermediateResult -> {
        val sourceIndicator = if (result.isOnline) "[在线]" else "[离线]"
        val timeInfo = "开始:${result.begin}ms"
        val roleInfo = if (result.role != 0) " 角色:${result.role}" else ""
        "$sourceIndicator 中间结果 ($timeInfo$roleInfo): ${result.`var`}"
      }

      is TranscriptionResult.ProcessingTextResult -> {
        val sourceIndicator = if (result.isOnline) "[在线]" else "[离线]"
        val timeInfo = "${result.begin}-${result.end}ms"
        val roleInfo = if (result.role != 0) " 角色:${result.role}" else ""
        "$sourceIndicator 处理中 ($timeInfo$roleInfo): ${result.text}"
      }

      is TranscriptionResult.FinalTextResult -> {
        val timeInfo = "${result.begin}-${result.end}ms"
        val roleInfo = if (result.role != 0) " 角色:${result.role}" else ""
        "[最终] 确认文本 ($timeInfo$roleInfo): ${result.text}"
      }

      is TranscriptionResult.AgendaResult -> {
        val timeInfo = "${result.begin}-${result.end}ms"
        val statusInfo = "状态:${result.status}"
        val seqInfo = "序号:${result.seq}"
        val lastInfo = if (result.isLast) " [最后]" else ""
        "[议程] ($timeInfo | $statusInfo | $seqInfo$lastInfo): ${result.text}"
      }

      is TranscriptionResult.InitializationResult -> "初始化内容"
    }
  }

  private fun getResultType(result: TranscriptionResult): String {
    return when (result) {
      is TranscriptionResult.StartResult -> "连接建立"
      is TranscriptionResult.IntermediateResult -> if (result.isOnline) "在线中间结果" else "离线中间结果"
      is TranscriptionResult.ProcessingTextResult -> if (result.isOnline) "在线处理结果" else "离线处理结果"
      is TranscriptionResult.FinalTextResult -> "最终文本结果"
      is TranscriptionResult.AgendaResult -> "议程结果"
      is TranscriptionResult.InitializationResult -> "初始化文本"
    }
  }

  override fun onCleared() {
    super.onCleared()
    disconnectFromService()
    addDebugLog(LogLevel.INFO, TAG, "DebugViewModel已清理")
  }
}
