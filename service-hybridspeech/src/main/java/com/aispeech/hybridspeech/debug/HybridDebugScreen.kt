package com.aispeech.hybridspeech.debug

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Clear
import androidx.compose.material.icons.filled.PlayArrow
import androidx.compose.material.icons.filled.Refresh
import androidx.compose.material.icons.filled.Settings
import androidx.compose.material.icons.filled.Stop
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.OutlinedButton
import androidx.compose.material3.Tab
import androidx.compose.material3.TabRow
import androidx.compose.material3.Text
import androidx.compose.material3.TextButton
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import androidx.lifecycle.viewmodel.compose.viewModel
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale

/**
 * 混合语音服务调试主界面
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun HybridDebugScreen(
    modifier: Modifier = Modifier,
    viewModel: HybridDebugViewModel = viewModel()
) {
    val uiState by viewModel.uiState.collectAsStateWithLifecycle()
    
    Column(
        modifier = modifier
            .fillMaxSize()
            .padding(16.dp)
    ) {
        // 标题
        Text(
            text = "混合语音服务调试",
            style = MaterialTheme.typography.headlineMedium,
            fontWeight = FontWeight.Bold,
            modifier = Modifier.padding(bottom = 16.dp)
        )
        
        // 服务连接状态卡片
        ServiceConnectionCard(
            uiState = uiState,
            onConnect = viewModel::connectToService,
            onDisconnect = viewModel::disconnectFromService
        )
        
        Spacer(modifier = Modifier.height(16.dp))
        
        // 录音控制卡片
        RecordingControlCard(
            uiState = uiState,
            onStartRecording = viewModel::startRecording,
            onStopRecording = viewModel::stopRecording,
            onShowConfig = viewModel::showConfigDialog,
            onGetStatus = viewModel::getCurrentStatus
        )
        
        Spacer(modifier = Modifier.height(16.dp))
        
        // 结果和日志标签页
        var selectedTab by remember { mutableStateOf(0) }
        val tabs = listOf("转写结果", "调试日志")
        
        TabRow(selectedTabIndex = selectedTab) {
            tabs.forEachIndexed { index, title ->
                Tab(
                    selected = selectedTab == index,
                    onClick = { selectedTab = index },
                    text = { Text(title) }
                )
            }
        }
        
        when (selectedTab) {
            0 -> TranscriptionResultsTab(
                uiState = uiState,
                onClearResults = viewModel::clearTranscriptionResults,
                onShowDetails = viewModel::showResultDetails
            )
            1 -> DebugLogsTab(
                uiState = uiState,
                onClearLogs = viewModel::clearDebugLogs
            )
        }
    }
    
    // 配置对话框
    if (uiState.showConfigDialog) {
        RecordingConfigDialog(
            currentConfig = uiState.currentConfig,
            onConfigUpdate = viewModel::updateRecordingConfig,
            onDismiss = viewModel::hideConfigDialog
        )
    }
    
    // 结果详情对话框
    if (uiState.showResultDetails && uiState.selectedResultForDetails != null) {
        TranscriptionResultDetailsDialog(
            result = uiState.selectedResultForDetails!!,
            onDismiss = viewModel::hideResultDetails
        )
    }
}

/**
 * 服务连接状态卡片
 */
@Composable
private fun ServiceConnectionCard(
    uiState: HybridDebugUiState,
    onConnect: () -> Unit,
    onDisconnect: () -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = "服务连接",
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.Bold
                )
                
                // 连接状态指示器
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Box(
                        modifier = Modifier
                            .size(12.dp)
                            .background(
                                color = when {
                                    uiState.isServiceConnected -> Color.Green
                                    uiState.isServiceBinding -> Color.Yellow
                                    else -> Color.Red
                                },
                                shape = RoundedCornerShape(6.dp)
                            )
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                    Text(
                        text = when {
                            uiState.isServiceConnected -> "已连接"
                            uiState.isServiceBinding -> "连接中..."
                            else -> "未连接"
                        },
                        style = MaterialTheme.typography.bodyMedium
                    )
                }
            }
            
            Spacer(modifier = Modifier.height(12.dp))
            
            // 连接错误信息
            uiState.serviceConnectionError?.let { error ->
                Text(
                    text = "错误: $error",
                    color = MaterialTheme.colorScheme.error,
                    style = MaterialTheme.typography.bodySmall
                )
                Spacer(modifier = Modifier.height(8.dp))
            }
            
            // 连接控制按钮
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                Button(
                    onClick = onConnect,
                    enabled = !uiState.isServiceConnected && !uiState.isServiceBinding,
                    modifier = Modifier.weight(1f)
                ) {
                    Icon(Icons.Default.PlayArrow, contentDescription = null)
                    Spacer(modifier = Modifier.width(4.dp))
                    Text("连接服务")
                }
                
                Button(
                    onClick = onDisconnect,
                    enabled = uiState.isServiceConnected,
                    modifier = Modifier.weight(1f),
                    colors = ButtonDefaults.buttonColors(
                        containerColor = MaterialTheme.colorScheme.error
                    )
                ) {
                    Icon(Icons.Default.Stop, contentDescription = null)
                    Spacer(modifier = Modifier.width(4.dp))
                    Text("断开连接")
                }
            }
        }
    }
}

/**
 * 录音控制卡片
 */
@Composable
private fun RecordingControlCard(
    uiState: HybridDebugUiState,
    onStartRecording: () -> Unit,
    onStopRecording: () -> Unit,
    onShowConfig: () -> Unit,
    onGetStatus: () -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = "录音控制",
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.Bold
                )
                
                // 服务状态
                val statusInfo = ServiceStatusInfo.fromStatus(uiState.currentServiceStatus)
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Box(
                        modifier = Modifier
                            .size(12.dp)
                            .background(
                                color = statusInfo.statusColor,
                                shape = RoundedCornerShape(6.dp)
                            )
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                    Text(
                        text = statusInfo.statusText,
                        style = MaterialTheme.typography.bodyMedium
                    )
                }
            }
            
            Spacer(modifier = Modifier.height(12.dp))
            
            // 录音模式显示
            Text(
                text = "模式: ${if (uiState.currentConfig.useOnlineMode) "在线" else "离线"}",
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
            
            // 录音错误信息
            uiState.recordingError?.let { error ->
                Spacer(modifier = Modifier.height(8.dp))
                Text(
                    text = "错误: $error",
                    color = MaterialTheme.colorScheme.error,
                    style = MaterialTheme.typography.bodySmall
                )
            }
            
            Spacer(modifier = Modifier.height(12.dp))
            
            // 控制按钮
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                Button(
                    onClick = onStartRecording,
                    enabled = uiState.isServiceConnected && !uiState.isRecording,
                    modifier = Modifier.weight(1f)
                ) {
                    Icon(Icons.Default.PlayArrow, contentDescription = null)
                    Spacer(modifier = Modifier.width(4.dp))
                    Text("开始录音")
                }
                
                Button(
                    onClick = onStopRecording,
                    enabled = uiState.isServiceConnected && uiState.isRecording,
                    modifier = Modifier.weight(1f),
                    colors = ButtonDefaults.buttonColors(
                        containerColor = MaterialTheme.colorScheme.error
                    )
                ) {
                    Icon(Icons.Default.Stop, contentDescription = null)
                    Spacer(modifier = Modifier.width(4.dp))
                    Text("停止录音")
                }
            }
            
            Spacer(modifier = Modifier.height(8.dp))
            
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                OutlinedButton(
                    onClick = onShowConfig,
                    modifier = Modifier.weight(1f)
                ) {
                    Icon(Icons.Default.Settings, contentDescription = null)
                    Spacer(modifier = Modifier.width(4.dp))
                    Text("配置")
                }
                
                OutlinedButton(
                    onClick = onGetStatus,
                    enabled = uiState.isServiceConnected,
                    modifier = Modifier.weight(1f)
                ) {
                    Icon(Icons.Default.Refresh, contentDescription = null)
                    Spacer(modifier = Modifier.width(4.dp))
                    Text("刷新状态")
                }
            }
        }
    }
}

/**
 * 转写结果标签页
 */
@Composable
private fun TranscriptionResultsTab(
    uiState: HybridDebugUiState,
    onClearResults: () -> Unit,
    onShowDetails: (com.aispeech.hybridspeech.TranscriptionResult) -> Unit
) {
    Column(
        modifier = Modifier.fillMaxSize()
    ) {
        // 工具栏
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = 8.dp),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = "结果数量: ${uiState.transcriptionResults.size}",
                style = MaterialTheme.typography.bodyMedium
            )

            TextButton(onClick = onClearResults) {
                Icon(Icons.Default.Clear, contentDescription = null)
                Spacer(modifier = Modifier.width(4.dp))
                Text("清空")
            }
        }

        // 结果列表
        LazyColumn(
            modifier = Modifier.fillMaxSize(),
            verticalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            items(uiState.transcriptionResults) { resultItem ->
                TranscriptionResultItem(
                    resultItem = resultItem,
                    onClick = { onShowDetails(resultItem.result) }
                )
            }
        }
    }
}

/**
 * 调试日志标签页
 */
@Composable
private fun DebugLogsTab(
    uiState: HybridDebugUiState,
    onClearLogs: () -> Unit
) {
    Column(
        modifier = Modifier.fillMaxSize()
    ) {
        // 工具栏
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = 8.dp),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = "日志数量: ${uiState.debugLogs.size}",
                style = MaterialTheme.typography.bodyMedium
            )

            TextButton(onClick = onClearLogs) {
                Icon(Icons.Default.Clear, contentDescription = null)
                Spacer(modifier = Modifier.width(4.dp))
                Text("清空")
            }
        }

        // 日志列表
        LazyColumn(
            modifier = Modifier.fillMaxSize(),
            verticalArrangement = Arrangement.spacedBy(4.dp)
        ) {
            items(uiState.debugLogs) { logItem ->
                DebugLogItem(logItem = logItem)
            }
        }
    }
}

/**
 * 转写结果项
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun TranscriptionResultItem(
    resultItem: TranscriptionResultItem,
    onClick: () -> Unit
) {
    Card(
        onClick = onClick,
        modifier = Modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Column(
            modifier = Modifier.padding(12.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = resultItem.resultType,
                    style = MaterialTheme.typography.labelMedium,
                    color = MaterialTheme.colorScheme.primary
                )

                Text(
                    text = SimpleDateFormat("HH:mm:ss", Locale.getDefault())
                        .format(Date(resultItem.timestamp)),
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }

            Spacer(modifier = Modifier.height(4.dp))

            Text(
                text = resultItem.displayText,
                style = MaterialTheme.typography.bodyMedium,
                maxLines = 2
            )

            Spacer(modifier = Modifier.height(4.dp))

            Text(
                text = "来源: ${resultItem.sourceType}",
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
        }
    }
}

/**
 * 调试日志项
 */
@Composable
private fun DebugLogItem(
    logItem: DebugLogItem
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 1.dp),
        colors = CardDefaults.cardColors(
            containerColor = when (logItem.level) {
                LogLevel.ERROR -> MaterialTheme.colorScheme.errorContainer
                LogLevel.WARN -> MaterialTheme.colorScheme.tertiaryContainer
                LogLevel.INFO -> MaterialTheme.colorScheme.primaryContainer
                LogLevel.DEBUG -> MaterialTheme.colorScheme.surfaceVariant
            }
        )
    ) {
        Column(
            modifier = Modifier.padding(8.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = logItem.level.name,
                        style = MaterialTheme.typography.labelSmall,
                        fontWeight = FontWeight.Bold,
                        color = when (logItem.level) {
                            LogLevel.ERROR -> MaterialTheme.colorScheme.onErrorContainer
                            LogLevel.WARN -> MaterialTheme.colorScheme.onTertiaryContainer
                            LogLevel.INFO -> MaterialTheme.colorScheme.onPrimaryContainer
                            LogLevel.DEBUG -> MaterialTheme.colorScheme.onSurfaceVariant
                        }
                    )

                    Spacer(modifier = Modifier.width(8.dp))

                    Text(
                        text = logItem.tag,
                        style = MaterialTheme.typography.labelSmall,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }

                Text(
                    text = SimpleDateFormat("HH:mm:ss.SSS", Locale.getDefault())
                        .format(Date(logItem.timestamp)),
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }

            Spacer(modifier = Modifier.height(4.dp))

            Text(
                text = logItem.message,
                style = MaterialTheme.typography.bodySmall
            )
        }
    }
}
