package com.aispeech.hybridspeech.debug

import androidx.compose.runtime.Immutable
import com.aispeech.hybridspeech.RecordingConfig
import com.aispeech.hybridspeech.ServiceStatus
import com.aispeech.hybridspeech.TranscriptionResult
import kotlinx.collections.immutable.ImmutableList
import kotlinx.collections.immutable.persistentListOf

/**
 * Debug界面的UI状态
 */
@Immutable
data class HybridDebugUiState(
  // 服务连接状态
  val isServiceConnected: Boolean = false,
  val isServiceBinding: Boolean = false,
  val serviceConnectionError: String? = null,

  // 录音状态
  val isRecording: Boolean = false,
  val currentServiceStatus: Int = ServiceStatus.IDLE,
  val recordingError: String? = null,

  // 转写结果
  val transcriptionResults: ImmutableList<TranscriptionResultItem> = persistentListOf(),
  val lastTranscriptionResult: TranscriptionResult? = null,

  // 配置
  val currentConfig: RecordingConfig = RecordingConfig(),
  val configError: String? = null,

  // 日志
  val debugLogs: ImmutableList<DebugLogItem> = persistentListOf(),

  // UI控制
  val showConfigDialog: Boolean = false,
  val showResultDetails: Boolean = false,
  val selectedResultForDetails: TranscriptionResult? = null
)

/**
 * 转写结果显示项
 */
@Immutable
data class TranscriptionResultItem(
  val id: String,
  val timestamp: Long,
  val result: TranscriptionResult,
  val displayText: String,
  val resultType: String,
  val sourceType: String
)

/**
 * 调试日志项
 */
@Immutable
data class DebugLogItem(
  val id: String,
  val timestamp: Long,
  val level: LogLevel,
  val tag: String,
  val message: String
)

/**
 * 日志级别
 */
enum class LogLevel {
  DEBUG, INFO, WARN, ERROR
}

/**
 * 服务状态显示信息
 */
data class ServiceStatusInfo(
  val status: Int,
  val statusText: String,
  val statusColor: androidx.compose.ui.graphics.Color
) {
  companion object {
    fun fromStatus(status: Int): ServiceStatusInfo {
      return when (status) {
        ServiceStatus.IDLE -> ServiceStatusInfo(
          status = status,
          statusText = "空闲",
          statusColor = androidx.compose.ui.graphics.Color.Gray
        )
        ServiceStatus.RECORDING -> ServiceStatusInfo(
          status = status,
          statusText = "录音中",
          statusColor = androidx.compose.ui.graphics.Color.Green
        )
        ServiceStatus.PROCESSING -> ServiceStatusInfo(
          status = status,
          statusText = "处理中",
          statusColor = androidx.compose.ui.graphics.Color.Blue
        )
        ServiceStatus.ERROR -> ServiceStatusInfo(
          status = status,
          statusText = "错误",
          statusColor = androidx.compose.ui.graphics.Color.Red
        )
        ServiceStatus.INITIALIZING -> ServiceStatusInfo(
          status = status,
          statusText = "初始化中",
          statusColor = androidx.compose.ui.graphics.Color.Yellow
        )
        ServiceStatus.PAUSED -> ServiceStatusInfo(
          status = status,
          statusText = "已暂停",
          statusColor = androidx.compose.ui.graphics.Color.Magenta
        )
        else -> ServiceStatusInfo(
          status = status,
          statusText = "未知($status)",
          statusColor = androidx.compose.ui.graphics.Color.Gray
        )
      }
    }
  }
}

/**
 * 录音配置预设
 */
object RecordingConfigPresets {
  private fun generateTimestamp(): String {
    return java.text.SimpleDateFormat("yyyyMMdd_HHmmss", java.util.Locale.getDefault()).format(java.util.Date())
  }

  val DEFAULT_ONLINE: RecordingConfig
    get() {
      val timestamp = generateTimestamp()
      return RecordingConfig(
        useOnlineMode = true,
        pcmFilePath = "/sdcard/hybrid_speech_debug/online_${timestamp}.pcm",
        mp3FilePath = "/sdcard/hybrid_speech_debug/online_${timestamp}.mp3"
      )
    }

  val DEFAULT_OFFLINE: RecordingConfig
    get() {
      val timestamp = generateTimestamp()
      return RecordingConfig(
        useOnlineMode = false,
        pcmFilePath = "/sdcard/hybrid_speech_debug/offline_${timestamp}.pcm",
        mp3FilePath = "/sdcard/hybrid_speech_debug/offline_${timestamp}.mp3"
      )
    }

  val HIGH_QUALITY: RecordingConfig
    get() {
      val timestamp = generateTimestamp()
      return RecordingConfig(
        useOnlineMode = true,
        pcmFilePath = "/sdcard/hybrid_speech_debug/hq_${timestamp}.pcm",
        mp3FilePath = "/sdcard/hybrid_speech_debug/hq_${timestamp}.mp3",
        audioConfig = com.aispeech.hybridspeech.AudioRecordingConfig(
          sampleRate = 44100,
          channels = 2,
          bitsPerSample = 16
        )
      )
    }
}
