package com.aispeech.hybridspeech.keepalive;

/**
 * 保活状态回调AIDL接口
 * 用于跨进程通知保活状态变化
 */
interface IKeepAliveCallback {
    
    /**
     * 保活状态变化回调
     * @param packageName 包名
     * @param status 新状态：0=UNKNOWN, 1=ACTIVE, 2=INACTIVE, 3=ACTIVATING, 4=FAILED, 5=DISABLED
     * @param strategy 触发状态变化的策略：0=FOREGROUND_SERVICE, 1=PERIODIC_CHECK, 2=BROADCAST_RECEIVER, 3=DOZE_WHITELIST, 4=PACKAGE_ACTIVATION
     */
    void onStatusChanged(String packageName, int status, int strategy);
    
    /**
     * 保活事件回调
     * @param event 事件类型：0=STARTED, 1=STOPPED, 2=PACKAGE_ACTIVATED, 3=PACKAGE_STOPPED, 4=SERVICE_CONNECTED, 5=SERVICE_DISCONNECTED, 6=WHITELIST_ADDED, 7=WHITELIST_REMOVED, 8=CHECK_COMPLETED, 9=ERROR_OCCURRED
     * @param packageName 相关包名
     * @param message 事件消息
     * @param extra 额外数据的JSON字符串
     */
    void onEvent(int event, String packageName, String message, String extra);
    
    /**
     * 保活错误回调
     * @param packageName 包名
     * @param strategy 出错的策略：0=FOREGROUND_SERVICE, 1=PERIODIC_CHECK, 2=BROADCAST_RECEIVER, 3=DOZE_WHITELIST, 4=PACKAGE_ACTIVATION
     * @param error 错误信息
     * @param exception 异常信息（可选）
     */
    void onError(String packageName, int strategy, String error, String exception);
    
    /**
     * 保活统计回调
     * @param statistics 统计数据的JSON字符串
     */
    void onStatistics(String statistics);
}
