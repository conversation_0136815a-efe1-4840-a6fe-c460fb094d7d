package com.aispeech.hybridspeech.keepalive;

import com.aispeech.hybridspeech.keepalive.IKeepAliveCallback;
import com.aispeech.hybridspeech.keepalive.KeepAliveConfig;

/**
 * 保活服务AIDL接口
 * 提供跨进程的保活管理功能
 */
interface IKeepAliveService {
    
    /**
     * 初始化保活服务
     * @param config 保活配置
     * @return 是否初始化成功
     */
    boolean initialize(in KeepAliveConfig config);
    
    /**
     * 启动保活服务
     * @return 是否启动成功
     */
    boolean start();
    
    /**
     * 停止保活服务
     * @return 是否停止成功
     */
    boolean stop();
    
    /**
     * 检查包状态
     * @param packageName 包名，为空则检查所有配置的包
     */
    void checkPackageStatus(String packageName);
    
    /**
     * 手动激活包
     * @param packageName 包名
     * @return 是否激活成功
     */
    boolean activatePackage(String packageName);
    
    /**
     * 获取包状态
     * @param packageName 包名
     * @return 状态值：0=UNKNOWN, 1=ACTIVE, 2=INACTIVE, 3=ACTIVATING, 4=FAILED, 5=DISABLED
     */
    int getPackageStatus(String packageName);
    
    /**
     * 获取所有包状态
     * @return 包名到状态的映射，状态值同上
     */
    Map getAllPackageStatuses();
    
    /**
     * 获取管理器状态
     * @return 状态值：0=UNKNOWN, 1=ACTIVE, 2=INACTIVE, 3=ACTIVATING, 4=FAILED, 5=DISABLED
     */
    int getManagerStatus();
    
    /**
     * 是否正在运行
     * @return 是否运行中
     */
    boolean isRunning();
    
    /**
     * 更新配置
     * @param config 新配置
     * @return 是否更新成功
     */
    boolean updateConfig(in KeepAliveConfig config);
    
    /**
     * 注册状态回调
     * @param callback 回调接口
     */
    void registerCallback(IKeepAliveCallback callback);
    
    /**
     * 注销状态回调
     * @param callback 回调接口
     */
    void unregisterCallback(IKeepAliveCallback callback);
    
    /**
     * 获取统计信息
     * @return 统计信息的JSON字符串
     */
    String getStatistics();
    
    /**
     * 获取版本信息
     * @return 版本字符串
     */
    String getVersion();
}
