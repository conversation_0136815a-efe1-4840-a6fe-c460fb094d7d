# 保活模块使用指南

## 概述

保活模块是一个综合性的Android应用保活解决方案，提供多种策略来确保应用和服务持续运行。该模块采用模块化设计，支持多种保活策略的组合使用。

## 核心组件

### 1. KeepAliveManager
核心管理器，负责协调所有保活策略。

### 2. 保活策略
- **PackageActivationStrategy**: 包激活策略
- **ForegroundServiceStrategy**: 前台服务策略
- **BroadcastReceiverStrategy**: 广播接收器策略
- **PeriodicCheckStrategy**: 定时检查策略
- **DozeWhitelistStrategy**: Doze白名单策略

### 3. 服务接口
- **KeepAliveService**: AIDL服务实现
- **KeepAliveClient**: 客户端访问接口

## 快速开始

### 1. 基本使用

```kotlin
// 获取保活管理器实例
val keepAliveManager = KeepAliveManager.getInstance()

// 创建配置
val config = KeepAliveConfig(
    targetPackages = listOf("com.aispeech.hybridspeech"),
    periodicCheckConfig = PeriodicCheckConfig(
        enabled = true,
        checkInterval = 30000L // 30秒检查一次
    ),
    packageActivationConfig = PackageActivationConfig(
        enabled = true,
        activationMethod = PackageActivationConfig.ActivationMethod.ACTIVITY
    )
)

// 创建回调
val callback = object : KeepAliveCallback {
    override fun onStatusChanged(packageName: String, status: KeepAliveStatus, strategy: KeepAliveStrategy) {
        Log.d("KeepAlive", "Package $packageName status changed to $status")
    }
    
    override fun onEvent(event: KeepAliveEvent, packageName: String, message: String, extra: Map<String, Any>) {
        Log.d("KeepAlive", "Event: $event for $packageName - $message")
    }
    
    override fun onError(packageName: String, strategy: KeepAliveStrategy, error: String, exception: Throwable?) {
        Log.e("KeepAlive", "Error in $strategy for $packageName: $error", exception)
    }
    
    override fun onStatistics(statistics: KeepAliveStatistics) {
        Log.d("KeepAlive", "Statistics: $statistics")
    }
}

// 初始化并启动
lifecycleScope.launch {
    keepAliveManager.initialize(this@MainActivity, config, callback)
    keepAliveManager.start()
}
```

### 2. 使用客户端接口

```kotlin
// 获取客户端实例
val client = KeepAliveClient.getInstance(this)

// 设置回调
client.setCallback(object : KeepAliveClient.KeepAliveClientCallback {
    override fun onStatusChanged(packageName: String, status: KeepAliveStatus, strategy: KeepAliveStrategy) {
        // 处理状态变化
    }
    
    override fun onEvent(event: KeepAliveEvent, packageName: String, message: String, extra: String) {
        // 处理事件
    }
    
    override fun onError(packageName: String, strategy: KeepAliveStrategy, error: String, exception: String?) {
        // 处理错误
    }
    
    override fun onStatistics(statistics: String) {
        // 处理统计信息
    }
})

// 连接并使用服务
lifecycleScope.launch {
    if (client.connect()) {
        // 初始化服务
        val config = KeepAliveConfig(/* 配置参数 */)
        client.initialize(config)
        
        // 启动保活
        client.start()
        
        // 检查状态
        val status = client.getPackageStatus("com.aispeech.hybridspeech")
        Log.d("KeepAlive", "Package status: $status")
        
        // 手动激活包
        val activated = client.activatePackage("com.aispeech.hybridspeech")
        Log.d("KeepAlive", "Package activated: $activated")
    }
}
```

## 配置选项

### 1. 基本配置

```kotlin
val config = KeepAliveConfig(
    targetPackages = listOf("com.aispeech.hybridspeech"),
    enableStatistics = true,
    statisticsInterval = 60000L // 1分钟统计间隔
)
```

### 2. 前台服务配置

```kotlin
val foregroundServiceConfig = ForegroundServiceConfig(
    enabled = true,
    notificationChannelId = "keep_alive_channel",
    notificationId = 1001,
    autoRestart = true,
    restartDelay = 5000L
)
```

### 3. 定时检查配置

```kotlin
val periodicCheckConfig = PeriodicCheckConfig(
    enabled = true,
    checkInterval = 30000L, // 30秒
    maxRetryCount = 3,
    retryDelay = 5000L,
    checkOnScreenOn = true,
    checkOnNetworkChange = true
)
```

### 4. 广播接收器配置

```kotlin
val broadcastReceiverConfig = BroadcastReceiverConfig(
    enabled = true,
    listenBootCompleted = true,
    listenUserPresent = true,
    listenPackageReplaced = true,
    listenPackageAdded = true,
    listenScreenOn = true,
    listenNetworkChange = true
)
```

### 5. Doze白名单配置

```kotlin
val dozeWhitelistConfig = DozeWhitelistConfig(
    enabled = true,
    autoAddToWhitelist = true,
    checkWhitelistStatus = true
)
```

### 6. 包激活配置

```kotlin
val packageActivationConfig = PackageActivationConfig(
    enabled = true,
    activationMethod = PackageActivationConfig.ActivationMethod.COMPREHENSIVE,
    maxActivationRetry = 3,
    activationTimeout = 10000L,
    retryDelay = 2000L,
    verifyActivation = true
)
```

## 高级用法

### 1. 自定义策略组合

```kotlin
val config = KeepAliveConfig(
    targetPackages = listOf("com.aispeech.hybridspeech"),
    // 启用多种策略
    foregroundServiceConfig = ForegroundServiceConfig(enabled = true),
    periodicCheckConfig = PeriodicCheckConfig(enabled = true, checkInterval = 60000L),
    broadcastReceiverConfig = BroadcastReceiverConfig(enabled = true),
    dozeWhitelistConfig = DozeWhitelistConfig(enabled = true),
    packageActivationConfig = PackageActivationConfig(enabled = true)
)
```

### 2. 动态配置更新

```kotlin
// 运行时更新配置
val newConfig = config.copy(
    periodicCheckConfig = config.periodicCheckConfig.copy(
        checkInterval = 120000L // 改为2分钟
    )
)

keepAliveManager.updateConfig(newConfig)
```

### 3. 手动操作

```kotlin
// 手动检查包状态
keepAliveManager.checkPackageStatus("com.aispeech.hybridspeech")

// 手动激活包
val activated = keepAliveManager.activatePackage("com.aispeech.hybridspeech")

// 获取所有包状态
val allStatuses = keepAliveManager.getAllPackageStatuses()

// 获取统计信息
val statistics = keepAliveManager.getStatistics()
```

## 权限要求

在AndroidManifest.xml中添加必要权限：

```xml
<!-- 基本权限 -->
<uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
<uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
<uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />

<!-- Doze白名单权限（可选） -->
<uses-permission android:name="android.permission.REQUEST_IGNORE_BATTERY_OPTIMIZATIONS" />

<!-- 系统级权限（需要系统签名，可选） -->
<uses-permission android:name="android.permission.FORCE_STOP_PACKAGES" />
<uses-permission android:name="android.permission.DEVICE_POWER" />
```

## 服务声明

```xml
<!-- 保活服务 -->
<service
    android:name="com.aispeech.hybridspeech.keepalive.KeepAliveService"
    android:enabled="true"
    android:exported="false" />

<!-- 广播接收器 -->
<receiver
    android:name="com.aispeech.hybridspeech.keepalive.receiver.KeepAliveReceiver"
    android:enabled="true"
    android:exported="false">
    <intent-filter android:priority="1000">
        <action android:name="android.intent.action.BOOT_COMPLETED" />
        <action android:name="android.intent.action.LOCKED_BOOT_COMPLETED" />
        <action android:name="android.intent.action.USER_PRESENT" />
        <action android:name="android.intent.action.MY_PACKAGE_REPLACED" />
        <data android:scheme="package" />
    </intent-filter>
</receiver>
```

## 最佳实践

### 1. 策略选择
- **轻量级应用**: 使用定时检查 + 包激活策略
- **重要服务**: 使用前台服务 + 广播接收器 + Doze白名单
- **系统级应用**: 使用全部策略组合

### 2. 配置优化
- 根据应用重要性调整检查间隔
- 在低电量模式下降低检查频率
- 根据网络状态调整策略

### 3. 错误处理
- 监听错误回调并记录日志
- 实现降级策略
- 定期检查策略有效性

### 4. 性能考虑
- 避免过于频繁的检查
- 合理设置重试次数和延迟
- 监控电池消耗

## 故障排除

### 1. 常见问题
- **服务连接失败**: 检查服务是否正确声明和启动
- **权限不足**: 确保必要权限已申请
- **策略不生效**: 检查配置是否正确，设备是否支持

### 2. 调试方法
- 启用详细日志
- 使用统计信息监控
- 检查回调事件

### 3. 性能监控
- 监控CPU和内存使用
- 检查电池消耗
- 分析策略执行频率

## 示例项目

完整的示例项目请参考 `example/` 目录，包含：
- 基本使用示例
- 高级配置示例
- 自定义策略示例
- 性能监控示例
