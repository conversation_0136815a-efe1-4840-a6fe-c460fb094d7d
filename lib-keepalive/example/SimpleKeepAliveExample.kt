package com.aispeech.keepalive.example

import android.app.Activity
import android.os.Bundle
import android.util.Log
import androidx.lifecycle.lifecycleScope
import com.aispeech.keepalive.*
import kotlinx.coroutines.launch

/**
 * 简单的保活使用示例
 */
class SimpleKeepAliveExample : Activity() {
    
    companion object {
        private const val TAG = "KeepAliveExample"
    }
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        // 示例1: 最简单的使用方式
        simpleUsageExample()
        
        // 示例2: 使用自定义回调
        customCallbackExample()
        
        // 示例3: 使用完整配置
        fullConfigExample()
        
        // 示例4: 手动操作示例
        manualOperationExample()
    }
    
    /**
     * 示例1: 最简单的使用方式
     */
    private fun simpleUsageExample() {
        lifecycleScope.launch {
            // 定义需要保活的包名
            val targetPackages = listOf(
                "com.aispeech.hybridspeech",
                "com.example.myapp"
            )
            
            // 一行代码启动保活功能
            val success = KeepAliveHelper.quickStart(this@SimpleKeepAliveExample, targetPackages)
            
            if (success) {
                Log.i(TAG, "保活功能启动成功")
            } else {
                Log.e(TAG, "保活功能启动失败")
            }
        }
    }
    
    /**
     * 示例2: 使用自定义回调
     */
    private fun customCallbackExample() {
        lifecycleScope.launch {
            val targetPackages = listOf("com.aispeech.hybridspeech")
            
            // 创建自定义回调
            val callback = object : KeepAliveCallback {
                override fun onStatusChanged(packageName: String, status: KeepAliveStatus, strategy: KeepAliveStrategy) {
                    Log.d(TAG, "包 $packageName 状态变为 $status (策略: $strategy)")
                    
                    // 根据状态变化执行相应操作
                    when (status) {
                        KeepAliveStatus.ACTIVE -> {
                            Log.i(TAG, "包 $packageName 已激活")
                        }
                        KeepAliveStatus.INACTIVE -> {
                            Log.w(TAG, "包 $packageName 已停止")
                        }
                        KeepAliveStatus.FAILED -> {
                            Log.e(TAG, "包 $packageName 激活失败")
                        }
                        else -> {
                            Log.d(TAG, "包 $packageName 状态: $status")
                        }
                    }
                }
                
                override fun onEvent(event: KeepAliveEvent, packageName: String, message: String, extra: Map<String, Any>) {
                    Log.d(TAG, "事件: $event, 包: $packageName, 消息: $message")
                    
                    // 处理特定事件
                    when (event) {
                        KeepAliveEvent.PACKAGE_ACTIVATED -> {
                            Log.i(TAG, "成功激活包: $packageName")
                        }
                        KeepAliveEvent.CHECK_COMPLETED -> {
                            Log.d(TAG, "检查完成: $packageName")
                        }
                        KeepAliveEvent.ERROR_OCCURRED -> {
                            Log.e(TAG, "发生错误: $packageName - $message")
                        }
                        else -> {
                            Log.d(TAG, "其他事件: $event")
                        }
                    }
                }
                
                override fun onError(packageName: String, strategy: KeepAliveStrategy, error: String, exception: Throwable?) {
                    Log.e(TAG, "策略 $strategy 对包 $packageName 执行失败: $error", exception)
                }
                
                override fun onStatistics(statistics: KeepAliveStatistics) {
                    Log.d(TAG, "统计信息: 总包数=${statistics.totalPackages}, " +
                            "活跃包数=${statistics.activePackages}, " +
                            "总检查次数=${statistics.totalChecks}, " +
                            "成功激活次数=${statistics.successfulActivations}")
                }
            }
            
            // 使用自定义回调初始化
            val initialized = KeepAliveHelper.initialize(this@SimpleKeepAliveExample, targetPackages, callback)
            if (initialized) {
                KeepAliveHelper.start()
                Log.i(TAG, "使用自定义回调启动保活功能")
            }
        }
    }
    
    /**
     * 示例3: 使用完整配置
     */
    private fun fullConfigExample() {
        lifecycleScope.launch {
            // 创建自定义配置
            val config = KeepAliveConfig(
                targetPackages = listOf("com.aispeech.hybridspeech"),
                
                // 定时检查配置
                periodicCheck = PeriodicCheckConfig(
                    enabled = true,
                    checkInterval = 30000L,     // 30秒检查一次
                    maxRetryCount = 3,          // 最大重试3次
                    retryDelay = 5000L,         // 重试间隔5秒
                    checkOnScreenOn = true,     // 屏幕点亮时检查
                    checkOnNetworkChange = false // 不监听网络变化
                ),
                
                // 包激活配置
                packageActivation = PackageActivationConfig(
                    enabled = true,
                    activationMethod = PackageActivationConfig.ActivationMethod.COMPREHENSIVE, // 综合激活方法
                    maxActivationRetry = 3,     // 最大激活重试3次
                    activationTimeout = 10000L, // 激活超时10秒
                    retryDelay = 2000L,         // 重试间隔2秒
                    verifyActivation = true     // 验证激活结果
                ),
                
                // 广播接收器配置
                broadcastReceiver = BroadcastReceiverConfig(
                    enabled = true,
                    listenBootCompleted = true,    // 监听开机完成
                    listenUserPresent = true,      // 监听用户解锁
                    listenPackageReplaced = true,  // 监听包替换
                    listenScreenOn = false,        // 不监听屏幕点亮
                    listenNetworkChange = false    // 不监听网络变化
                ),
                
                // Doze白名单配置（需要系统权限）
                dozeWhitelist = DozeWhitelistConfig(
                    enabled = false,              // 默认关闭
                    autoAddToWhitelist = false,   // 不自动添加到白名单
                    checkWhitelistStatus = true   // 检查白名单状态
                ),
                
                // 统计配置
                enableStatistics = true,
                statisticsInterval = 60000L       // 1分钟统计间隔
            )
            
            // 使用管理器进行完整配置
            val manager = KeepAliveManager.getInstance()
            val callback = KeepAliveHelper.createSimpleCallback()
            
            val initialized = manager.initialize(this@SimpleKeepAliveExample, config, callback)
            if (initialized) {
                manager.start()
                Log.i(TAG, "使用完整配置启动保活功能")
            }
        }
    }
    
    /**
     * 示例4: 手动操作示例
     */
    private fun manualOperationExample() {
        lifecycleScope.launch {
            // 先启动保活功能
            val targetPackages = listOf("com.aispeech.hybridspeech", "com.example.app")
            KeepAliveHelper.quickStart(this@SimpleKeepAliveExample, targetPackages)
            
            // 等待一段时间让保活功能初始化
            kotlinx.coroutines.delay(2000)
            
            // 手动检查单个包状态
            val packageName = "com.aispeech.hybridspeech"
            val status = KeepAliveHelper.checkPackageStatus(packageName)
            Log.d(TAG, "包 $packageName 当前状态: $status")
            
            // 如果包处于非活跃状态，手动激活
            if (status == KeepAliveStatus.INACTIVE) {
                Log.i(TAG, "尝试激活包: $packageName")
                val activated = KeepAliveHelper.activatePackage(packageName)
                Log.i(TAG, "激活结果: $activated")
            }
            
            // 获取所有包的状态
            val allStatuses = KeepAliveHelper.getAllPackageStatuses()
            Log.d(TAG, "所有包状态:")
            allStatuses.forEach { (pkg, stat) ->
                Log.d(TAG, "  $pkg: $stat")
            }
            
            // 批量激活所有非活跃的包
            val activationResults = KeepAliveHelper.activateAllPackages()
            Log.d(TAG, "批量激活结果:")
            activationResults.forEach { (pkg, result) ->
                Log.d(TAG, "  $pkg: $result")
            }
            
            // 获取统计信息
            val statistics = KeepAliveHelper.getStatistics()
            statistics?.let { stats ->
                Log.d(TAG, "保活统计信息:")
                Log.d(TAG, "  总包数: ${stats.totalPackages}")
                Log.d(TAG, "  活跃包数: ${stats.activePackages}")
                Log.d(TAG, "  非活跃包数: ${stats.inactivePackages}")
                Log.d(TAG, "  总检查次数: ${stats.totalChecks}")
                Log.d(TAG, "  成功激活次数: ${stats.successfulActivations}")
                Log.d(TAG, "  失败激活次数: ${stats.failedActivations}")
                Log.d(TAG, "  运行时间: ${stats.uptime}ms")
            }
        }
    }
    
    override fun onDestroy() {
        super.onDestroy()
        
        // 释放保活资源
        lifecycleScope.launch {
            KeepAliveHelper.release()
            Log.i(TAG, "保活资源已释放")
        }
    }
}
