package com.aispeech.keepalive

/**
 * 保活回调接口
 */
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.StateFlow

/**
 * Flow-based KeepAlive events interface
 */
interface KeepAliveEvents {
    val events: Flow<KeepAliveEvent>
    val packageStatuses: StateFlow<Map<String, KeepAliveStatus>>
}

/**
 * Legacy callback interface for backward compatibility
 */
interface KeepAliveCallback {
    /**
     * 状态变化回调
     * @param packageName 包名
     * @param status 新状态
     * @param strategy 触发状态变化的策略
     */
    fun onStatusChanged(packageName: String, status: KeepAliveStatus, strategy: KeepAliveStrategy) {}
    
    /**
     * 事件回调
     * @param event 事件类型
     * @param packageName 相关包名
     * @param message 事件消息
     * @param extra 额外数据
     */
    fun onEvent(event: KeepAliveEvent, packageName: String, message: String, extra: Map<String, Any> = emptyMap()) {}
    
    /**
     * 错误回调
     * @param packageName 包名
     * @param strategy 出错的策略
     * @param error 错误信息
     * @param exception 异常（可选）
     */
    fun onError(packageName: String, strategy: KeepAliveStrategy, error: String, exception: Throwable? = null) {}
    
    /**
     * 统计回调
     * @param statistics 统计数据
     */
    fun onStatistics(statistics: KeepAliveStatistics) {}
}

/**
 * 保活统计数据
 */
data class KeepAliveStatistics(
    val timestamp: Long = System.currentTimeMillis(),
    val totalPackages: Int = 0,
    val activePackages: Int = 0,
    val inactivePackages: Int = 0,
    val totalChecks: Long = 0,
    val successfulActivations: Long = 0,
    val failedActivations: Long = 0,
    val averageCheckTime: Long = 0,
    val uptime: Long = 0,
    val strategies: Map<KeepAliveStrategy, StrategyStatistics> = emptyMap()
)

/**
 * 策略统计数据
 */
data class StrategyStatistics(
    val enabled: Boolean = false,
    val totalExecutions: Long = 0,
    val successfulExecutions: Long = 0,
    val failedExecutions: Long = 0,
    val averageExecutionTime: Long = 0,
    val lastExecutionTime: Long = 0
)
