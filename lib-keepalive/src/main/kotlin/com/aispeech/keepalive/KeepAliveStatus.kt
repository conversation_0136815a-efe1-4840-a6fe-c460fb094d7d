package com.aispeech.keepalive

/**
 * 保活状态封装类
 */
sealed class KeepAliveStatus {
    object Unknown : KeepAliveStatus()    // 未知状态
    object Active : KeepAliveStatus()     // 活跃状态
    object Inactive : KeepAliveStatus()   // 非活跃状态
    object Activating : KeepAliveStatus() // 激活中
    object Failed : KeepAliveStatus()     // 失败状态
    object Disabled : KeepAliveStatus()   // 已禁用
    
    // 为了兼容性保留的枚举值
    @Deprecated("Use sealed class objects instead", ReplaceWith("KeepAliveStatus.Unknown"))
    companion object {
        @JvmField val UNKNOWN = Unknown
        @JvmField val ACTIVE = Active
        @JvmField val INACTIVE = Inactive
        @JvmField val ACTIVATING = Activating
        @JvmField val FAILED = Failed
        @JvmField val DISABLED = Disabled
    }
}

/**
 * 保活事件封装类
 */
sealed interface KeepAliveEvent {
    data class Started(val packageName: String) : KeepAliveEvent
    data class Stopped(val packageName: String) : KeepAliveEvent
    data class PackageActivated(val packageName: String) : KeepAliveEvent
    data class PackageStopped(val packageName: String) : KeepAliveEvent
    data class CheckCompleted(val packageName: String) : KeepAliveEvent
    data class WhitelistAdded(val packageName: String) : KeepAliveEvent
    data class WhitelistRemoved(val packageName: String) : KeepAliveEvent
    data class ErrorOccurred(val packageName: String, val error: String) : KeepAliveEvent
    
    // 为了兼容性保留的常量（使用伴生对象字段）
    companion object {
        @JvmField val STARTED = "STARTED"
        @JvmField val STOPPED = "STOPPED"
        @JvmField val PACKAGE_ACTIVATED = "PACKAGE_ACTIVATED"
        @JvmField val PACKAGE_STOPPED = "PACKAGE_STOPPED"
        @JvmField val CHECK_COMPLETED = "CHECK_COMPLETED"
        @JvmField val WHITELIST_ADDED = "WHITELIST_ADDED"
        @JvmField val WHITELIST_REMOVED = "WHITELIST_REMOVED"
        @JvmField val ERROR_OCCURRED = "ERROR_OCCURRED"
    }
}

/**
 * 保活策略封装类
 */
sealed class KeepAliveStrategy {
    object PackageActivation : KeepAliveStrategy()   // 包激活策略
    object PeriodicCheck : KeepAliveStrategy()       // 定时检查策略
    object BroadcastReceiver : KeepAliveStrategy()   // 广播接收器策略
    object DozeWhitelist : KeepAliveStrategy()       // Doze白名单策略
    
    // 为了兼容性保留的枚举值
    @Deprecated("Use sealed class objects instead", ReplaceWith("KeepAliveStrategy.PackageActivation"))
    companion object {
        @JvmField val PACKAGE_ACTIVATION = PackageActivation
        @JvmField val PERIODIC_CHECK = PeriodicCheck
        @JvmField val BROADCAST_RECEIVER = BroadcastReceiver
        @JvmField val DOZE_WHITELIST = DozeWhitelist
    }
}
