package com.aispeech.keepalive

import android.os.Parcelable
import kotlinx.parcelize.Parcelize
import kotlinx.serialization.Serializable

/**
 * 保活配置
 */
@Parcelize
@Serializable
data class KeepAliveConfig(
    // 目标包名列表
    val targetPackages: List<String> = emptyList(),
    
    // 定时检查配置
    val periodicCheck: PeriodicCheckConfig = PeriodicCheckConfig(),
    
    // 包激活配置
    val packageActivation: PackageActivationConfig = PackageActivationConfig(),
    
    // Doze白名单配置
    val dozeWhitelist: DozeWhitelistConfig = DozeWhitelistConfig(),
    
    // 广播监听配置
    val broadcastReceiver: BroadcastReceiverConfig = BroadcastReceiverConfig(),
    
    // 统计配置
    val enableStatistics: Boolean = true,
    val statisticsInterval: Long = 60000L // 1分钟
) : Parcelable

/**
 * 定时检查配置
 */
@Parcelize
@Serializable
data class PeriodicCheckConfig(
    val enabled: Boolean = true,
    val checkInterval: Long = 30000L, // 30秒
    val maxRetryCount: Int = 3,
    val retryDelay: Long = 5000L,
    val checkOnScreenOn: Boolean = true,
    val checkOnNetworkChange: Boolean = false
) : Parcelable

/**
 * 包激活配置
 */
@Parcelize
@Serializable
data class PackageActivationConfig(
    val enabled: Boolean = true,
    val activationMethod: ActivationMethod = ActivationMethod.ACTIVITY,
    val maxActivationRetry: Int = 3,
    val activationTimeout: Long = 10000L,
    val retryDelay: Long = 2000L,
    val verifyActivation: Boolean = true
) : Parcelable {
    
    @Serializable
    enum class ActivationMethod {
        ACTIVITY,      // 通过启动Activity激活
        SHELL,         // 通过Shell命令激活
        COMPREHENSIVE  // 综合方法
    }
}

/**
 * Doze白名单配置
 */
@Parcelize
@Serializable
data class DozeWhitelistConfig(
    val enabled: Boolean = false, // 默认关闭，需要系统权限
    val autoAddToWhitelist: Boolean = false,
    val checkWhitelistStatus: Boolean = true
) : Parcelable

/**
 * 广播接收器配置
 */
@Parcelize
@Serializable
data class BroadcastReceiverConfig(
    val enabled: Boolean = true,
    val listenBootCompleted: Boolean = true,
    val listenUserPresent: Boolean = true,
    val listenPackageReplaced: Boolean = true,
    val listenScreenOn: Boolean = false,
    val listenNetworkChange: Boolean = false
) : Parcelable


