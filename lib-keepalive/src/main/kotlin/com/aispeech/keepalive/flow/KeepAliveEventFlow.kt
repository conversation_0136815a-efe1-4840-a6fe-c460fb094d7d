package com.aispeech.keepalive.flow

import com.aispeech.keepalive.*
import kotlinx.coroutines.channels.Channel
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.receiveAsFlow

/**
 * Flow-based event system for KeepAlive
 * Provides reactive streams for different types of events
 */
class KeepAliveEventFlow {

    // Event channels
    private val statusChangeChannel = Channel<StatusChangeEvent>(Channel.BUFFERED)
    private val keepAliveEventChannel = Channel<KeepAliveEventData>(Channel.BUFFERED)
    private val errorChannel = Channel<ErrorEvent>(Channel.BUFFERED)
    private val statisticsChannel = Channel<KeepAliveStatistics>(Channel.BUFFERED)

    // Public flows
    val statusChanges: Flow<StatusChangeEvent> = statusChangeChannel.receiveAsFlow()
    val events: Flow<KeepAliveEventData> = keepAliveEventChannel.receiveAsFlow()
    val errors: Flow<ErrorEvent> = errorChannel.receiveAsFlow()
    val statistics: Flow<KeepAliveStatistics> = statisticsChannel.receiveAsFlow()

    /**
     * Emit a status change event
     */
    fun emitStatusChange(packageName: String, status: KeepAliveStatus, strategy: KeepAliveStrategy) {
        statusChangeChannel.trySend(StatusChangeEvent(packageName, status, strategy))
    }

    /**
     * Emit a keep-alive event
     */
    fun emitEvent(event: KeepAliveEvent, packageName: String, message: String, extra: Map<String, Any> = emptyMap()) {
        keepAliveEventChannel.trySend(KeepAliveEventData(event, packageName, message, extra))
    }

    /**
     * Emit an error event
     */
    fun emitError(packageName: String, strategy: KeepAliveStrategy, error: String, exception: Throwable? = null) {
        errorChannel.trySend(ErrorEvent(packageName, strategy, error, exception))
    }

    /**
     * Emit statistics
     */
    fun emitStatistics(statistics: KeepAliveStatistics) {
        statisticsChannel.trySend(statistics)
    }

    /**
     * Close all channels
     */
    fun close() {
        statusChangeChannel.close()
        keepAliveEventChannel.close()
        errorChannel.close()
        statisticsChannel.close()
    }
}

/**
 * Status change event data class
 */
data class StatusChangeEvent(
    val packageName: String,
    val status: KeepAliveStatus,
    val strategy: KeepAliveStrategy,
    val timestamp: Long = System.currentTimeMillis()
)

/**
 * KeepAlive event data class
 */
data class KeepAliveEventData(
    val event: KeepAliveEvent,
    val packageName: String,
    val message: String,
    val extra: Map<String, Any> = emptyMap(),
    val timestamp: Long = System.currentTimeMillis()
)

/**
 * Error event data class
 */
data class ErrorEvent(
    val packageName: String,
    val strategy: KeepAliveStrategy,
    val error: String,
    val exception: Throwable? = null,
    val timestamp: Long = System.currentTimeMillis()
)

import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

/**
 * Provides an adapter to convert Flow to KeepAliveCallback
 */
fun KeepAliveEvents.asCallback(callback: KeepAliveCallback) {
    CoroutineScope(Dispatchers.Default).launch {
        events.collect { event ->
            when (event) {
                is KeepAliveEvent.Started -> callback.onEvent(event, event.packageName, "Started")
                is KeepAliveEvent.Stopped -> callback.onEvent(event, event.packageName, "Stopped")
                is KeepAliveEvent.PackageActivated -> callback.onEvent(event, event.packageName, "Package Activated")
                is KeepAliveEvent.PackageStopped -> callback.onEvent(event, event.packageName, "Package Stopped")
                is KeepAliveEvent.CheckCompleted -> callback.onEvent(event, event.packageName, "Check Completed")
                is KeepAliveEvent.WhitelistAdded -> callback.onEvent(event, event.packageName, "Whitelist Added")
                is KeepAliveEvent.WhitelistRemoved -> callback.onEvent(event, event.packageName, "Whitelist Removed")
                is KeepAliveEvent.ErrorOccurred -> callback.onError(event.packageName, KeepAliveStrategy.PackageActivation, event.error)
            }
        }
    }
}

/**
 * Extension functions for easier usage
 */

/**
 * Collect status changes for a specific package
 */
fun KeepAliveEventFlow.statusChangesFor(packageName: String): Flow<StatusChangeEvent> {
    return statusChanges.kotlinx.coroutines.flow.filter { it.packageName == packageName }
}

/**
 * Collect events of a specific type
 */
fun KeepAliveEventFlow.eventsOfType(eventType: KeepAliveEvent): Flow<KeepAliveEventData> {
    return events.kotlinx.coroutines.flow.filter { it.event == eventType }
}

/**
 * Collect errors from a specific strategy
 */
fun KeepAliveEventFlow.errorsFrom(strategy: KeepAliveStrategy): Flow<ErrorEvent> {
    return errors.kotlinx.coroutines.flow.filter { it.strategy == strategy }
}
