package com.aispeech.keepalive

import android.content.Context
import com.aispeech.aibase.AILog
import com.aispeech.keepalive.strategy.*
import kotlinx.coroutines.*
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.Flow
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.atomic.AtomicBoolean
import java.util.concurrent.atomic.AtomicLong

/**
 * 简化的保活管理器
 * 提供基本的保活功能，不依赖AIDL服务
 */
class KeepAliveManager private constructor() : KeepAliveEvents {
    
    companion object {
        private const val TAG = "KeepAliveManager"
        
        @Volatile
        private var INSTANCE: KeepAliveManager? = null
        
        fun getInstance(): KeepAliveManager {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: KeepAliveManager().also { INSTANCE = it }
            }
        }
    }
    
    // 状态管理
    private val _managerStatus = MutableStateFlow(KeepAliveStatus.UNKNOWN)
val managerStatus: StateFlow<KeepAliveStatus> = _managerStatus.asStateFlow()
    
    private val _packageStatuses = MutableStateFlow<Map<String, KeepAliveStatus>>(emptyMap())
    override val packageStatuses: StateFlow<Map<String, KeepAliveStatus>> = _packageStatuses.asStateFlow()
    
    // 配置和回调
    private var context: Context? = null
    private var config: KeepAliveConfig = KeepAliveConfig()
    private val _keepAliveEvents = MutableSharedFlow<KeepAliveEvent>()
    override val events: Flow<KeepAliveEvent> = _keepAliveEvents
    
    // 运行状态
    private val isInitialized = AtomicBoolean(false)
    private val isRunning = AtomicBoolean(false)
    private val startTime = AtomicLong(0)
    
    // 协程管理
    private var managerScope: CoroutineScope? = null
    
    // 策略管理
    private val strategies = mutableMapOf<KeepAliveStrategy, BaseKeepAliveStrategy>()
    private val packageStatusMap = ConcurrentHashMap<String, KeepAliveStatus>()
    
    // 统计数据
    private val totalChecks = AtomicLong(0)
    private val successfulActivations = AtomicLong(0)
    private val failedActivations = AtomicLong(0)
    
    /**
     * 初始化保活管理器
     */
    private var callback: KeepAliveCallback? = null
    
    suspend fun initialize(context: Context, config: KeepAliveConfig, callback: KeepAliveCallback? = null): Boolean {
        return withContext(Dispatchers.Default) {
            try {
                if (isInitialized.get()) {
                    AILog.w(TAG, "KeepAliveManager already initialized")
                    return@withContext true
                }
                
                <EMAIL> = context.applicationContext
                <EMAIL> = config
                <EMAIL> = callback
                
                // 创建协程作用域
                managerScope = CoroutineScope(
                    SupervisorJob() + Dispatchers.Default + CoroutineName("KeepAliveManager")
                )
                
                // 初始化策略
                initializeStrategies()
                
                // 初始化包状态
                config.targetPackages.forEach { packageName ->
                    packageStatusMap[packageName] = KeepAliveStatus.UNKNOWN
                }
                updatePackageStatusFlow()
                
                _managerStatus.value = KeepAliveStatus.INACTIVE
                isInitialized.set(true)
                
                AILog.i(TAG, "KeepAliveManager initialized successfully with ${config.targetPackages.size} packages")
                true
                
            } catch (e: Exception) {
                AILog.e(TAG, "Failed to initialize KeepAliveManager", e)
                false
            }
        }
    }
    
    /**
     * 启动保活管理器
     */
    suspend fun start() {
        if (!isInitialized.get()) {
            AILog.w(TAG, "KeepAliveManager not initialized")
            return
        }
        
        if (isRunning.get()) {
            AILog.w(TAG, "KeepAliveManager already running")
            return
        }
        
        try {
            startTime.set(System.currentTimeMillis())
            isRunning.set(true)
            _managerStatus.value = KeepAliveStatus.ACTIVE
            
            // 启动所有策略
            strategies.values.forEach { strategy ->
                if (strategy.isEnabled) {
                    strategy.start(config.targetPackages)
                }
            }
            
            // 启动统计任务
            if (config.enableStatistics) {
                startStatisticsTask()
            }
            
            AILog.i(TAG, "KeepAliveManager started")
_keepAliveEvents.tryEmit(KeepAliveEvent.Started(packageName = "")) // Assuming packageName here
            
        } catch (e: Exception) {
            AILog.e(TAG, "Failed to start KeepAliveManager", e)
            isRunning.set(false)
            _managerStatus.value = KeepAliveStatus.FAILED
        }
    }
    
    /**
     * 停止保活管理器
     */
    suspend fun stop() {
        if (!isRunning.get()) {
            return
        }
        
        try {
            isRunning.set(false)
            
            // 停止所有策略
            strategies.values.forEach { strategy ->
                strategy.stop()
            }
            
            _managerStatus.value = KeepAliveStatus.INACTIVE
            
            AILog.i(TAG, "KeepAliveManager stopped")
_keepAliveEvents.tryEmit(KeepAliveEvent.Stopped(packageName = "")) // Assuming packageName here
            
        } catch (e: Exception) {
            AILog.e(TAG, "Failed to stop KeepAliveManager", e)
        }
    }
    
    /**
     * 检查包状态
     */
    suspend fun checkPackageStatus(packageName: String? = null) {
        if (!isRunning.get()) return
        
        val packagesToCheck = packageName?.let { listOf(it) } ?: config.targetPackages
        
        packagesToCheck.forEach { pkg ->
            managerScope?.launch {
                try {
                    totalChecks.incrementAndGet()
                    
                    // 使用包激活策略检查状态
                    val strategy = strategies[KeepAliveStrategy.PACKAGE_ACTIVATION] as? PackageActivationStrategy
                    val status = strategy?.checkStatus(pkg) ?: KeepAliveStatus.UNKNOWN
                    
                    packageStatusMap[pkg] = status
                    updatePackageStatusFlow()
                    
                    _keepAliveEvents.tryEmit(KeepAliveEvent.CheckCompleted(pkg))
                    callback?.onEvent(KeepAliveEvent.CheckCompleted(pkg), pkg, "Status check completed: $status")
                    
                } catch (e: Exception) {
                    AILog.e(TAG, "Failed to check status for package: $pkg", e)
                    callback?.onError(pkg, KeepAliveStrategy.PACKAGE_ACTIVATION, "Status check failed", e)
                }
            }
        }
    }
    
    /**
     * 激活包
     */
    suspend fun activatePackage(packageName: String): Boolean {
        if (!isRunning.get()) return false
        
        return try {
            val strategy = strategies[KeepAliveStrategy.PACKAGE_ACTIVATION] as? PackageActivationStrategy
            val result = strategy?.activate(packageName) ?: false
            
            if (result) {
                successfulActivations.incrementAndGet()
                packageStatusMap[packageName] = KeepAliveStatus.ACTIVE
                val event = KeepAliveEvent.PackageActivated(packageName)
                _keepAliveEvents.tryEmit(event)
                callback?.onEvent(event, packageName, "Package activated successfully")
            } else {
                failedActivations.incrementAndGet()
                callback?.onError(packageName, KeepAliveStrategy.PACKAGE_ACTIVATION, "Package activation failed")
            }
            
            updatePackageStatusFlow()
            result
            
        } catch (e: Exception) {
            AILog.e(TAG, "Failed to activate package: $packageName", e)
            failedActivations.incrementAndGet()
            callback?.onError(packageName, KeepAliveStrategy.PACKAGE_ACTIVATION, "Package activation exception", e)
            false
        }
    }
    
    /**
     * 获取包状态
     */
    fun getPackageStatus(packageName: String): KeepAliveStatus {
        return packageStatusMap[packageName] ?: KeepAliveStatus.UNKNOWN
    }
    
    /**
     * 获取所有包状态
     */
    fun getAllPackageStatuses(): Map<String, KeepAliveStatus> {
        return packageStatusMap.toMap()
    }
    
    /**
     * 更新配置
     */
    suspend fun updateConfig(newConfig: KeepAliveConfig) {
        this.config = newConfig
        
        // 更新策略配置
        strategies.values.forEach { strategy ->
            strategy.updateConfig(newConfig)
        }
        
        AILog.i(TAG, "Configuration updated")
    }
    
    /**
     * 获取统计信息
     */
    fun getStatistics(): KeepAliveStatistics {
        val currentTime = System.currentTimeMillis()
        val uptime = if (startTime.get() > 0) currentTime - startTime.get() else 0
        
        val strategyStats = strategies.mapKeys { it.key }.mapValues { (_, strategy) ->
            val stats = strategy.getStatistics()
            StrategyStatistics(
                enabled = strategy.isEnabled,
                totalExecutions = stats["totalExecutions"] as? Long ?: 0,
                successfulExecutions = stats["successfulExecutions"] as? Long ?: 0,
                failedExecutions = stats["failedExecutions"] as? Long ?: 0,
                averageExecutionTime = stats["averageExecutionTime"] as? Long ?: 0,
                lastExecutionTime = stats["lastExecutionTime"] as? Long ?: 0
            )
        }
        
        return KeepAliveStatistics(
            timestamp = currentTime,
            totalPackages = config.targetPackages.size,
            activePackages = packageStatusMap.values.count { it == KeepAliveStatus.ACTIVE },
            inactivePackages = packageStatusMap.values.count { it == KeepAliveStatus.INACTIVE },
            totalChecks = totalChecks.get(),
            successfulActivations = successfulActivations.get(),
            failedActivations = failedActivations.get(),
            uptime = uptime,
            strategies = strategyStats
        )
    }
    
    /**
     * 释放资源
     */
    suspend fun release() {
        try {
            stop()
            
            strategies.values.forEach { strategy ->
                strategy.release()
            }
            strategies.clear()
            
            managerScope?.cancel()
            managerScope = null
            
            packageStatusMap.clear()
            _packageStatuses.value = emptyMap()
            _managerStatus.value = KeepAliveStatus.UNKNOWN
            
            isInitialized.set(false)
            startTime.set(0)
            
            context = null
            callback = null
            
            AILog.i(TAG, "KeepAliveManager released")
            
        } catch (e: Exception) {
            AILog.e(TAG, "Error during release", e)
        }
    }
    
    /**
     * 检查是否正在运行
     */
    fun isRunning(): Boolean = isRunning.get()
    
    /**
     * 初始化策略
     */
    private suspend fun initializeStrategies() {
        val ctx = context ?: return
        
        // 创建策略实例
        val packageActivationStrategy = PackageActivationStrategy()
        val periodicCheckStrategy = PeriodicCheckStrategy()
        val broadcastReceiverStrategy = BroadcastReceiverStrategy()
        val dozeWhitelistStrategy = DozeWhitelistStrategy()
        
        // 创建统一回调
        val strategyCallback = createStrategyCallback()
        
        // 初始化策略
        packageActivationStrategy.initialize(ctx, config, strategyCallback)
        periodicCheckStrategy.initialize(ctx, config, strategyCallback)
        broadcastReceiverStrategy.initialize(ctx, config, strategyCallback)
        dozeWhitelistStrategy.initialize(ctx, config, strategyCallback)
        
        // 添加到策略映射
        strategies[KeepAliveStrategy.PACKAGE_ACTIVATION] = packageActivationStrategy
        strategies[KeepAliveStrategy.PERIODIC_CHECK] = periodicCheckStrategy
        strategies[KeepAliveStrategy.BROADCAST_RECEIVER] = broadcastReceiverStrategy
        strategies[KeepAliveStrategy.DOZE_WHITELIST] = dozeWhitelistStrategy
    }
    
    /**
     * 创建策略回调
     */
    private fun createStrategyCallback(): KeepAliveCallback {
        return object : KeepAliveCallback {
            override fun onStatusChanged(packageName: String, status: KeepAliveStatus, strategy: KeepAliveStrategy) {
                packageStatusMap[packageName] = status
                updatePackageStatusFlow()
                callback?.onStatusChanged(packageName, status, strategy)
            }
            
override fun onEvent(event: KeepAliveEvent, packageName: String, message: String, extra: Map<String, Any>) {
                _keepAliveEvents.tryEmit(event)
                callback?.onEvent(event, packageName, message, extra)
            }
            
            override fun onError(packageName: String, strategy: KeepAliveStrategy, error: String, exception: Throwable?) {
                callback?.onError(packageName, strategy, error, exception)
            }
            
            override fun onStatistics(statistics: KeepAliveStatistics) {
                callback?.onStatistics(statistics)
            }
        }
    }
    
    /**
     * 更新包状态流
     */
    private fun updatePackageStatusFlow() {
        _packageStatuses.value = packageStatusMap.toMap()
    }
    
    /**
     * 启动统计任务
     */
    private fun startStatisticsTask() {
        managerScope?.launch {
            while (isRunning.get()) {
                try {
                    delay(config.statisticsInterval)
                    val statistics = getStatistics()
                    callback?.onStatistics(statistics)
                } catch (e: Exception) {
                    AILog.e(TAG, "Error in statistics task", e)
                    delay(5000) // 出错后等待5秒
                }
            }
        }
    }
}
