package com.aispeech.keepalive.strategy

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import com.aispeech.aibase.AILog
import com.aispeech.keepalive.*
import kotlinx.coroutines.launch

/**
 * 广播接收器策略
 * 监听系统广播事件并触发保活检查
 */
class BroadcastReceiverStrategy : BaseKeepAliveStrategy() {
    
    override val name: String = "BroadcastReceiver"
    override val strategy: KeepAliveStrategy = KeepAliveStrategy.BROADCAST_RECEIVER
    
    override val isEnabled: Boolean
        get() = config.broadcastReceiver.enabled
    
    private var receiver: KeepAliveBroadcastReceiver? = null
    private var packageActivationStrategy: PackageActivationStrategy? = null
    
    override suspend fun onInitialize() {
        // 创建包激活策略实例
        packageActivationStrategy = PackageActivationStrategy()
        context?.let { ctx ->
            packageActivationStrategy?.initialize(ctx, config, callback)
        }
        
        // 创建广播接收器
        receiver = KeepAliveBroadcastReceiver()
    }
    
    override suspend fun onStart() {
        registerReceiver()
        AILog.d(TAG, "Broadcast receiver strategy started")
    }
    
    override suspend fun onStop() {
        unregisterReceiver()
        AILog.d(TAG, "Broadcast receiver strategy stopped")
    }
    
    override suspend fun onConfigUpdated() {
        // 重新注册接收器以应用新配置
        if (isRunning.get()) {
            unregisterReceiver()
            registerReceiver()
            AILog.d(TAG, "Broadcast receiver re-registered with new configuration")
        }
    }
    
    override suspend fun onRelease() {
        packageActivationStrategy?.release()
        packageActivationStrategy = null
        receiver = null
    }
    
    override suspend fun checkStatus(packageName: String): KeepAliveStatus {
        return packageActivationStrategy?.checkStatus(packageName) ?: KeepAliveStatus.UNKNOWN
    }
    
    override suspend fun activate(packageName: String): Boolean {
        return packageActivationStrategy?.activate(packageName) ?: false
    }
    
    /**
     * 注册广播接收器
     */
    private fun registerReceiver() {
        val ctx = context ?: return
        val rcv = receiver ?: return
        val broadcastConfig = config.broadcastReceiver
        
        try {
            val intentFilter = IntentFilter().apply {
                if (broadcastConfig.listenBootCompleted) {
                    addAction(Intent.ACTION_BOOT_COMPLETED)
                    addAction(Intent.ACTION_LOCKED_BOOT_COMPLETED)
                }
                
                if (broadcastConfig.listenUserPresent) {
                    addAction(Intent.ACTION_USER_PRESENT)
                }
                
                if (broadcastConfig.listenPackageReplaced) {
                    addAction(Intent.ACTION_MY_PACKAGE_REPLACED)
                    addDataScheme("package")
                }
                
                if (broadcastConfig.listenScreenOn) {
                    addAction(Intent.ACTION_SCREEN_ON)
                }
                
                if (broadcastConfig.listenNetworkChange) {
                    addAction("android.net.conn.CONNECTIVITY_CHANGE")
                }
            }
            
            ctx.registerReceiver(rcv, intentFilter)
            AILog.d(TAG, "Broadcast receiver registered successfully")
            
        } catch (e: Exception) {
            AILog.e(TAG, "Failed to register broadcast receiver", e)
            notifyError("", "Failed to register broadcast receiver", e)
        }
    }
    
    /**
     * 注销广播接收器
     */
    private fun unregisterReceiver() {
        val ctx = context ?: return
        val rcv = receiver ?: return
        
        try {
            ctx.unregisterReceiver(rcv)
            AILog.d(TAG, "Broadcast receiver unregistered")
        } catch (e: Exception) {
            AILog.w(TAG, "Error unregistering broadcast receiver", e)
        }
    }
    
    /**
     * 处理广播事件
     */
    private fun handleBroadcastEvent(action: String, intent: Intent) {
        if (!isRunning.get()) {
            return
        }
        
        scope?.launch {
            try {
                AILog.d(TAG, "Handling broadcast event: $action")
                
                when (action) {
                    Intent.ACTION_BOOT_COMPLETED,
                    Intent.ACTION_LOCKED_BOOT_COMPLETED -> {
                        handleBootCompleted()
                    }
                    Intent.ACTION_USER_PRESENT -> {
                        handleUserPresent()
                    }
                    Intent.ACTION_MY_PACKAGE_REPLACED -> {
                        handlePackageReplaced(intent)
                    }
                    Intent.ACTION_SCREEN_ON -> {
                        handleScreenOn()
                    }
                    "android.net.conn.CONNECTIVITY_CHANGE" -> {
                        handleNetworkChange()
                    }
                }
                
                notifyEvent(KeepAliveEvent.CHECK_COMPLETED, "", "Broadcast event handled: $action")
                
            } catch (e: Exception) {
                AILog.e(TAG, "Error handling broadcast event: $action", e)
                notifyError("", "Error handling broadcast event: $action", e)
            }
        }
    }
    
    /**
     * 处理开机完成事件
     */
    private suspend fun handleBootCompleted() {
        AILog.i(TAG, "Boot completed - checking all packages")
        checkAllPackages("Boot completed")
    }
    
    /**
     * 处理用户解锁事件
     */
    private suspend fun handleUserPresent() {
        AILog.i(TAG, "User present - checking all packages")
        checkAllPackages("User present")
    }
    
    /**
     * 处理包替换事件
     */
    private suspend fun handlePackageReplaced(intent: Intent) {
        val packageName = intent.data?.schemeSpecificPart
        if (packageName != null && packageName in targetPackages) {
            AILog.i(TAG, "Package replaced: $packageName - checking status")
            checkSinglePackage(packageName, "Package replaced")
        }
    }
    
    /**
     * 处理屏幕点亮事件
     */
    private suspend fun handleScreenOn() {
        if (config.periodicCheck.checkOnScreenOn) {
            AILog.d(TAG, "Screen on - checking all packages")
            checkAllPackages("Screen on")
        }
    }
    
    /**
     * 处理网络变化事件
     */
    private suspend fun handleNetworkChange() {
        if (config.periodicCheck.checkOnNetworkChange) {
            AILog.d(TAG, "Network change - checking all packages")
            checkAllPackages("Network change")
        }
    }
    
    /**
     * 检查所有包
     */
    private suspend fun checkAllPackages(trigger: String) {
        targetPackages.forEach { packageName ->
            checkSinglePackage(packageName, trigger)
        }
    }
    
    /**
     * 检查单个包
     */
    private suspend fun checkSinglePackage(packageName: String, trigger: String) {
        executeWithStats(packageName) {
            val status = packageActivationStrategy?.checkStatus(packageName) ?: KeepAliveStatus.UNKNOWN
            
            when (status) {
                KeepAliveStatus.INACTIVE -> {
                    AILog.d(TAG, "Package $packageName is inactive (trigger: $trigger), attempting activation")
                    notifyStatusChanged(packageName, KeepAliveStatus.ACTIVATING)
                    
                    val activated = packageActivationStrategy?.activate(packageName) ?: false
                    if (activated) {
                        AILog.i(TAG, "Successfully activated package: $packageName (trigger: $trigger)")
                        notifyStatusChanged(packageName, KeepAliveStatus.ACTIVE)
                        notifyEvent(KeepAliveEvent.PACKAGE_ACTIVATED, packageName, "Package activated by broadcast: $trigger")
                    } else {
                        AILog.w(TAG, "Failed to activate package: $packageName (trigger: $trigger)")
                        notifyStatusChanged(packageName, KeepAliveStatus.FAILED)
                        notifyError(packageName, "Package activation failed on broadcast: $trigger")
                    }
                }
                KeepAliveStatus.ACTIVE -> {
                    notifyStatusChanged(packageName, KeepAliveStatus.ACTIVE)
                }
                else -> {
                    notifyStatusChanged(packageName, status)
                }
            }
            
            status
        }
    }
    
    override fun getStatistics(): Map<String, Any> {
        val baseStats = super.getStatistics().toMutableMap()
        val broadcastConfig = config.broadcastReceiver
        
        baseStats["listenBootCompleted"] = broadcastConfig.listenBootCompleted
        baseStats["listenUserPresent"] = broadcastConfig.listenUserPresent
        baseStats["listenPackageReplaced"] = broadcastConfig.listenPackageReplaced
        baseStats["listenScreenOn"] = broadcastConfig.listenScreenOn
        baseStats["listenNetworkChange"] = broadcastConfig.listenNetworkChange
        baseStats["receiverRegistered"] = (receiver != null && isRunning.get())
        
        return baseStats
    }
    
    /**
     * 保活广播接收器
     */
    private inner class KeepAliveBroadcastReceiver : BroadcastReceiver() {
        override fun onReceive(context: Context, intent: Intent) {
            val action = intent.action ?: return
            
            AILog.d(TAG, "Received broadcast: $action")
            handleBroadcastEvent(action, intent)
        }
    }
}
