package com.aispeech.keepalive.strategy

import android.content.Context
import android.os.PowerManager
import com.aispeech.aibase.AILog
import com.aispeech.keepalive.*
import kotlinx.coroutines.delay

/**
 * Doze白名单策略
 * 管理应用的Doze模式白名单状态
 */
class DozeWhitelistStrategy : BaseKeepAliveStrategy() {
    
    override val name: String = "DozeWhitelist"
    override val strategy: KeepAliveStrategy = KeepAliveStrategy.DOZE_WHITELIST
    
    override val isEnabled: Boolean
        get() = config.dozeWhitelist.enabled
    
    private val powerManager: PowerManager?
        get() = context?.getSystemService(Context.POWER_SERVICE) as? PowerManager
    
    override suspend fun onInitialize() {
        // 检查是否有必要的权限
        checkPermissions()
    }
    
    override suspend fun onStart() {
        if (config.dozeWhitelist.autoAddToWhitelist) {
            addAllPackagesToWhitelist()
        }
        AILog.d(TAG, "Doze whitelist strategy started")
    }
    
    override suspend fun onStop() {
        AILog.d(TAG, "Doze whitelist strategy stopped")
    }
    
    override suspend fun checkStatus(packageName: String): KeepAliveStatus {
        return executeWithStats(packageName) {
            checkWhitelistStatusInternal(packageName)
        } ?: KeepAliveStatus.UNKNOWN
    }
    
    override suspend fun activate(packageName: String): Boolean {
        return executeWithStats(packageName) {
            addToWhitelistInternal(packageName)
        } ?: false
    }
    
    /**
     * 检查权限
     */
    private fun checkPermissions() {
        val pm = powerManager
        if (pm == null) {
            AILog.w(TAG, "PowerManager not available")
            return
        }
        
        try {
            // 尝试检查白名单状态来验证权限
            val currentPackage = context?.packageName ?: return
            pm.isIgnoringBatteryOptimizations(currentPackage)
            AILog.d(TAG, "Doze whitelist permissions available")
        } catch (e: Exception) {
            AILog.w(TAG, "Doze whitelist permissions may not be available", e)
        }
    }
    
    /**
     * 检查包的白名单状态
     */
    private suspend fun checkWhitelistStatusInternal(packageName: String): KeepAliveStatus {
        val pm = powerManager ?: return KeepAliveStatus.UNKNOWN
        
        return try {
            val isWhitelisted = pm.isIgnoringBatteryOptimizations(packageName)
            if (isWhitelisted) {
                KeepAliveStatus.ACTIVE
            } else {
                KeepAliveStatus.INACTIVE
            }
        } catch (e: Exception) {
            AILog.e(TAG, "Error checking whitelist status for package: $packageName", e)
            KeepAliveStatus.UNKNOWN
        }
    }
    
    /**
     * 添加包到白名单
     */
    private suspend fun addToWhitelistInternal(packageName: String): Boolean {
        return try {
            // 首先检查当前状态
            val currentStatus = checkWhitelistStatusInternal(packageName)
            if (currentStatus == KeepAliveStatus.ACTIVE) {
                AILog.d(TAG, "Package $packageName already in whitelist")
                return true
            }
            
            // 尝试通过Shell命令添加到白名单
            val success = addToWhitelistByShell(packageName)
            
            if (success) {
                // 验证添加结果
                delay(1000) // 等待系统更新状态
                val newStatus = checkWhitelistStatusInternal(packageName)
                val verified = newStatus == KeepAliveStatus.ACTIVE
                
                if (verified) {
                    AILog.i(TAG, "Successfully added package to whitelist: $packageName")
                    notifyEvent(KeepAliveEvent.WHITELIST_ADDED, packageName, "Package added to Doze whitelist")
                    notifyStatusChanged(packageName, KeepAliveStatus.ACTIVE)
                } else {
                    AILog.w(TAG, "Failed to verify whitelist addition for package: $packageName")
                }
                
                verified
            } else {
                AILog.w(TAG, "Failed to add package to whitelist: $packageName")
                false
            }
            
        } catch (e: Exception) {
            AILog.e(TAG, "Error adding package to whitelist: $packageName", e)
            false
        }
    }
    
    /**
     * 通过Shell命令添加到白名单
     */
    private suspend fun addToWhitelistByShell(packageName: String): Boolean {
        return try {
            val command = "dumpsys deviceidle whitelist +$packageName"
            val process = Runtime.getRuntime().exec(command)
            val exitCode = process.waitFor()
            
            if (exitCode == 0) {
                AILog.d(TAG, "Shell command successful for adding $packageName to whitelist")
                true
            } else {
                AILog.w(TAG, "Shell command failed for adding $packageName to whitelist, exit code: $exitCode")
                false
            }
        } catch (e: Exception) {
            AILog.e(TAG, "Error executing shell command for package: $packageName", e)
            false
        }
    }
    
    /**
     * 从白名单移除包
     */
    suspend fun removeFromWhitelist(packageName: String): Boolean {
        return executeWithStats(packageName) {
            removeFromWhitelistInternal(packageName)
        } ?: false
    }
    
    /**
     * 从白名单移除包的内部实现
     */
    private suspend fun removeFromWhitelistInternal(packageName: String): Boolean {
        return try {
            // 首先检查当前状态
            val currentStatus = checkWhitelistStatusInternal(packageName)
            if (currentStatus != KeepAliveStatus.ACTIVE) {
                AILog.d(TAG, "Package $packageName not in whitelist")
                return true
            }
            
            // 尝试通过Shell命令从白名单移除
            val command = "dumpsys deviceidle whitelist -$packageName"
            val process = Runtime.getRuntime().exec(command)
            val exitCode = process.waitFor()
            
            if (exitCode == 0) {
                // 验证移除结果
                delay(1000) // 等待系统更新状态
                val newStatus = checkWhitelistStatusInternal(packageName)
                val verified = newStatus != KeepAliveStatus.ACTIVE
                
                if (verified) {
                    AILog.i(TAG, "Successfully removed package from whitelist: $packageName")
                    notifyEvent(KeepAliveEvent.WHITELIST_REMOVED, packageName, "Package removed from Doze whitelist")
                    notifyStatusChanged(packageName, KeepAliveStatus.INACTIVE)
                } else {
                    AILog.w(TAG, "Failed to verify whitelist removal for package: $packageName")
                }
                
                verified
            } else {
                AILog.w(TAG, "Failed to remove package from whitelist: $packageName, exit code: $exitCode")
                false
            }
            
        } catch (e: Exception) {
            AILog.e(TAG, "Error removing package from whitelist: $packageName", e)
            false
        }
    }
    
    /**
     * 添加所有目标包到白名单
     */
    private suspend fun addAllPackagesToWhitelist() {
        AILog.d(TAG, "Adding all target packages to whitelist")
        
        targetPackages.forEach { packageName ->
            try {
                addToWhitelistInternal(packageName)
            } catch (e: Exception) {
                AILog.e(TAG, "Failed to add package to whitelist: $packageName", e)
                notifyError(packageName, "Failed to add to whitelist", e)
            }
        }
    }
    
    /**
     * 检查所有包的白名单状态
     */
    suspend fun checkAllWhitelistStatuses(): Map<String, KeepAliveStatus> {
        val results = mutableMapOf<String, KeepAliveStatus>()
        
        targetPackages.forEach { packageName ->
            val status = checkStatus(packageName)
            results[packageName] = status
        }
        
        return results
    }
    
    /**
     * 获取系统白名单信息
     */
    suspend fun getSystemWhitelistInfo(): Map<String, Any> {
        return try {
            val pm = powerManager
            val info = mutableMapOf<String, Any>()
            
            if (pm != null) {
                info["powerManagerAvailable"] = true
                
                // 检查当前应用的白名单状态
                val currentPackage = context?.packageName
                if (currentPackage != null) {
                    info["currentAppWhitelisted"] = pm.isIgnoringBatteryOptimizations(currentPackage)
                }
            } else {
                info["powerManagerAvailable"] = false
            }
            
            info["targetPackagesCount"] = targetPackages.size
            info
        } catch (e: Exception) {
            AILog.e(TAG, "Error getting system whitelist info", e)
            mapOf("error" to e.message)
        }
    }
    
    override fun getStatistics(): Map<String, Any> {
        val baseStats = super.getStatistics().toMutableMap()
        val dozeConfig = config.dozeWhitelist
        
        baseStats["autoAddToWhitelist"] = dozeConfig.autoAddToWhitelist
        baseStats["checkWhitelistStatus"] = dozeConfig.checkWhitelistStatus
        baseStats["powerManagerAvailable"] = (powerManager != null)
        
        return baseStats
    }
}
