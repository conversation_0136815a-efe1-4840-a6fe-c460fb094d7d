package com.aispeech.keepalive.strategy

import android.content.Context
import com.aispeech.aibase.AILog
import com.aispeech.keepalive.*
import kotlinx.coroutines.*
import java.util.concurrent.atomic.AtomicBoolean
import java.util.concurrent.atomic.AtomicLong

/**
 * 保活策略基类
 */
abstract class BaseKeepAliveStrategy {
    
    protected val TAG = this::class.java.simpleName
    
    // 基本属性
    abstract val name: String
    abstract val strategy: KeepAliveStrategy
    
    // 状态管理
    protected var context: Context? = null
    protected var config: KeepAliveConfig = KeepAliveConfig()
    protected var callback: KeepAliveCallback? = null
    
    protected val isInitialized = AtomicBoolean(false)
    protected val isRunning = AtomicBoolean(false)
    
    // 协程管理
    protected var scope: CoroutineScope? = null
    
    // 统计数据
    protected val totalExecutions = AtomicLong(0)
    protected val successfulExecutions = AtomicLong(0)
    protected val failedExecutions = AtomicLong(0)
    protected val totalExecutionTime = AtomicLong(0)
    protected val lastExecutionTime = AtomicLong(0)
    
    // 目标包列表
    protected var targetPackages: List<String> = emptyList()
    
    /**
     * 是否启用
     */
    abstract val isEnabled: Boolean
    
    /**
     * 初始化策略
     */
    open suspend fun initialize(context: Context, config: KeepAliveConfig, callback: KeepAliveCallback?) {
        if (isInitialized.get()) {
            AILog.w(TAG, "Strategy already initialized")
            return
        }
        
        this.context = context.applicationContext
        this.config = config
        this.callback = callback
        
        // 创建协程作用域
        scope = CoroutineScope(
            SupervisorJob() + Dispatchers.Default + CoroutineName("${name}Strategy")
        )
        
        // 执行具体初始化
        onInitialize()
        
        isInitialized.set(true)
        AILog.i(TAG, "$name strategy initialized")
    }
    
    /**
     * 启动策略
     */
    open suspend fun start(packages: List<String>) {
        if (!isInitialized.get()) {
            AILog.w(TAG, "Strategy not initialized")
            return
        }
        
        if (!isEnabled) {
            AILog.d(TAG, "$name strategy is disabled")
            return
        }
        
        if (isRunning.get()) {
            AILog.w(TAG, "$name strategy already running")
            return
        }
        
        targetPackages = packages
        isRunning.set(true)
        
        try {
            onStart()
            AILog.i(TAG, "$name strategy started with ${packages.size} packages")
        } catch (e: Exception) {
            AILog.e(TAG, "Failed to start $name strategy", e)
            isRunning.set(false)
            throw e
        }
    }
    
    /**
     * 停止策略
     */
    open suspend fun stop() {
        if (!isRunning.get()) {
            return
        }
        
        try {
            onStop()
            isRunning.set(false)
            AILog.i(TAG, "$name strategy stopped")
        } catch (e: Exception) {
            AILog.e(TAG, "Failed to stop $name strategy", e)
        }
    }
    
    /**
     * 更新配置
     */
    open suspend fun updateConfig(newConfig: KeepAliveConfig) {
        this.config = newConfig
        onConfigUpdated()
    }
    
    /**
     * 检查包状态
     */
    abstract suspend fun checkStatus(packageName: String): KeepAliveStatus
    
    /**
     * 激活包
     */
    abstract suspend fun activate(packageName: String): Boolean
    
    /**
     * 获取统计信息
     */
    open fun getStatistics(): Map<String, Any> {
        val avgExecutionTime = if (totalExecutions.get() > 0) {
            totalExecutionTime.get() / totalExecutions.get()
        } else 0L
        
        return mapOf(
            "name" to name,
            "strategy" to strategy.name,
            "enabled" to isEnabled,
            "running" to isRunning.get(),
            "totalExecutions" to totalExecutions.get(),
            "successfulExecutions" to successfulExecutions.get(),
            "failedExecutions" to failedExecutions.get(),
            "averageExecutionTime" to avgExecutionTime,
            "lastExecutionTime" to lastExecutionTime.get(),
            "targetPackages" to targetPackages.size
        )
    }
    
    /**
     * 释放资源
     */
    open suspend fun release() {
        try {
            stop()
            
            scope?.cancel()
            scope = null
            
            onRelease()
            
            isInitialized.set(false)
            context = null
            callback = null
            
            AILog.i(TAG, "$name strategy released")
            
        } catch (e: Exception) {
            AILog.e(TAG, "Error during $name strategy release", e)
        }
    }
    
    /**
     * 执行操作并记录统计
     */
    protected suspend inline fun <T> executeWithStats(
        packageName: String,
        operation: suspend () -> T
    ): T? {
        val startTime = System.currentTimeMillis()
        totalExecutions.incrementAndGet()
        
        return try {
            val result = operation()
            val executionTime = System.currentTimeMillis() - startTime
            
            successfulExecutions.incrementAndGet()
            totalExecutionTime.addAndGet(executionTime)
            lastExecutionTime.set(System.currentTimeMillis())
            
            result
        } catch (e: Exception) {
            val executionTime = System.currentTimeMillis() - startTime
            
            failedExecutions.incrementAndGet()
            totalExecutionTime.addAndGet(executionTime)
            lastExecutionTime.set(System.currentTimeMillis())
            
            AILog.e(TAG, "Operation failed for package: $packageName", e)
            callback?.onError(packageName, strategy, "Operation failed: ${e.message}", e)
            
            null
        }
    }
    
    /**
     * 通知状态变化
     */
    protected fun notifyStatusChanged(packageName: String, status: KeepAliveStatus) {
        callback?.onStatusChanged(packageName, status, strategy)
    }
    
    /**
     * 通知事件
     */
    protected fun notifyEvent(event: KeepAliveEvent, packageName: String, message: String, extra: Map<String, Any> = emptyMap()) {
        callback?.onEvent(event, packageName, message, extra)
    }
    
    /**
     * 通知错误
     */
    protected fun notifyError(packageName: String, error: String, exception: Throwable? = null) {
        callback?.onError(packageName, strategy, error, exception)
    }
    
    // 抽象方法，由子类实现
    
    /**
     * 具体初始化逻辑
     */
    protected abstract suspend fun onInitialize()
    
    /**
     * 具体启动逻辑
     */
    protected abstract suspend fun onStart()
    
    /**
     * 具体停止逻辑
     */
    protected abstract suspend fun onStop()
    
    /**
     * 配置更新处理
     */
    protected open suspend fun onConfigUpdated() {
        // 默认空实现
    }
    
    /**
     * 具体释放逻辑
     */
    protected open suspend fun onRelease() {
        // 默认空实现
    }
}
