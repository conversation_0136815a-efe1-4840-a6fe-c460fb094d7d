package com.aispeech.keepalive.strategy

import android.content.Context
import android.content.Intent
import android.content.pm.ApplicationInfo
import android.content.pm.PackageManager
import com.aispeech.aibase.AILog
import com.aispeech.keepalive.*
import kotlinx.coroutines.delay
import kotlinx.coroutines.withTimeout
import java.io.BufferedReader
import java.io.InputStreamReader

/**
 * 包激活策略
 * 负责检查和激活应用包
 */
class PackageActivationStrategy : BaseKeepAliveStrategy() {
    
    override val name: String = "PackageActivation"
    override val strategy: KeepAliveStrategy = KeepAliveStrategy.PACKAGE_ACTIVATION
    
    override val isEnabled: Boolean
        get() = config.packageActivation.enabled
    
    private val packageManager: PackageManager?
        get() = context?.packageManager
    
    override suspend fun onInitialize() {
        // 包激活策略无需特殊初始化
    }
    
    override suspend fun onStart() {
        // 包激活策略无需特殊启动逻辑
        AILog.d(TAG, "Package activation strategy started")
    }
    
    override suspend fun onStop() {
        // 包激活策略无需特殊停止逻辑
        AILog.d(TAG, "Package activation strategy stopped")
    }
    
    override suspend fun checkStatus(packageName: String): KeepAliveStatus {
        return executeWithStats(packageName) {
            checkPackageStatusInternal(packageName)
        } ?: KeepAliveStatus.UNKNOWN
    }
    
    override suspend fun activate(packageName: String): Boolean {
        return executeWithStats(packageName) {
            activatePackageInternal(packageName)
        } ?: false
    }
    
    /**
     * 检查包状态的内部实现
     */
    private suspend fun checkPackageStatusInternal(packageName: String): KeepAliveStatus {
        val pm = packageManager ?: return KeepAliveStatus.UNKNOWN
        
        return try {
            val appInfo = pm.getApplicationInfo(packageName, 0)
            
            // 检查应用是否被停用
            val isStopped = (appInfo.flags and ApplicationInfo.FLAG_STOPPED) != 0
            
            if (isStopped) {
                KeepAliveStatus.INACTIVE
            } else {
                KeepAliveStatus.ACTIVE
            }
            
        } catch (e: PackageManager.NameNotFoundException) {
            AILog.w(TAG, "Package not found: $packageName")
            KeepAliveStatus.UNKNOWN
        } catch (e: Exception) {
            AILog.e(TAG, "Error checking package status: $packageName", e)
            KeepAliveStatus.UNKNOWN
        }
    }
    
    /**
     * 激活包的内部实现
     */
    private suspend fun activatePackageInternal(packageName: String): Boolean {
        val activationConfig = config.packageActivation
        
        return try {
            withTimeout(activationConfig.activationTimeout) {
                when (activationConfig.activationMethod) {
                    PackageActivationConfig.ActivationMethod.ACTIVITY -> {
                        activateByActivity(packageName)
                    }
                    PackageActivationConfig.ActivationMethod.SHELL -> {
                        activateByShell(packageName)
                    }
                    PackageActivationConfig.ActivationMethod.COMPREHENSIVE -> {
                        activateComprehensive(packageName)
                    }
                }
            }
        } catch (e: Exception) {
            AILog.e(TAG, "Failed to activate package: $packageName", e)
            false
        }
    }
    
    /**
     * 通过启动Activity激活包
     */
    private suspend fun activateByActivity(packageName: String): Boolean {
        val ctx = context ?: return false
        val pm = packageManager ?: return false
        
        return try {
            val launchIntent = pm.getLaunchIntentForPackage(packageName)
            if (launchIntent != null) {
                launchIntent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                launchIntent.addFlags(Intent.FLAG_ACTIVITY_RESET_TASK_IF_NEEDED)
                ctx.startActivity(launchIntent)
                
                // 等待一小段时间让应用启动
                delay(1000)
                
                // 验证激活结果
                if (config.packageActivation.verifyActivation) {
                    val newStatus = checkPackageStatusInternal(packageName)
                    val success = newStatus == KeepAliveStatus.ACTIVE
                    
                    if (success) {
                        notifyEvent(KeepAliveEvent.PACKAGE_ACTIVATED, packageName, "Package activated by activity")
                        notifyStatusChanged(packageName, KeepAliveStatus.ACTIVE)
                    }
                    
                    success
                } else {
                    true
                }
            } else {
                AILog.w(TAG, "No launch intent found for package: $packageName")
                false
            }
        } catch (e: Exception) {
            AILog.e(TAG, "Failed to activate package by activity: $packageName", e)
            false
        }
    }
    
    /**
     * 通过Shell命令激活包
     */
    private suspend fun activateByShell(packageName: String): Boolean {
        return try {
            val command = "am start -n $packageName/.MainActivity"
            val process = Runtime.getRuntime().exec(command)
            val exitCode = process.waitFor()
            
            if (exitCode == 0) {
                delay(1000)
                
                if (config.packageActivation.verifyActivation) {
                    val newStatus = checkPackageStatusInternal(packageName)
                    val success = newStatus == KeepAliveStatus.ACTIVE
                    
                    if (success) {
                        notifyEvent(KeepAliveEvent.PACKAGE_ACTIVATED, packageName, "Package activated by shell")
                        notifyStatusChanged(packageName, KeepAliveStatus.ACTIVE)
                    }
                    
                    success
                } else {
                    true
                }
            } else {
                AILog.w(TAG, "Shell activation failed for package: $packageName, exit code: $exitCode")
                false
            }
        } catch (e: Exception) {
            AILog.e(TAG, "Failed to activate package by shell: $packageName", e)
            false
        }
    }
    
    /**
     * 综合方法激活包
     */
    private suspend fun activateComprehensive(packageName: String): Boolean {
        val activationConfig = config.packageActivation
        var attempt = 0
        
        while (attempt < activationConfig.maxActivationRetry) {
            attempt++
            
            // 首先尝试Activity方法
            if (activateByActivity(packageName)) {
                return true
            }
            
            // 如果Activity方法失败，尝试Shell方法
            if (activateByShell(packageName)) {
                return true
            }
            
            // 如果还有重试机会，等待一段时间
            if (attempt < activationConfig.maxActivationRetry) {
                AILog.d(TAG, "Activation attempt $attempt failed for $packageName, retrying...")
                delay(activationConfig.retryDelay)
            }
        }
        
        AILog.w(TAG, "All activation attempts failed for package: $packageName")
        notifyError(packageName, "All activation attempts failed after $attempt tries")
        return false
    }
    
    /**
     * 批量检查包状态
     */
    suspend fun checkAllPackageStatuses(): Map<String, KeepAliveStatus> {
        val results = mutableMapOf<String, KeepAliveStatus>()
        
        targetPackages.forEach { packageName ->
            val status = checkStatus(packageName)
            results[packageName] = status
        }
        
        return results
    }
    
    /**
     * 批量激活包
     */
    suspend fun activateAllPackages(): Map<String, Boolean> {
        val results = mutableMapOf<String, Boolean>()
        
        targetPackages.forEach { packageName ->
            val currentStatus = checkStatus(packageName)
            if (currentStatus == KeepAliveStatus.INACTIVE) {
                val activated = activate(packageName)
                results[packageName] = activated
            } else {
                results[packageName] = true // 已经是活跃状态
            }
        }
        
        return results
    }
    
    override fun getStatistics(): Map<String, Any> {
        val baseStats = super.getStatistics().toMutableMap()
        
        baseStats["activationMethod"] = config.packageActivation.activationMethod.name
        baseStats["maxRetryCount"] = config.packageActivation.maxActivationRetry
        baseStats["activationTimeout"] = config.packageActivation.activationTimeout
        baseStats["verifyActivation"] = config.packageActivation.verifyActivation
        
        return baseStats
    }
}
