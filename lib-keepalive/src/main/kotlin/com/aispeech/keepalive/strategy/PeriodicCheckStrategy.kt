package com.aispeech.keepalive.strategy

import com.aispeech.aibase.AILog
import com.aispeech.keepalive.*
import kotlinx.coroutines.*

/**
 * 定时检查策略
 * 定期检查目标包的状态并在需要时激活
 */
class PeriodicCheckStrategy : BaseKeepAliveStrategy() {
    
    override val name: String = "PeriodicCheck"
    override val strategy: KeepAliveStrategy = KeepAliveStrategy.PERIODIC_CHECK
    
    override val isEnabled: Boolean
        get() = config.periodicCheck.enabled
    
    private var checkJob: Job? = null
    private var packageActivationStrategy: PackageActivationStrategy? = null
    
    override suspend fun onInitialize() {
        // 创建包激活策略实例用于状态检查和激活
        packageActivationStrategy = PackageActivationStrategy()
        context?.let { ctx ->
            packageActivationStrategy?.initialize(ctx, config, callback)
        }
    }
    
    override suspend fun onStart() {
        startPeriodicCheck()
        AILog.d(TAG, "Periodic check strategy started with interval: ${config.periodicCheck.checkInterval}ms")
    }
    
    override suspend fun onStop() {
        stopPeriodicCheck()
        AILog.d(TAG, "Periodic check strategy stopped")
    }
    
    override suspend fun onConfigUpdated() {
        // 重启定时检查以应用新配置
        if (isRunning.get()) {
            stopPeriodicCheck()
            startPeriodicCheck()
            AILog.d(TAG, "Periodic check restarted with new configuration")
        }
    }
    
    override suspend fun onRelease() {
        packageActivationStrategy?.release()
        packageActivationStrategy = null
    }
    
    override suspend fun checkStatus(packageName: String): KeepAliveStatus {
        return packageActivationStrategy?.checkStatus(packageName) ?: KeepAliveStatus.UNKNOWN
    }
    
    override suspend fun activate(packageName: String): Boolean {
        return packageActivationStrategy?.activate(packageName) ?: false
    }
    
    /**
     * 启动定时检查
     */
    private fun startPeriodicCheck() {
        if (checkJob?.isActive == true) {
            return
        }
        
        checkJob = scope?.launch {
            while (isActive && isRunning.get()) {
                try {
                    performPeriodicCheck()
                    delay(config.periodicCheck.checkInterval)
                } catch (e: CancellationException) {
                    AILog.d(TAG, "Periodic check cancelled")
                    break
                } catch (e: Exception) {
                    AILog.e(TAG, "Error in periodic check", e)
                    // 出错后等待一段时间再继续
                    delay(minOf(config.periodicCheck.checkInterval, 30000L))
                }
            }
        }
    }
    
    /**
     * 停止定时检查
     */
    private fun stopPeriodicCheck() {
        checkJob?.cancel()
        checkJob = null
    }
    
    /**
     * 执行定时检查
     */
    private suspend fun performPeriodicCheck() {
        if (targetPackages.isEmpty()) {
            return
        }
        
        AILog.d(TAG, "Performing periodic check for ${targetPackages.size} packages")
        
        targetPackages.forEach { packageName ->
            if (!isRunning.get()) {
                return // 如果策略已停止，退出检查
            }
            
            checkPackageWithRetry(packageName)
        }
        
        notifyEvent(KeepAliveEvent.CHECK_COMPLETED, "", "Periodic check completed for all packages")
    }
    
    /**
     * 带重试的包检查
     */
    private suspend fun checkPackageWithRetry(packageName: String) {
        val checkConfig = config.periodicCheck
        var attempt = 0
        var lastError: Exception? = null
        
        while (attempt < checkConfig.maxRetryCount && isRunning.get()) {
            attempt++
            
            try {
                val status = executeWithStats(packageName) {
                    packageActivationStrategy?.checkStatus(packageName) ?: KeepAliveStatus.UNKNOWN
                }
                
                when (status) {
                    KeepAliveStatus.INACTIVE -> {
                        AILog.d(TAG, "Package $packageName is inactive, attempting activation")
                        notifyStatusChanged(packageName, KeepAliveStatus.ACTIVATING)
                        
                        val activated = executeWithStats(packageName) {
                            packageActivationStrategy?.activate(packageName) ?: false
                        }
                        
                        if (activated == true) {
                            AILog.i(TAG, "Successfully activated package: $packageName")
                            notifyStatusChanged(packageName, KeepAliveStatus.ACTIVE)
                            notifyEvent(KeepAliveEvent.PACKAGE_ACTIVATED, packageName, "Package activated by periodic check")
                        } else {
                            AILog.w(TAG, "Failed to activate package: $packageName")
                            notifyStatusChanged(packageName, KeepAliveStatus.FAILED)
                            notifyError(packageName, "Package activation failed during periodic check")
                        }
                    }
                    KeepAliveStatus.ACTIVE -> {
                        // 包已经是活跃状态，无需操作
                        notifyStatusChanged(packageName, KeepAliveStatus.ACTIVE)
                    }
                    KeepAliveStatus.UNKNOWN -> {
                        AILog.w(TAG, "Unknown status for package: $packageName")
                        notifyStatusChanged(packageName, KeepAliveStatus.UNKNOWN)
                    }
                    else -> {
                        notifyStatusChanged(packageName, status)
                    }
                }
                
                // 成功完成检查，退出重试循环
                return
                
            } catch (e: Exception) {
                lastError = e
                AILog.w(TAG, "Check attempt $attempt failed for package: $packageName", e)
                
                if (attempt < checkConfig.maxRetryCount) {
                    delay(checkConfig.retryDelay)
                }
            }
        }
        
        // 所有重试都失败了
        if (lastError != null) {
            AILog.e(TAG, "All check attempts failed for package: $packageName", lastError)
            notifyError(packageName, "Package check failed after $attempt attempts", lastError)
            notifyStatusChanged(packageName, KeepAliveStatus.FAILED)
        }
    }
    
    /**
     * 手动触发检查
     */
    suspend fun triggerImmediateCheck() {
        if (!isRunning.get()) {
            AILog.w(TAG, "Cannot trigger check - strategy not running")
            return
        }
        
        scope?.launch {
            try {
                performPeriodicCheck()
                AILog.d(TAG, "Manual check triggered successfully")
            } catch (e: Exception) {
                AILog.e(TAG, "Manual check failed", e)
            }
        }
    }
    
    /**
     * 检查单个包
     */
    suspend fun checkSinglePackage(packageName: String) {
        if (!isRunning.get()) {
            AILog.w(TAG, "Cannot check package - strategy not running")
            return
        }
        
        if (packageName !in targetPackages) {
            AILog.w(TAG, "Package $packageName is not in target list")
            return
        }
        
        checkPackageWithRetry(packageName)
    }
    
    override fun getStatistics(): Map<String, Any> {
        val baseStats = super.getStatistics().toMutableMap()
        
        baseStats["checkInterval"] = config.periodicCheck.checkInterval
        baseStats["maxRetryCount"] = config.periodicCheck.maxRetryCount
        baseStats["retryDelay"] = config.periodicCheck.retryDelay
        baseStats["checkOnScreenOn"] = config.periodicCheck.checkOnScreenOn
        baseStats["checkOnNetworkChange"] = config.periodicCheck.checkOnNetworkChange
        baseStats["isCheckJobActive"] = checkJob?.isActive ?: false
        
        return baseStats
    }
}
