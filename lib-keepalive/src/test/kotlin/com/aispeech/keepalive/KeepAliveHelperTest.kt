package com.aispeech.keepalive

import android.content.Context
import androidx.test.core.app.ApplicationProvider
import androidx.test.ext.junit.runners.AndroidJUnit4
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.test.*
import org.junit.After
import org.junit.Assert.*
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith

/**
 * KeepAliveHelper单元测试
 */
@ExperimentalCoroutinesApi
@RunWith(AndroidJUnit4::class)
class KeepAliveHelperTest {
    
    private lateinit var context: Context
    private val testPackages = listOf("com.test.package1", "com.test.package2")
    
    private val testDispatcher = StandardTestDispatcher()
    
    @Before
    fun setUp() {
        // 设置测试调度器
        Dispatchers.setMain(testDispatcher)
        
        context = ApplicationProvider.getApplicationContext()
    }
    
    @After
    fun tearDown() {
        runTest {
            KeepAliveHelper.release()
        }
        Dispatchers.resetMain()
    }
    
    @Test
    fun testInitialization() = runTest {
        // 测试初始化
        val result = KeepAliveHelper.initialize(context, testPackages)
        assertTrue("Helper should initialize successfully", result)
        
        // 测试重复初始化
        val result2 = KeepAliveHelper.initialize(context, testPackages)
        assertTrue("Helper should handle duplicate initialization", result2)
    }
    
    @Test
    fun testStartStop() = runTest {
        // 初始化
        KeepAliveHelper.initialize(context, testPackages)
        
        // 启动
        val startResult = KeepAliveHelper.start()
        assertTrue("Helper should start successfully", startResult)
        assertTrue("Helper should be running", KeepAliveHelper.isRunning())
        
        // 停止
        val stopResult = KeepAliveHelper.stop()
        assertTrue("Helper should stop successfully", stopResult)
    }
    
    @Test
    fun testQuickStart() = runTest {
        // 测试快速启动
        val result = KeepAliveHelper.quickStart(context, testPackages)
        assertTrue("Quick start should succeed", result)
        assertTrue("Helper should be running after quick start", KeepAliveHelper.isRunning())
    }
    
    @Test
    fun testPackageStatusCheck() = runTest {
        // 初始化并启动
        KeepAliveHelper.initialize(context, testPackages)
        KeepAliveHelper.start()
        advanceUntilIdle()
        
        // 检查包状态
        val status = KeepAliveHelper.checkPackageStatus("com.test.package1")
        assertTrue("Status should be valid", status in KeepAliveStatus.values())
    }
    
    @Test
    fun testGetAllPackageStatuses() = runTest {
        // 初始化并启动
        KeepAliveHelper.initialize(context, testPackages)
        KeepAliveHelper.start()
        advanceUntilIdle()
        
        // 获取所有包状态
        val statuses = KeepAliveHelper.getAllPackageStatuses()
        
        // 验证返回的状态包含所有目标包
        testPackages.forEach { packageName ->
            assertTrue("Status should contain package $packageName", statuses.containsKey(packageName))
        }
    }
    
    @Test
    fun testPackageActivation() = runTest {
        // 初始化并启动
        KeepAliveHelper.initialize(context, testPackages)
        KeepAliveHelper.start()
        advanceUntilIdle()
        
        // 尝试激活包
        val result = KeepAliveHelper.activatePackage("com.test.package1")
        
        // 由于是测试环境，激活可能失败，但应该有响应
        assertTrue("Activation should return a boolean result", result is Boolean)
    }
    
    @Test
    fun testActivateAllPackages() = runTest {
        // 初始化并启动
        KeepAliveHelper.initialize(context, testPackages)
        KeepAliveHelper.start()
        advanceUntilIdle()
        
        // 批量激活所有包
        val results = KeepAliveHelper.activateAllPackages()
        
        // 验证返回结果包含所有目标包
        testPackages.forEach { packageName ->
            assertTrue("Results should contain package $packageName", results.containsKey(packageName))
        }
    }
    
    @Test
    fun testStatistics() = runTest {
        // 初始化并启动
        KeepAliveHelper.initialize(context, testPackages)
        KeepAliveHelper.start()
        advanceUntilIdle()
        
        // 获取统计信息
        val statistics = KeepAliveHelper.getStatistics()
        
        // 验证统计信息
        assertNotNull("Statistics should not be null", statistics)
        statistics?.let { stats ->
            assertEquals("Total packages should match", testPackages.size, stats.totalPackages)
            assertTrue("Timestamp should be valid", stats.timestamp > 0)
        }
    }
    
    @Test
    fun testSimpleCallback() = runTest {
        // 测试简单回调创建
        val callback = KeepAliveHelper.createSimpleCallback()
        assertNotNull("Callback should not be null", callback)
        
        // 测试回调方法不会抛出异常
        callback.onStatusChanged("test.package", KeepAliveStatus.ACTIVE, KeepAliveStrategy.PACKAGE_ACTIVATION)
        callback.onEvent(KeepAliveEvent.STARTED, "test.package", "Test message")
        callback.onError("test.package", KeepAliveStrategy.PACKAGE_ACTIVATION, "Test error")
        callback.onStatistics(KeepAliveStatistics())
    }
    
    @Test
    fun testUninitializedOperations() = runTest {
        // 测试未初始化时的操作
        assertFalse("Should not be running when uninitialized", KeepAliveHelper.isRunning())
        
        val startResult = KeepAliveHelper.start()
        assertFalse("Start should fail when uninitialized", startResult)
        
        val status = KeepAliveHelper.checkPackageStatus("test.package")
        assertEquals("Status should be unknown when uninitialized", KeepAliveStatus.UNKNOWN, status)
        
        val statuses = KeepAliveHelper.getAllPackageStatuses()
        assertTrue("Statuses should be empty when uninitialized", statuses.isEmpty())
        
        val statistics = KeepAliveHelper.getStatistics()
        assertNull("Statistics should be null when uninitialized", statistics)
    }
    
    @Test
    fun testResourceCleanup() = runTest {
        // 初始化并启动
        KeepAliveHelper.initialize(context, testPackages)
        KeepAliveHelper.start()
        assertTrue("Helper should be running", KeepAliveHelper.isRunning())
        
        // 释放资源
        KeepAliveHelper.release()
        assertFalse("Helper should not be running after release", KeepAliveHelper.isRunning())
        
        // 验证释放后的状态
        val status = KeepAliveHelper.checkPackageStatus("test.package")
        assertEquals("Status should be unknown after release", KeepAliveStatus.UNKNOWN, status)
    }
    
    @Test
    fun testErrorHandling() = runTest {
        // 测试空包列表
        val result = KeepAliveHelper.initialize(context, emptyList())
        assertTrue("Should handle empty package list", result)
        
        // 测试无效包名
        KeepAliveHelper.initialize(context, listOf("invalid.package.name"))
        KeepAliveHelper.start()
        
        val status = KeepAliveHelper.checkPackageStatus("invalid.package.name")
        assertTrue("Should handle invalid package gracefully", status in KeepAliveStatus.values())
    }
}
