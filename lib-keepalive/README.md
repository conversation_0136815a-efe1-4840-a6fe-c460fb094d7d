# lib-keepalive

一个简单易用的Android应用保活库，提供多种保活策略来确保应用持续运行。

## 特性

- 🚀 **简单易用**: 提供简化的API接口，快速集成
- 🔧 **多种策略**: 支持包激活、定时检查、广播监听、Doze白名单等多种保活策略
- ⚡ **高性能**: 基于Kotlin协程，异步执行，不阻塞主线程
- 📊 **状态监控**: 实时监控包状态，提供详细的统计信息
- 🛡️ **错误处理**: 完善的错误处理和重试机制
- 🎯 **模块化**: 策略独立，可灵活配置和组合

## 快速开始

### 1. 添加依赖

在你的模块的 `build.gradle.kts` 中添加：

```kotlin
dependencies {
    implementation(project(":lib-keepalive"))
}
```

### 2. 基本使用

```kotlin
import com.aispeech.keepalive.KeepAliveHelper
import kotlinx.coroutines.launch

class MainActivity : AppCompatActivity() {
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        // 定义需要保活的包名
        val targetPackages = listOf(
            "com.aispeech.hybridspeech",
            "com.your.app.package"
        )
        
        // 启动保活功能
        lifecycleScope.launch {
            val success = KeepAliveHelper.quickStart(this@MainActivity, targetPackages)
            if (success) {
                Log.d("KeepAlive", "保活功能启动成功")
            } else {
                Log.e("KeepAlive", "保活功能启动失败")
            }
        }
    }
    
    override fun onDestroy() {
        super.onDestroy()
        // 释放资源
        lifecycleScope.launch {
            KeepAliveHelper.release()
        }
    }
}
```

### 3. 高级使用

```kotlin
import com.aispeech.keepalive.*

// 自定义配置
val config = KeepAliveConfig(
    targetPackages = listOf("com.your.app"),
    periodicCheck = PeriodicCheckConfig(
        enabled = true,
        checkInterval = 30000L, // 30秒检查一次
        maxRetryCount = 3
    ),
    packageActivation = PackageActivationConfig(
        enabled = true,
        activationMethod = PackageActivationConfig.ActivationMethod.COMPREHENSIVE
    ),
    broadcastReceiver = BroadcastReceiverConfig(
        enabled = true,
        listenBootCompleted = true,
        listenUserPresent = true
    )
)

// 自定义回调
val callback = object : KeepAliveCallback {
    override fun onStatusChanged(packageName: String, status: KeepAliveStatus, strategy: KeepAliveStrategy) {
        Log.d("KeepAlive", "包 $packageName 状态变为 $status")
    }
    
    override fun onEvent(event: KeepAliveEvent, packageName: String, message: String, extra: Map<String, Any>) {
        Log.d("KeepAlive", "事件: $event - $message")
    }
    
    override fun onError(packageName: String, strategy: KeepAliveStrategy, error: String, exception: Throwable?) {
        Log.e("KeepAlive", "错误: $error", exception)
    }
}

// 使用管理器
lifecycleScope.launch {
    val manager = KeepAliveManager.getInstance()
    manager.initialize(this@MainActivity, config, callback)
    manager.start()
    
    // 手动检查状态
    val status = manager.getPackageStatus("com.your.app")
    Log.d("KeepAlive", "当前状态: $status")
    
    // 手动激活
    val activated = manager.activatePackage("com.your.app")
    Log.d("KeepAlive", "激活结果: $activated")
}
```

## 保活策略

### 1. 包激活策略 (PackageActivationStrategy)
- 检测应用是否被系统停止
- 通过启动Activity或Shell命令激活应用
- 支持多种激活方法和重试机制

### 2. 定时检查策略 (PeriodicCheckStrategy)
- 定期检查目标应用状态
- 发现应用停止时自动激活
- 可配置检查间隔和重试次数

### 3. 广播接收器策略 (BroadcastReceiverStrategy)
- 监听系统广播事件
- 在开机完成、用户解锁等时机触发检查
- 支持多种系统事件监听

### 4. Doze白名单策略 (DozeWhitelistStrategy)
- 管理应用的Doze模式白名单状态
- 自动添加应用到电池优化白名单
- 需要系统级权限

## 配置选项

### 基本配置
```kotlin
KeepAliveConfig(
    targetPackages = listOf("com.your.app"),
    enableStatistics = true,
    statisticsInterval = 60000L
)
```

### 定时检查配置
```kotlin
PeriodicCheckConfig(
    enabled = true,
    checkInterval = 30000L,      // 检查间隔
    maxRetryCount = 3,           // 最大重试次数
    retryDelay = 5000L,          // 重试延迟
    checkOnScreenOn = true,      // 屏幕点亮时检查
    checkOnNetworkChange = false // 网络变化时检查
)
```

### 包激活配置
```kotlin
PackageActivationConfig(
    enabled = true,
    activationMethod = ActivationMethod.COMPREHENSIVE, // 激活方法
    maxActivationRetry = 3,      // 最大激活重试次数
    activationTimeout = 10000L,  // 激活超时时间
    retryDelay = 2000L,          // 重试延迟
    verifyActivation = true      // 验证激活结果
)
```

## 权限要求

在 `AndroidManifest.xml` 中添加必要权限：

```xml
<!-- 基本权限 -->
<uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
<uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
<uses-permission android:name="android.permission.WAKE_LOCK" />

<!-- Doze白名单权限（可选） -->
<uses-permission android:name="android.permission.REQUEST_IGNORE_BATTERY_OPTIMIZATIONS" />

<!-- 系统级权限（需要系统签名，可选） -->
<uses-permission android:name="android.permission.FORCE_STOP_PACKAGES" />
<uses-permission android:name="android.permission.DEVICE_POWER" />
```

## API 参考

### KeepAliveHelper

简化的保活助手类，提供最常用的功能：

- `initialize()` - 初始化保活功能
- `start()` - 启动保活
- `stop()` - 停止保活
- `quickStart()` - 快速启动（使用默认配置）
- `checkPackageStatus()` - 检查包状态
- `activatePackage()` - 激活包
- `getAllPackageStatuses()` - 获取所有包状态
- `getStatistics()` - 获取统计信息
- `release()` - 释放资源

### KeepAliveManager

核心管理器，提供完整的保活功能：

- `initialize()` - 初始化管理器
- `start()` - 启动保活
- `stop()` - 停止保活
- `checkPackageStatus()` - 检查包状态
- `activatePackage()` - 激活包
- `updateConfig()` - 更新配置
- `getStatistics()` - 获取统计信息
- `release()` - 释放资源

## 最佳实践

1. **选择合适的策略组合**
   - 轻量级应用：定时检查 + 包激活
   - 重要服务：全部策略组合使用

2. **合理配置检查间隔**
   - 根据应用重要性调整检查频率
   - 避免过于频繁的检查影响性能

3. **监听回调事件**
   - 实现回调接口监听状态变化
   - 记录错误日志便于问题排查

4. **资源管理**
   - 在适当的生命周期释放资源
   - 避免内存泄漏

## 注意事项

- 保活功能需要相应的系统权限
- 部分功能在不同Android版本上可能有差异
- 建议在实际设备上测试保活效果
- 过度的保活可能影响设备性能和电池续航

## 许可证

本项目采用 MIT 许可证。
