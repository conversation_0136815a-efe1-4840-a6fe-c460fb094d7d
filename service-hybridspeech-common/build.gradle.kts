/*
 * Copyright (C) 2023 AISPEECH. All rights reserved.
 *
 * This software is the confidential and proprietary information of AI Speech Co., Ltd..
 * Unauthorized copying, modification, publication, or use of this software, either
 * in part or in whole, is strictly prohibited without the prior written consent of AISPEECH.
 *
 * For authorization inquiries, please contact AISPEECH at www.aispeech.com.
 */

plugins {
  id("aispeech.android.library")
  id("org.jetbrains.kotlin.plugin.parcelize")
  id("org.jetbrains.kotlin.plugin.serialization") version "1.9.25"
}

android {
  namespace = "com.aispeech.hybridspeech"

  buildFeatures {
    aidl = true
  }
}

dependencies {
  api("org.jetbrains.kotlinx:kotlinx-serialization-json:1.6.3")
  api(libs.kotlinx.collections.immutable)
  implementation("androidx.compose.runtime:runtime:1.7.5")

  // Kotlin Coroutines
  api("org.jetbrains.kotlinx:kotlinx-coroutines-core:1.7.3")
  api("org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3")

  // Android dependencies
  implementation("androidx.core:core-ktx:1.12.0")
}