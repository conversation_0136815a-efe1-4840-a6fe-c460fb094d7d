package com.aispeech.hybridspeech.example

import android.content.Context
import android.util.Log
import com.aispeech.hybridspeech.*
import kotlinx.coroutines.*

/**
 * HybridSpeechClient 使用示例
 * 
 * 这个示例展示了如何使用 HybridSpeechClient 进行录音和转写
 */
class HybridSpeechClientExample(private val context: Context) {
    
    companion object {
        private const val TAG = "HybridSpeechClientExample"
    }
    
    private var client: HybridSpeechClient? = null
    private val exampleScope = CoroutineScope(SupervisorJob() + Dispatchers.Main)
    
    /**
     * 初始化客户端
     */
    suspend fun initializeClient(): <PERSON><PERSON>an {
        return try {
            // 使用工厂创建客户端
            client = HybridSpeechClientFactory.createAndConnect(context)
            client != null
        } catch (e: Exception) {
            Log.e(TAG, "Failed to initialize client", e)
            false
        }
    }
    
    /**
     * 开始录音示例
     */
    suspend fun startRecordingExample() {
        val client = this.client ?: run {
            Log.e(TAG, "Client not initialized")
            return
        }
        
        try {
            // 创建录音配置
            val config = RecordingConfig(
                language = "zh-CN",
                onlineAsrConfig = OnlineAsrConfig(
                    audioType = "opus",
                    enableTranslation = false
                ),
                offlineAsrConfig = OfflineAsrConfig(
                    enableOfflineAsr = true
                ),
                audioConfig = AudioRecordingConfig(
                    sampleRate = 16000,
                    channels = 1,
                    bitsPerSample = 16
                )
            )
            
            // 注册转写回调
            val transcriptionCallback = object : ITranscriptionCallback.Stub() {
                override fun onTranscriptionResult(result: TranscriptionResult?) {
                    result?.let {
                        Log.d(TAG, "Transcription result: ${it.text}")
                    }
                }
                
                override fun onTranscriptionError(errorCode: Int, errorMessage: String?) {
                    Log.e(TAG, "Transcription error: $errorCode - $errorMessage")
                }
            }
            
            client.registerTranscriptionCallback(transcriptionCallback)
            
            // 开始录音
            client.startRecordingAsync(config)
            Log.d(TAG, "Recording started successfully")
            
        } catch (e: Exception) {
            Log.e(TAG, "Failed to start recording", e)
        }
    }
    
    /**
     * 停止录音示例
     */
    suspend fun stopRecordingExample() {
        val client = this.client ?: run {
            Log.e(TAG, "Client not initialized")
            return
        }
        
        try {
            val result = client.stopRecordingWithResultAsync()
            Log.d(TAG, "Recording stopped successfully")
            Log.d(TAG, "PCM file: ${result.pcmFilePath}")
            Log.d(TAG, "MP3 file: ${result.mp3FilePath}")
            Log.d(TAG, "Duration: ${result.durationMs}ms")
            Log.d(TAG, "File size: ${result.fileSizeBytes} bytes")
            
        } catch (e: Exception) {
            Log.e(TAG, "Failed to stop recording", e)
        }
    }
    
    /**
     * 监控录音进度示例
     */
    fun monitorProgressExample() {
        val client = this.client ?: run {
            Log.e(TAG, "Client not initialized")
            return
        }
        
        exampleScope.launch {
            client.createProgressFlow(500).collect { state ->
                when (state) {
                    is HybridSpeechClient.RecordingProgressState.Started -> {
                        Log.d(TAG, "Recording started")
                    }
                    is HybridSpeechClient.RecordingProgressState.Progress -> {
                        Log.d(TAG, "Recording progress: ${state.durationMs}ms")
                    }
                    is HybridSpeechClient.RecordingProgressState.Stopped -> {
                        Log.d(TAG, "Recording stopped, total: ${state.totalDurationMs}ms")
                    }
                    is HybridSpeechClient.RecordingProgressState.Error -> {
                        Log.e(TAG, "Recording error: ${state.message}")
                    }
                }
            }
        }
    }
    
    /**
     * 完整的录音流程示例
     */
    suspend fun completeRecordingFlow() {
        try {
            // 1. 初始化客户端
            if (!initializeClient()) {
                Log.e(TAG, "Failed to initialize client")
                return
            }
            
            // 2. 开始监控进度
            monitorProgressExample()
            
            // 3. 开始录音
            startRecordingExample()
            
            // 4. 等待一段时间（实际应用中可能是用户操作）
            delay(10000) // 录音10秒
            
            // 5. 停止录音
            stopRecordingExample()
            
        } catch (e: Exception) {
            Log.e(TAG, "Error in complete recording flow", e)
        } finally {
            cleanup()
        }
    }
    
    /**
     * 清理资源
     */
    fun cleanup() {
        client?.cleanup()
        client = null
        exampleScope.cancel()
    }
}
