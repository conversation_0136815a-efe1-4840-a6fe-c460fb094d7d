package com.aispeech.hybridspeech

import android.os.Parcel
import android.os.Parcelable
import androidx.compose.runtime.Immutable
import kotlinx.collections.immutable.ImmutableList
import kotlinx.collections.immutable.ImmutableMap
import kotlinx.collections.immutable.persistentListOf
import kotlinx.collections.immutable.persistentMapOf
import kotlinx.collections.immutable.toImmutableList
import kotlinx.collections.immutable.toPersistentMap
import kotlinx.serialization.Serializable

/**
 * 录音配置数据类
 * 包含录音所需的所有参数配置
 */
@Immutable
@Serializable
data class RecordingConfig(
  // 基本配置
  val useOnlineMode: Boolean = true,
  val pcmFilePath: String = "",
  val mp3FilePath: String = "",

  // 语言配置
  val language: String = "cn",
  val translate: String? = null, // 翻译目标语言，可空

  // 音频录制参数
  val audioConfig: AudioRecordingConfig = AudioRecordingConfig(),

  // MP3编码配置
  val mp3Config: Mp3EncodingConfig = Mp3EncodingConfig(),

  // Opus编码配置（用于在线ASR）
  val opusConfig: OpusEncodingConfig = OpusEncodingConfig(),

  // 录音进度配置
  val progressConfig: RecordingProgressConfig = RecordingProgressConfig(),

  // 在线ASR配置（包含业务参数）
  val onlineAsrConfig: OnlineAsrConfig? = null,

  // 离线引擎配置
  val offlineEngineConfig: OfflineEngineConfig? = null,

  // 离线翻译配置
  val offlineTranslationConfig: OfflineTranslationConfig? = null
) : Parcelable {

  constructor(parcel: Parcel) : this(
    useOnlineMode = parcel.readByte() != 0.toByte(),
    pcmFilePath = parcel.readString() ?: "",
    mp3FilePath = parcel.readString() ?: "",
    language = parcel.readString() ?: "cn",
    translate = parcel.readString(),
    audioConfig = parcel.readParcelable(AudioRecordingConfig::class.java.classLoader)
      ?: AudioRecordingConfig(),
    mp3Config = parcel.readParcelable(Mp3EncodingConfig::class.java.classLoader)
      ?: Mp3EncodingConfig(),
    opusConfig = parcel.readParcelable(OpusEncodingConfig::class.java.classLoader)
      ?: OpusEncodingConfig(),
    progressConfig = parcel.readParcelable(RecordingProgressConfig::class.java.classLoader)
      ?: RecordingProgressConfig(),
    onlineAsrConfig = parcel.readParcelable(OnlineAsrConfig::class.java.classLoader),
    offlineEngineConfig = parcel.readParcelable(OfflineEngineConfig::class.java.classLoader),
    offlineTranslationConfig = parcel.readParcelable(OfflineTranslationConfig::class.java.classLoader)
  )

  override fun writeToParcel(parcel: Parcel, flags: Int) {
    parcel.writeByte(if (useOnlineMode) 1 else 0)
    parcel.writeString(pcmFilePath)
    parcel.writeString(mp3FilePath)
    parcel.writeString(language)
    parcel.writeString(translate)
    parcel.writeParcelable(audioConfig, flags)
    parcel.writeParcelable(mp3Config, flags)
    parcel.writeParcelable(opusConfig, flags)
    parcel.writeParcelable(progressConfig, flags)
    parcel.writeParcelable(onlineAsrConfig, flags)
    parcel.writeParcelable(offlineEngineConfig, flags)
    parcel.writeParcelable(offlineTranslationConfig, flags)
  }

  override fun describeContents(): Int {
    return 0
  }

  companion object CREATOR : Parcelable.Creator<RecordingConfig> {
    override fun createFromParcel(parcel: Parcel): RecordingConfig {
      return RecordingConfig(parcel)
    }

    override fun newArray(size: Int): Array<RecordingConfig?> {
      return arrayOfNulls(size)
    }

    /**
     * 创建默认配置
     */
    fun createDefault(pcmFilePath: String, mp3FilePath: String): RecordingConfig {
      return RecordingConfig(
        useOnlineMode = true,
        pcmFilePath = pcmFilePath,
        mp3FilePath = mp3FilePath,
        audioConfig = AudioRecordingConfig.createDefault(),
        mp3Config = Mp3EncodingConfig.createDefault(),
        opusConfig = OpusEncodingConfig.createDefault(),
        progressConfig = RecordingProgressConfig.createDefault()
      )
    }

    /**
     * 创建在线模式配置
     */
    fun createOnlineConfig(
      pcmFilePath: String,
      mp3FilePath: String,
      serverUrl: String,
      apiKey: String
    ): RecordingConfig {
      return RecordingConfig(
        useOnlineMode = true,
        pcmFilePath = pcmFilePath,
        mp3FilePath = mp3FilePath,
        audioConfig = AudioRecordingConfig.createDefault(),
        mp3Config = Mp3EncodingConfig.createDefault(),
        opusConfig = OpusEncodingConfig.createDefault(),
        progressConfig = RecordingProgressConfig.createDefault(),
        onlineAsrConfig = OnlineAsrConfig(serverUrl, apiKey)
      )
    }

    /**
     * 创建离线模式配置
     */
    fun createOfflineConfig(
      pcmFilePath: String,
      mp3FilePath: String,
      modelPath: String
    ): RecordingConfig {
      return RecordingConfig(
        useOnlineMode = false,
        pcmFilePath = pcmFilePath,
        mp3FilePath = mp3FilePath,
        audioConfig = AudioRecordingConfig.createDefault(),
        mp3Config = Mp3EncodingConfig.createDefault(),
        opusConfig = OpusEncodingConfig.createDefault(),
        progressConfig = RecordingProgressConfig.createDefault(),
        offlineEngineConfig = OfflineEngineConfig(modelPath)
      )
    }

    /**
     * 创建在线模式配置（支持音频类型）
     * 替代 OnlineRecordingRequest 的功能
     */
    fun createOnlineConfigWithAudioType(
      pcmFilePath: String,
      mp3FilePath: String,
      serverUrl: String,
      apiKey: String,
      language: String = "cn",
      audioType: String = "ogg_opus",
      translate: String? = null
    ): RecordingConfig {
      return RecordingConfig(
        useOnlineMode = true,
        pcmFilePath = pcmFilePath,
        mp3FilePath = mp3FilePath,
        language = language,
        translate = translate,
        audioConfig = AudioRecordingConfig.createDefault(),
        mp3Config = Mp3EncodingConfig.createDefault(),
        opusConfig = OpusEncodingConfig.createDefault(),
        progressConfig = RecordingProgressConfig.createDefault(),
        onlineAsrConfig = OnlineAsrConfig(
          serverUrl = serverUrl,
          apiKey = apiKey,
          audioType = audioType
        )
      )
    }

    /**
     * 创建完整的在线录音配置
     * 包含所有业务字段，完全替代 OnlineRecordingRequest
     */
    fun createOnlineRecordingConfig(
      recordId: Long,
      userId: String,
      pcmFilePath: String,
      mp3FilePath: String,
      serverUrl: String,
      apiKey: String,
      language: String = "cn",
      audioType: String = "ogg_opus",
      translate: String? = null,
      enableRealtimeAgenda: Boolean = true,
      customOptions: Map<String, String> = emptyMap()
    ): RecordingConfig {
      return RecordingConfig(
        useOnlineMode = true,
        pcmFilePath = pcmFilePath,
        mp3FilePath = mp3FilePath,
        language = language,
        translate = translate,
        audioConfig = AudioRecordingConfig.createDefault(),
        mp3Config = Mp3EncodingConfig.createDefault(),
        opusConfig = OpusEncodingConfig.createDefault(),
        progressConfig = RecordingProgressConfig.createDefault(),
        onlineAsrConfig = OnlineAsrConfig(
          serverUrl = serverUrl,
          apiKey = apiKey,
          recordId = recordId,
          userId = userId,
          audioType = audioType,
          enableRealtimeAgenda = enableRealtimeAgenda,
          customOptions = customOptions.toPersistentMap()
        )
      )
    }

    /**
     * 创建支持续传的在线录音配置
     * 替代 OnlineRecordingRequest.createWithResumeSupport
     */
    fun createOnlineRecordingConfigWithResumeSupport(
      recordId: Long,
      userId: String,
      pcmFilePath: String,
      mp3FilePath: String,
      serverUrl: String,
      apiKey: String,
      language: String = "cn",
      audioType: String = "ogg_opus",
      translate: String? = null
    ): RecordingConfig {
      return RecordingConfig(
        useOnlineMode = true,
        pcmFilePath = pcmFilePath,
        mp3FilePath = mp3FilePath,
        language = language,
        translate = translate,
        audioConfig = AudioRecordingConfig.createDefault(),
        mp3Config = Mp3EncodingConfig.createDefault(),
        opusConfig = OpusEncodingConfig.createDefault(),
        progressConfig = RecordingProgressConfig.createDefault(),
        onlineAsrConfig = OnlineAsrConfig(
          serverUrl = serverUrl,
          apiKey = apiKey,
          recordId = recordId,
          userId = userId,
          audioType = audioType,
          enableRealtimeAgenda = true,
        )
      )
    }
  }
}

/**
 * 音频录制配置
 */
@Immutable
@Serializable
data class AudioRecordingConfig(
  val sampleRate: Int = 16000,
  val channels: Int = 1,
  val bitsPerSample: Int = 16,
  val bufferSizeFactor: Int = 2
) : Parcelable {

  constructor(parcel: Parcel) : this(
    sampleRate = parcel.readInt(),
    channels = parcel.readInt(),
    bitsPerSample = parcel.readInt(),
    bufferSizeFactor = parcel.readInt()
  )

  override fun writeToParcel(parcel: Parcel, flags: Int) {
    parcel.writeInt(sampleRate)
    parcel.writeInt(channels)
    parcel.writeInt(bitsPerSample)
    parcel.writeInt(bufferSizeFactor)
  }

  override fun describeContents(): Int = 0

  companion object CREATOR : Parcelable.Creator<AudioRecordingConfig> {
    override fun createFromParcel(parcel: Parcel): AudioRecordingConfig {
      return AudioRecordingConfig(parcel)
    }

    override fun newArray(size: Int): Array<AudioRecordingConfig?> {
      return arrayOfNulls(size)
    }

    fun createDefault(): AudioRecordingConfig {
      return AudioRecordingConfig(
        sampleRate = 16000,
        channels = 1,
        bitsPerSample = 16,
        bufferSizeFactor = 2
      )
    }
  }
}

/**
 * MP3编码配置
 */
@Immutable
@Serializable
data class Mp3EncodingConfig(
  val bitRate: Int = 64000, // 64kbps
  val quality: Int = 5 // 0-9, 0为最高质量
) : Parcelable {

  constructor(parcel: Parcel) : this(
    bitRate = parcel.readInt(),
    quality = parcel.readInt()
  )

  override fun writeToParcel(parcel: Parcel, flags: Int) {
    parcel.writeInt(bitRate)
    parcel.writeInt(quality)
  }

  override fun describeContents(): Int = 0

  companion object CREATOR : Parcelable.Creator<Mp3EncodingConfig> {
    override fun createFromParcel(parcel: Parcel): Mp3EncodingConfig {
      return Mp3EncodingConfig(parcel)
    }

    override fun newArray(size: Int): Array<Mp3EncodingConfig?> {
      return arrayOfNulls(size)
    }

    fun createDefault(): Mp3EncodingConfig {
      return Mp3EncodingConfig(
        bitRate = 64000,
        quality = 5
      )
    }
  }
}

/**
 * 录音结果数据类
 * 用于AIDL接口返回录音文件信息
 */
@Immutable
@Serializable
data class RecordingResultInfo(
  val pcmFilePath: String,
  val mp3FilePath: String,
  val durationMs: Long,
  val fileSizeBytes: Long,
  val audioConfig: AudioRecordingConfig
) : Parcelable {

  constructor(parcel: Parcel) : this(
    pcmFilePath = parcel.readString() ?: "",
    mp3FilePath = parcel.readString() ?: "",
    durationMs = parcel.readLong(),
    fileSizeBytes = parcel.readLong(),
    audioConfig = parcel.readParcelable(AudioRecordingConfig::class.java.classLoader)
      ?: AudioRecordingConfig()
  )

  override fun writeToParcel(parcel: Parcel, flags: Int) {
    parcel.writeString(pcmFilePath)
    parcel.writeString(mp3FilePath)
    parcel.writeLong(durationMs)
    parcel.writeLong(fileSizeBytes)
    parcel.writeParcelable(audioConfig, flags)
  }

  override fun describeContents(): Int = 0

  companion object CREATOR : Parcelable.Creator<RecordingResultInfo> {
    override fun createFromParcel(parcel: Parcel): RecordingResultInfo {
      return RecordingResultInfo(parcel)
    }

    override fun newArray(size: Int): Array<RecordingResultInfo?> {
      return arrayOfNulls(size)
    }
  }
}

/**
 * Opus编码配置
 */
@Immutable
@Serializable
data class OpusEncodingConfig(
  val bitRate: Int = 32000, // 32kbps
  val frameSize: Int = 960, // 60ms at 16kHz
  val complexity: Int = 5 // 0-10, 10为最高质量
) : Parcelable {

  constructor(parcel: Parcel) : this(
    bitRate = parcel.readInt(),
    frameSize = parcel.readInt(),
    complexity = parcel.readInt()
  )

  override fun writeToParcel(parcel: Parcel, flags: Int) {
    parcel.writeInt(bitRate)
    parcel.writeInt(frameSize)
    parcel.writeInt(complexity)
  }

  override fun describeContents(): Int = 0

  companion object CREATOR : Parcelable.Creator<OpusEncodingConfig> {
    override fun createFromParcel(parcel: Parcel): OpusEncodingConfig {
      return OpusEncodingConfig(parcel)
    }

    override fun newArray(size: Int): Array<OpusEncodingConfig?> {
      return arrayOfNulls(size)
    }

    fun createDefault(): OpusEncodingConfig {
      return OpusEncodingConfig(
        bitRate = 32000,
        frameSize = 960,
        complexity = 5
      )
    }
  }
}

/**
 * 在线ASR配置
 * 包含在线ASR相关的所有配置参数，包括业务参数和技术参数
 */
@Immutable
@Serializable
data class OnlineAsrConfig(
  // 网络配置
  val serverUrl: String,
  val apiKey: String,
  val timeout: Long = 30000L, // 30秒超时
  val retryCount: Int = 3,

  // 业务相关参数（从 OnlineRecordingRequest 迁移）
  val recordId: Long = 0L,
  val userId: String = "",
  val audioType: String = "ogg_opus",
  val enableRealtimeAgenda: Boolean = true,
  val customOptions: ImmutableMap<String, String> = persistentMapOf()
) : Parcelable {

  constructor(parcel: Parcel) : this(
    serverUrl = parcel.readString() ?: "",
    apiKey = parcel.readString() ?: "",
    timeout = parcel.readLong(),
    retryCount = parcel.readInt(),
    recordId = parcel.readLong(),
    userId = parcel.readString() ?: "",
    audioType = parcel.readString() ?: "ogg_opus",
    enableRealtimeAgenda = parcel.readByte() != 0.toByte(),
    customOptions = parcel.readHashMap(String::class.java.classLoader)?.let { map ->
      (map as? Map<String, String>)?.toPersistentMap() ?: persistentMapOf()
    } ?: persistentMapOf()
  )

  override fun writeToParcel(parcel: Parcel, flags: Int) {
    parcel.writeString(serverUrl)
    parcel.writeString(apiKey)
    parcel.writeLong(timeout)
    parcel.writeInt(retryCount)
    parcel.writeLong(recordId)
    parcel.writeString(userId)
    parcel.writeString(audioType)
    parcel.writeByte(if (enableRealtimeAgenda) 1 else 0)
    parcel.writeMap(customOptions.toMap())
  }

  override fun describeContents(): Int = 0

  companion object CREATOR : Parcelable.Creator<OnlineAsrConfig> {
    override fun createFromParcel(parcel: Parcel): OnlineAsrConfig {
      return OnlineAsrConfig(parcel)
    }

    override fun newArray(size: Int): Array<OnlineAsrConfig?> {
      return arrayOfNulls(size)
    }
  }
}

/**
 * 离线引擎配置
 */
@Immutable
@Serializable
data class OfflineEngineConfig(
  val modelPath: String,
  val enableVAD: Boolean = true, // 语音活动检测
  val vadThreshold: Float = 0.5f
) : Parcelable {

  constructor(parcel: Parcel) : this(
    modelPath = parcel.readString() ?: "",
    enableVAD = parcel.readByte() != 0.toByte(),
    vadThreshold = parcel.readFloat()
  )

  override fun writeToParcel(parcel: Parcel, flags: Int) {
    parcel.writeString(modelPath)
    parcel.writeByte(if (enableVAD) 1 else 0)
    parcel.writeFloat(vadThreshold)
  }

  override fun describeContents(): Int = 0

  companion object CREATOR : Parcelable.Creator<OfflineEngineConfig> {
    override fun createFromParcel(parcel: Parcel): OfflineEngineConfig {
      return OfflineEngineConfig(parcel)
    }

    override fun newArray(size: Int): Array<OfflineEngineConfig?> {
      return arrayOfNulls(size)
    }
  }
}

/**
 * 离线翻译配置
 */
@Immutable
@Serializable
data class OfflineTranslationConfig(
  val enabled: Boolean = false,                    // 是否启用翻译
  val taskName: String = "trans_dialect",              // 翻译任务类型
  val fromLanguage: String = "Chinese",           // 源语言
  val toLanguage: String = "English",             // 目标语言
  val domain: String = "aicar",                    // 翻译领域
  val resourcePath: String = "",                  // 翻译资源路径
  val useNer: Boolean = false,                    // 是否使用NER
  val usePost: Boolean = false,                   // 是否使用后处理
  val nerUserPath: String? = null,                // NER用户路径
  val nerUserVocabs: ImmutableList<String> = persistentListOf()   // NER用户词汇
) : Parcelable {

  constructor(parcel: Parcel) : this(
    enabled = parcel.readByte() != 0.toByte(),
    taskName = parcel.readString() ?: "trans_dialect",
    fromLanguage = parcel.readString() ?: "Chinese",
    toLanguage = parcel.readString() ?: "English",
    domain = parcel.readString() ?: "aicar",
    resourcePath = parcel.readString() ?: "",
    useNer = parcel.readByte() != 0.toByte(),
    usePost = parcel.readByte() != 0.toByte(),
    nerUserPath = parcel.readString(),
    nerUserVocabs = parcel.createStringArrayList()?.toImmutableList() ?: persistentListOf()
  )

  override fun writeToParcel(parcel: Parcel, flags: Int) {
    parcel.writeByte(if (enabled) 1 else 0)
    parcel.writeString(taskName)
    parcel.writeString(fromLanguage)
    parcel.writeString(toLanguage)
    parcel.writeString(domain)
    parcel.writeString(resourcePath)
    parcel.writeByte(if (useNer) 1 else 0)
    parcel.writeByte(if (usePost) 1 else 0)
    parcel.writeString(nerUserPath)
    parcel.writeStringList(nerUserVocabs.toList())
  }

  override fun describeContents(): Int = 0

  companion object CREATOR : Parcelable.Creator<OfflineTranslationConfig> {
    override fun createFromParcel(parcel: Parcel): OfflineTranslationConfig {
      return OfflineTranslationConfig(parcel)
    }

    override fun newArray(size: Int): Array<OfflineTranslationConfig?> {
      return arrayOfNulls(size)
    }

    /**
     * 创建默认翻译配置
     */
    fun createDefault(): OfflineTranslationConfig {
      return OfflineTranslationConfig(
        enabled = false,
        taskName = "trans_dialect",
        fromLanguage = "Chinese",
        toLanguage = "English",
        domain = "aicar"
      )
    }

    /**
     * 创建启用的翻译配置
     */
    fun createEnabled(
      taskName: String = "trans_dialect",
      fromLanguage: String = "Chinese",
      toLanguage: String = "English",
      domain: String = "aicar",
      resourcePath: String = ""
    ): OfflineTranslationConfig {
      return OfflineTranslationConfig(
        enabled = true,
        taskName = taskName,
        fromLanguage = fromLanguage,
        toLanguage = toLanguage,
        domain = domain,
        resourcePath = resourcePath
      )
    }
  }
}

/**
 * 录音进度配置
 */
@Immutable
@Serializable
data class RecordingProgressConfig(
  val enabled: Boolean = true,                    // 是否启用进度回调
  val defaultIntervalMs: Int = 200,               // 默认回调间隔（毫秒）
  val minIntervalMs: Int = 100,                   // 最小回调间隔（毫秒）
  val maxIntervalMs: Int = 1000,                  // 最大回调间隔（毫秒）
  val enableDurationQuery: Boolean = true         // 是否启用时长查询功能
) : Parcelable {

  constructor(parcel: Parcel) : this(
    enabled = parcel.readByte() != 0.toByte(),
    defaultIntervalMs = parcel.readInt(),
    minIntervalMs = parcel.readInt(),
    maxIntervalMs = parcel.readInt(),
    enableDurationQuery = parcel.readByte() != 0.toByte()
  )

  override fun writeToParcel(parcel: Parcel, flags: Int) {
    parcel.writeByte(if (enabled) 1 else 0)
    parcel.writeInt(defaultIntervalMs)
    parcel.writeInt(minIntervalMs)
    parcel.writeInt(maxIntervalMs)
    parcel.writeByte(if (enableDurationQuery) 1 else 0)
  }

  override fun describeContents(): Int = 0

  companion object CREATOR : Parcelable.Creator<RecordingProgressConfig> {
    override fun createFromParcel(parcel: Parcel): RecordingProgressConfig {
      return RecordingProgressConfig(parcel)
    }

    override fun newArray(size: Int): Array<RecordingProgressConfig?> {
      return arrayOfNulls(size)
    }

    /**
     * 创建默认进度配置
     */
    fun createDefault(): RecordingProgressConfig {
      return RecordingProgressConfig(
        enabled = true,
        defaultIntervalMs = 200,
        minIntervalMs = 100,
        maxIntervalMs = 1000,
        enableDurationQuery = true
      )
    }

    /**
     * 创建高频率进度配置（适合实时显示）
     */
    fun createHighFrequency(): RecordingProgressConfig {
      return RecordingProgressConfig(
        enabled = true,
        defaultIntervalMs = 100,
        minIntervalMs = 50,
        maxIntervalMs = 500,
        enableDurationQuery = true
      )
    }

    /**
     * 创建低频率进度配置（适合节省资源）
     */
    fun createLowFrequency(): RecordingProgressConfig {
      return RecordingProgressConfig(
        enabled = true,
        defaultIntervalMs = 500,
        minIntervalMs = 200,
        maxIntervalMs = 2000,
        enableDurationQuery = true
      )
    }
  }
}
