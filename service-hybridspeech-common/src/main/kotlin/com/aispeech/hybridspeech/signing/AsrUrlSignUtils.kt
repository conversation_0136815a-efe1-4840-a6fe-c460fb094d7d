package com.aispeech.hybridspeech.signing

import android.util.Log
import java.security.MessageDigest
import java.util.UUID


/**
 * ASR URL签名工具
 * 专门负责为URL添加签名参数
 * 设计为静态工具类，供AsrWebSocketParams扩展函数使用
 */
object AsrUrlSigner {
  private const val TAG = "AsrUrlSigner"

  /**
   * 为URL添加签名 - 使用Android Uri替代OkHttp
   */
  fun signUrl(originalUrl: String, apiKey: String, signConfig: AsrSignConfig): String {
    return try {
      // 解析现有查询参数
      val existingParams = UrlBuilder.parseQueryParams(originalUrl)

      // 添加签名相关参数
      val nonce = UUID.randomUUID().toString()
      val signParams = mutableMapOf<String, String>()
      signParams["apiKey"] = apiKey
      signParams["nonce"] = nonce
      signParams["signType"] = signConfig.signType

      // 添加API选项
      signConfig.apiOptions.forEach { (key, value) ->
        signParams[key] = value
      }

      // 添加公共参数
      val commonHeaders = makeCommonHeader()
      commonHeaders.forEach { (key, value) ->
        if (!existingParams.containsKey(key)) {
          signParams[key] = value
        }
      }

      // 合并所有参数用于签名
      val allParams = mutableMapOf<String, String>()
      allParams.putAll(existingParams)
      allParams.putAll(signParams)

      // 生成签名
      val sign = generateSign(allParams, signConfig.apiSecret, signConfig.signType)
      signParams["sign"] = sign

      // 添加所有参数到URL
      val finalUrl = UrlBuilder.addQueryParams(originalUrl, signParams)

      if (signConfig.enableDebug) {
        Log.d(TAG, "Built signed URL: $finalUrl")
      }

      finalUrl

    } catch (e: Exception) {
      Log.e(TAG, "Error building signed URL", e)
      originalUrl
    }
  }


  /**
   * 生成签名
   */
  fun generateSign(params: Map<String, String>, secret: String, signType: String): String {
    return when (signType.lowercase()) {
      "md5" -> generateMd5Sign(params, secret)
      "sha1" -> generateSha1Sign(params, secret)
      "sha256" -> generateSha256Sign(params, secret)
      "hmac-md5" -> generateHmacMd5Sign(params, secret)
      else -> generateMd5Sign(params, secret)
    }
  }

  /**
   * 生成MD5签名（原有方式）
   */
  private fun generateMd5Sign(params: Map<String, String>, secret: String): String {
    val sortedParams = params.toSortedMap()
    val signString = sortedParams.entries.joinToString("&") { "${it.key}=${it.value}" }
    val finalSignString = "$signString&key=$secret"
    Log.d(TAG, "MD5 Sign string: $finalSignString")
    return md5(finalSignString)
  }

  /**
   * 生成SHA1签名（原有方式）
   */
  private fun generateSha1Sign(params: Map<String, String>, secret: String): String {
    val sortedParams = params.toSortedMap()
    val signString = sortedParams.entries.joinToString("&") { "${it.key}=${it.value}" }
    val finalSignString = "$signString&key=$secret"
    Log.d(TAG, "SHA1 Sign string: $finalSignString")
    return sha1(finalSignString)
  }

  /**
   * 生成SHA256签名（原有方式）
   */
  private fun generateSha256Sign(params: Map<String, String>, secret: String): String {
    val sortedParams = params.toSortedMap()
    val signString = sortedParams.entries.joinToString("&") { "${it.key}=${it.value}" }
    val finalSignString = "$signString&key=$secret"
    Log.d(TAG, "SHA256 Sign string: $finalSignString")
    return sha256(finalSignString)
  }

  /**
   * 生成HMAC-MD5签名（参考您提供的实现）
   */
  private fun generateHmacMd5Sign(params: Map<String, String>, secret: String): String {
    // 1. 去除签名关键字Key
    val filteredParams = params.toMutableMap()
    filteredParams.remove("sign")

    // 2. 根据参数Key排序（顺序）
    val sortedParams = java.util.TreeMap<String, String>()
    sortedParams.putAll(filteredParams)

    // 3. 构造待签名的请求串
    val sortQueryStringTmp = StringBuilder()
    val iterator = sortedParams.keys.iterator()
    while (iterator.hasNext()) {
      val key = iterator.next()
      sortQueryStringTmp.append("&")
        .append(specialUrlEncode(key))
        .append("=")
        .append(filteredParams[key]?.let { specialUrlEncode(it) })
    }
    sortQueryStringTmp.append("&apiSecret=$secret") // apiSecret放最后，且不参与排序

    // 4. 去除第一个多余的&符号
    val sortedQueryString = sortQueryStringTmp.substring(1)
    val stringToSign = specialUrlEncode(sortedQueryString)

    Log.d(TAG, "HMAC-MD5 Sign string: $stringToSign")
    Log.d(TAG, "HMAC-MD5 Secret: $secret&")

    // 5. 使用HMAC-MD5签名
    return hmacMd5(stringToSign, "$secret&")
  }

  /**
   * 生成公共请求头参数
   */
  private fun makeCommonHeader(): Map<String, String> {
    return mapOf(
      "timestamp" to System.currentTimeMillis().toString(),
      "version" to "1.0",
      "platform" to "android"
    )
  }

  /**
   * MD5加密
   */
  private fun md5(input: String): String {
    val md = MessageDigest.getInstance("MD5")
    val digest = md.digest(input.toByteArray())
    return digest.joinToString("") { "%02x".format(it) }
  }

  /**
   * SHA1加密
   */
  private fun sha1(input: String): String {
    val md = MessageDigest.getInstance("SHA-1")
    val digest = md.digest(input.toByteArray())
    return digest.joinToString("") { "%02x".format(it) }
  }

  /**
   * SHA256加密
   */
  private fun sha256(input: String): String {
    val md = MessageDigest.getInstance("SHA-256")
    val digest = md.digest(input.toByteArray())
    return digest.joinToString("") { "%02x".format(it) }
  }

  /**
   * 特殊URL编码（参考您提供的实现）
   */
  private fun specialUrlEncode(value: String): String {
    // 根据您的实现，这里直接返回原值，不进行URL编码
    // 如果需要URL编码，可以取消注释下面的代码
    /*
    return URLEncoder.encode(value, "UTF-8")
        .replace("+", "%20")
        .replace("*", "%2A")
        .replace("%7E", "~")
    */
    return value
  }

  /**
   * HMAC-MD5签名
   */
  private fun hmacMd5(data: String, key: String): String {
    return try {
      val secretKeySpec = javax.crypto.spec.SecretKeySpec(key.toByteArray(), "HmacMD5")
      val mac = javax.crypto.Mac.getInstance("HmacMD5")
      mac.init(secretKeySpec)
      val digest = mac.doFinal(data.toByteArray())
      digest.joinToString("") { "%02x".format(it) }
    } catch (e: Exception) {
      Log.e(TAG, "HMAC-MD5 signing failed", e)
      // 降级到普通MD5
      md5(data + key)
    }
  }
}

/**
 * 兼容原有SignUtils的工具类
 */
object SignUtils {
  fun sign(params: Map<String, String>, secret: String): String {
    val signConfig = AsrSignConfig(apiSecret = secret, signType = "md5")
    return AsrUrlSigner.generateSign(params, secret, signConfig.signType)
  }
}

/**
 * 为AsrWebSocketParams添加签名支持的扩展函数
 */

/**
 * 构建带签名的URL
 * 支持的签名类型：md5, sha1, sha256, hmac-md5
 */
fun AsrWebSocketParams.toSignedUrl(
  baseUrl: String,
  apiKey: String,
  apiSecret: String,
  signType: String = "md5",
  apiOptions: Map<String, String> = emptyMap(),
  enableDebug: Boolean = false
): String {
  // 先构建基础URL
  val basicUrl = this.toUrl(baseUrl, enableDebug)

  // 创建签名配置
  val signConfig = AsrSignConfig(
    apiSecret = apiSecret,
    signType = signType,
    apiOptions = apiOptions
  )

  // 添加签名
  return AsrUrlSigner.signUrl(basicUrl, apiKey, signConfig)
}

/**
 * 构建带签名的URL并返回调试信息
 */
fun AsrWebSocketParams.toSignedUrlWithDebugInfo(
  baseUrl: String,
  apiKey: String,
  apiSecret: String,
  signType: String = "md5",
  apiOptions: Map<String, String> = emptyMap()
): Pair<String, String> {
  val basicUrl = this.toUrl(baseUrl, true)
  val signConfig = AsrSignConfig(apiSecret, signType, apiOptions)
  val signedUrl = AsrUrlSigner.signUrl(basicUrl, apiKey, signConfig)

  val debugInfo = buildString {
    appendLine("=== ASR Signed URL Debug Info ===")
    appendLine("Basic URL: $basicUrl")
    appendLine("API Key: ${apiKey.take(8)}...")
    appendLine("API Secret: ${apiSecret.take(8)}...")
    appendLine("Sign Type: $signType")
    appendLine("API Options: $apiOptions")
    appendLine("Final Signed URL: $signedUrl")
    appendLine("=================================")
  }

  return Pair(signedUrl, debugInfo)
}

/**
 * 使用签名配置对象的版本
 */
fun AsrWebSocketParams.toSignedUrl(
  baseUrl: String,
  apiKey: String,
  signConfig: AsrSignConfig,
  enableDebug: Boolean = false
): String {
  val basicUrl = this.toUrl(baseUrl, enableDebug)
  return AsrUrlSigner.signUrl(basicUrl, apiKey, signConfig)
}
