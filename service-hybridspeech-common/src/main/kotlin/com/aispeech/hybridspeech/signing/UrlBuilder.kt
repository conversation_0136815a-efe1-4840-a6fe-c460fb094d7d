package com.aispeech.hybridspeech.signing

import android.net.Uri
import android.util.Log
import java.net.URLEncoder

/**
 * URL构建工具 - 使用Android原生Uri替代OkHttp
 * 避免在common模块中引入OkHttp依赖
 */
object UrlBuilder {
  private const val TAG = "UrlBuilder"

  /**
   * 构建基础URL
   */
  fun buildUrl(
    baseUrl: String,
    path: String? = null,
    params: Map<String, String> = emptyMap()
  ): String {
    return try {
      var builder = Uri.parse(baseUrl).buildUpon()

      // 添加路径
      path?.let { pathStr ->
        val pathSegments = pathStr.split("/").filter { it.isNotEmpty() }
        pathSegments.forEach { segment ->
          builder = builder.appendPath(segment)
        }
      }

      // 添加查询参数
      params.forEach { (key, value) ->
        builder = builder.appendQueryParameter(key, value)
      }

      builder.build().toString()
    } catch (e: Exception) {
      Log.e(TAG, "Failed to build URL: $baseUrl", e)
      baseUrl // 返回原始URL作为降级
    }
  }

  /**
   * 解析URL获取查询参数
   */
  fun parseQueryParams(url: String): Map<String, String> {
    return try {
      val uri = Uri.parse(url)
      val params = mutableMapOf<String, String>()

      uri.queryParameterNames.forEach { name ->
        uri.getQueryParameter(name)?.let { value ->
          if (value.isNotEmpty()) {
            params[name] = value
          }
        }
      }

      params
    } catch (e: Exception) {
      Log.e(TAG, "Failed to parse query params from: $url", e)
      emptyMap()
    }
  }

  /**
   * 向现有URL添加查询参数
   */
  fun addQueryParams(url: String, newParams: Map<String, String>): String {
    return try {
      val uri = Uri.parse(url)
      var builder = uri.buildUpon()

      newParams.forEach { (key, value) ->
        builder = builder.appendQueryParameter(key, value)
      }

      builder.build().toString()
    } catch (e: Exception) {
      Log.e(TAG, "Failed to add query params to: $url", e)
      url
    }
  }

  /**
   * URL编码
   */
  fun urlEncode(value: String): String {
    return try {
      URLEncoder.encode(value, "UTF-8")
    } catch (e: Exception) {
      Log.e(TAG, "Failed to URL encode: $value", e)
      value
    }
  }

  /**
   * 特殊URL编码（用于签名）
   */
  fun specialUrlEncode(value: String): String {
    return try {
      URLEncoder.encode(value, "UTF-8")
        .replace("+", "%20")
        .replace("*", "%2A")
        .replace("%7E", "~")
    } catch (e: Exception) {
      Log.e(TAG, "Failed to special URL encode: $value", e)
      value
    }
  }

  /**
   * 验证URL格式
   */
  fun isValidUrl(url: String): Boolean {
    return try {
      val uri = Uri.parse(url)
      !uri.scheme.isNullOrEmpty() && !uri.host.isNullOrEmpty()
    } catch (e: Exception) {
      false
    }
  }

  /**
   * 验证WebSocket URL格式
   */
  fun isValidWebSocketUrl(url: String): Boolean {
    return try {
      val uri = Uri.parse(url)
      val scheme = uri.scheme?.lowercase()
      (scheme == "ws" || scheme == "wss") && !uri.host.isNullOrEmpty()
    } catch (e: Exception) {
      false
    }
  }
}

/**
 * ASR WebSocket URL构建器 - 使用Android Uri替代OkHttp
 */
class AsrWebSocketUrlBuilder(private val baseUrl: String) {
  companion object {
    private const val TAG = "AsrWebSocketUrlBuilder"
    private const val DEFAULT_PATH = "aitablet/asr/ws"
  }

  private val queryParams = mutableMapOf<String, String>()
  private var debugMode = false
  private var customPath: String? = null

  /**
   * 启用调试模式
   */
  fun enableDebug(): AsrWebSocketUrlBuilder {
    debugMode = true
    if (debugMode) Log.d(TAG, "Debug mode enabled for base URL: $baseUrl")
    return this
  }

  /**
   * 设置自定义路径
   */
  fun setPath(path: String): AsrWebSocketUrlBuilder {
    customPath = path
    if (debugMode) Log.d(TAG, "Custom path set: $path")
    return this
  }

  /**
   * 从参数对象批量添加参数
   */
  fun addParams(params: AsrWebSocketParams): AsrWebSocketUrlBuilder {
    if (debugMode) Log.d(TAG, "Adding params from AsrWebSocketParams object")

    addParam("requestId", params.requestId)
    addParam("userId", params.userId)
    addParam("recordId", params.recordId)
    addParam("offset", params.duration)
    addParam("language", params.language)
    addParam("translate", params.translate)
    addParam("useSpeechSpeaker", "0")
    addParam("audioType", params.audioType)
    addParam("extractAiNote", params.realtimeAgenda)
    addParam("audioId", params.objectId)
    addConditionalParam("modelId", params.selectAsrConfig, params.selectAsrConfig != "-1")
    addParam("enableSmooth", params.enableSmooth)
    addConditionalParam("aiNoteExtractNum", "50", params.simulateAction)

    return this
  }

  /**
   * 添加字符串参数
   */
  fun addParam(key: String, value: String?): AsrWebSocketUrlBuilder {
    value?.let {
      queryParams[key] = it
      if (debugMode) Log.d(TAG, "Added param: $key = $it")
    }
    return this
  }

  /**
   * 添加数字参数
   */
  fun addParam(key: String, value: Long): AsrWebSocketUrlBuilder {
    queryParams[key] = value.toString()
    if (debugMode) Log.d(TAG, "Added param: $key = $value")
    return this
  }

  /**
   * 添加布尔参数
   */
  fun addParam(key: String, value: Boolean): AsrWebSocketUrlBuilder {
    queryParams[key] = value.toString()
    if (debugMode) Log.d(TAG, "Added param: $key = $value")
    return this
  }

  /**
   * 添加条件参数
   */
  fun addConditionalParam(key: String, value: String?, condition: Boolean): AsrWebSocketUrlBuilder {
    if (condition) {
      value?.let {
        queryParams[key] = it
        if (debugMode) Log.d(TAG, "Added conditional param: $key = $it (condition: true)")
      }
    } else if (debugMode) {
      Log.d(TAG, "Skipped conditional param: $key (condition: false)")
    }
    return this
  }

  /**
   * 构建最终URL
   */
  fun build(): String {
    if (debugMode) {
      Log.d(TAG, "Building URL with base: $baseUrl")
      Log.d(TAG, "Path: ${customPath ?: DEFAULT_PATH}")
      Log.d(TAG, "Query params count: ${queryParams.size}")
    }

    val finalUrl = UrlBuilder.buildUrl(
      baseUrl = baseUrl,
      path = customPath ?: DEFAULT_PATH,
      params = queryParams
    )

    if (debugMode) Log.d(TAG, "Final URL: $finalUrl")
    return finalUrl
  }

  /**
   * 获取调试信息
   */
  fun getDebugInfo(): String {
    return buildString {
      appendLine("=== ASR WebSocket URL Debug Info ===")
      appendLine("Base URL: $baseUrl")
      appendLine("Path: ${customPath ?: DEFAULT_PATH}")
      appendLine("Query Parameters (${queryParams.size}):")
      queryParams.forEach { (key, value) ->
        appendLine("  $key = $value")
      }
      appendLine("Final URL: ${build()}")
      appendLine("=====================================")
    }
  }

  /**
   * 清空所有参数
   */
  fun clear(): AsrWebSocketUrlBuilder {
    queryParams.clear()
    customPath = null
    if (debugMode) Log.d(TAG, "Cleared all parameters")
    return this
  }
}

/**
 * AsrWebSocketParams 扩展函数
 */
fun AsrWebSocketParams.toUrl(baseUrl: String, enableDebug: Boolean = false): String {
  return AsrWebSocketUrlBuilder(baseUrl)
    .apply { if (enableDebug) enableDebug() }
    .addParams(this)
    .build()
}

fun AsrWebSocketParams.toUrlWithDebugInfo(baseUrl: String): Pair<String, String> {
  val builder = AsrWebSocketUrlBuilder(baseUrl).enableDebug().addParams(this)
  return Pair(builder.build(), builder.getDebugInfo())
}
