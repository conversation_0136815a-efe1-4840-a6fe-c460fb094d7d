package com.aispeech.hybridspeech.signing

import android.util.Log
import com.aispeech.hybridspeech.AsrResumeConfig
import com.aispeech.hybridspeech.NetworkConfig
import com.aispeech.hybridspeech.NetworkConfigRequest
import com.aispeech.hybridspeech.WebSocketConfig
import java.util.UUID

/**
 * 统一签名管理器
 * 管理所有请求的签名配置和逻辑
 */
class HybridSpeechSignManager private constructor() {
  companion object {
    private const val TAG = "HybridSpeechSignManager"

    @Volatile
    private var INSTANCE: HybridSpeechSignManager? = null

    fun getInstance(): HybridSpeechSignManager {
      return INSTANCE ?: synchronized(this) {
        INSTANCE ?: HybridSpeechSignManager().also { INSTANCE = it }
      }
    }
  }

  // 签名配置缓存
  private var defaultSignConfig: AsrSignConfig? = null
  private var apiCredentials: ApiCredentials? = null
  private var commonHeaders: Map<String, String> = emptyMap()
  private var environment: String = "prod"

  /**
   * 初始化签名管理器
   */
  fun initialize(
    apiKey: String,
    apiSecret: String,
    signType: String = "HMAC-MD5",
    environment: String = "prod"
  ) {
    this.environment = environment
    this.apiCredentials = ApiCredentials(apiKey, apiSecret)
    this.defaultSignConfig = AsrSignConfig(
      apiSecret = apiSecret,
      signType = signType,
      enableDebug = environment != "prod"
    )
    this.commonHeaders = generateCommonHeaders()

    Log.i(TAG, "SignManager initialized with signType: $signType, environment: $environment")
  }

  /**
   * 为 ASR WebSocket 生成签名 URL
   */
  fun signAsrWebSocketUrl(
    params: AsrWebSocketParams,
    baseUrl: String,
    customOptions: Map<String, String> = emptyMap()
  ): String {
    requireInitialized()

    val finalOptions = commonHeaders + customOptions
    val signConfig = defaultSignConfig!!.copy(apiOptions = finalOptions)

    return params.toSignedUrl(
      baseUrl = baseUrl,
      apiKey = apiCredentials!!.apiKey,
      signConfig = signConfig
    )
  }



  /**
   * 通用签名方法，支持任意参数
   */
  fun signGenericUrl(
    originalUrl: String,
    additionalParams: Map<String, String> = emptyMap(),
    customOptions: Map<String, String> = emptyMap()
  ): String {
    requireInitialized()

    val finalOptions = commonHeaders + customOptions + additionalParams
    val signConfig = defaultSignConfig!!.copy(apiOptions = finalOptions)

    return AsrUrlSigner.signUrl(originalUrl, apiCredentials!!.apiKey, signConfig)
  }

  /**
   * 生成统一网络配置
   * 签名器不区分初次连接还是续传连接，只负责根据传入参数生成签名URL
   * 业务层决定传递什么参数
   */
  fun generateNetworkConfig(
    request: NetworkConfigRequest,
    baseUrl: String,
    customOptions: Map<String, String> = emptyMap()
  ): NetworkConfig {
    requireInitialized()

    // 构建WebSocket参数，使用请求中的所有参数
    val asrParams = AsrWebSocketParams(
      requestId = UUID.randomUUID().toString(),
      userId = request.userId,
      recordId = request.recordId,
      duration = request.duration,
      language = request.language,
      translate = request.translate,
      audioType = request.audioType,
      realtimeAgenda = request.enableRealtimeAgenda,
      objectId = "obj_${UUID.randomUUID()}",
      selectAsrConfig = "-1",
      enableSmooth = true,
      simulateAction = true
    )

    // 生成签名URL
    val signedUrl = signAsrWebSocketUrl(asrParams, baseUrl, customOptions)
    val websocketConfig = WebSocketConfig(
      signedUrl = signedUrl,
      apiKey = apiCredentials!!.apiKey
    )

    // 如果是续传请求，同时生成AsrResumeConfig
    val asrResumeConfig = if (request.isResume) {
      AsrResumeConfig(
        resumeWebSocketUrl = signedUrl,
        sessionId = request.sessionId ?: "",
        resumeFromOffset = request.resumeFromOffset
      )
    } else null

    return NetworkConfig(
      websocketConfig = websocketConfig,
      asrResumeConfig = asrResumeConfig,
      timeout = 30000L,
      retryCount = 3,
      headers = getDefaultHeaders()
    )
  }

  /**
   * 获取默认请求头
   */
  private fun getDefaultHeaders(): Map<String, String> {
    return mapOf(
      "User-Agent" to "HybridSpeech-Android/1.0",
      "X-Client-Type" to "Android",
      "X-Sign-Version" to "1.0"
    )
  }



  private fun generateCommonHeaders(): Map<String, String> {
    return mapOf(
      "timestamp" to System.currentTimeMillis().toString(),
      "version" to "1.0",
      "platform" to "android",
      "device_id" to getDeviceId()
    )
  }

  private fun getDeviceId(): String {
    // 获取设备ID的逻辑
    return "android_device_${System.currentTimeMillis()}"
  }

  private fun requireInitialized() {
    check(apiCredentials != null && defaultSignConfig != null) {
      "HybridSpeechSignManager must be initialized before use"
    }
  }
}

/**
 * API 凭据数据类
 */
data class ApiCredentials(
  val apiKey: String,
  val apiSecret: String
)

 