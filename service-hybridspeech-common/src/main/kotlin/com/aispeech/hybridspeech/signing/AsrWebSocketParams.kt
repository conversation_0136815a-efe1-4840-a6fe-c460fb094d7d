package com.aispeech.hybridspeech.signing

import android.os.Parcel
import android.os.Parcelable
import kotlinx.serialization.Serializable

/**
 * ASR WebSocket URL 参数数据类
 * 移动到 common 模块，供内部和外部使用
 */
@Serializable
data class AsrWebSocketParams(
  val requestId: String,
  val userId: String,
  val recordId: Long,
  val duration: Long = 0L,
  val language: String = "zh-CN",
  val translate: String? = null,
  val audioType: String = "ogg-opus",
  val realtimeAgenda: Boolean = false,
  val objectId: String,
  val selectAsrConfig: String = "-1",
  val enableSmooth: Boolean = true,
  val simulateAction: Boolean = false
) : Parcelable {

  constructor(parcel: Parcel) : this(
    requestId = parcel.readString() ?: "",
    userId = parcel.readString() ?: "",
    recordId = parcel.readLong(),
    duration = parcel.readLong(),
    language = parcel.readString() ?: "zh-CN",
    translate = parcel.readString(),
    audioType = parcel.readString() ?: "ogg-opus",
    realtimeAgenda = parcel.readByte() != 0.toByte(),
    objectId = parcel.readString() ?: "",
    selectAsrConfig = parcel.readString() ?: "-1",
    enableSmooth = parcel.readByte() != 0.toByte(),
    simulateAction = parcel.readByte() != 0.toByte()
  )

  override fun writeToParcel(parcel: Parcel, flags: Int) {
    parcel.writeString(requestId)
    parcel.writeString(userId)
    parcel.writeLong(recordId)
    parcel.writeLong(duration)
    parcel.writeString(language)
    parcel.writeString(translate)
    parcel.writeString(audioType)
    parcel.writeByte(if (realtimeAgenda) 1 else 0)
    parcel.writeString(objectId)
    parcel.writeString(selectAsrConfig)
    parcel.writeByte(if (enableSmooth) 1 else 0)
    parcel.writeByte(if (simulateAction) 1 else 0)
  }

  override fun describeContents(): Int = 0

  companion object CREATOR : Parcelable.Creator<AsrWebSocketParams> {
    override fun createFromParcel(parcel: Parcel): AsrWebSocketParams {
      return AsrWebSocketParams(parcel)
    }

    override fun newArray(size: Int): Array<AsrWebSocketParams?> {
      return arrayOfNulls(size)
    }
  }
}

/**
 * 签名配置
 */
@Serializable
data class AsrSignConfig(
  val apiSecret: String,
  val signType: String = "HMAC-MD5",
  val apiOptions: Map<String, String> = emptyMap(),
  val enableDebug: Boolean = false
) : Parcelable {

  constructor(parcel: Parcel) : this(
    apiSecret = parcel.readString() ?: "",
    signType = parcel.readString() ?: "HMAC-MD5",
    apiOptions = parcel.readSerializable() as? Map<String, String> ?: emptyMap(),
    enableDebug = parcel.readByte() != 0.toByte()
  )

  override fun writeToParcel(parcel: Parcel, flags: Int) {
    parcel.writeString(apiSecret)
    parcel.writeString(signType)
    parcel.writeSerializable(HashMap(apiOptions))
    parcel.writeByte(if (enableDebug) 1 else 0)
  }

  override fun describeContents(): Int = 0

  companion object CREATOR : Parcelable.Creator<AsrSignConfig> {
    override fun createFromParcel(parcel: Parcel): AsrSignConfig {
      return AsrSignConfig(parcel)
    }

    override fun newArray(size: Int): Array<AsrSignConfig?> {
      return arrayOfNulls(size)
    }

    /**
     * 创建默认签名配置
     */
    fun createDefault(apiSecret: String): AsrSignConfig {
      return AsrSignConfig(
        apiSecret = apiSecret,
        signType = "HMAC-MD5"
      )
    }

    /**
     * 创建调试签名配置
     */
    fun createDebug(apiSecret: String, signType: String = "HMAC-MD5"): AsrSignConfig {
      return AsrSignConfig(
        apiSecret = apiSecret,
        signType = signType,
        enableDebug = true
      )
    }
  }
}
