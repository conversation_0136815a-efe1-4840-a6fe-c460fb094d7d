package com.aispeech.hybridspeech

import android.os.Parcel
import android.os.Parcelable
import kotlinx.serialization.Serializable

/**
 * 统一网络配置请求（简化版）
 * 包含WebSocket和ASR识别续传所需的核心信息
 */
@Serializable
data class NetworkConfigRequest(
  // 基础信息
  val recordId: Long,
  val userId: String,

  // ASR WebSocket 相关
  val language: String = "cn",
  val audioType: String = "ogg_opus",
  val translate: String? = null, // 翻译目标语言，可空
  val enableRealtimeAgenda: Boolean = true,
  val duration: Long = 0L,

  // ASR识别续传相关
  val isResume: Boolean = false,
  val sessionId: String? = null, // 会话ID，用于续传时关联
  val resumeFromOffset: Long = 0L, // 从音频流的哪个位置开始续传（毫秒）
  val resumeFromMp3ChunkIndex: Long? = null // MP3模式下从第几个chunk开始续传
) : Parcelable {

  constructor(parcel: Parcel) : this(
    recordId = parcel.readLong(),
    userId = parcel.readString() ?: "",
    language = parcel.readString() ?: "cn",
    audioType = parcel.readString() ?: "ogg_opus",
    translate = parcel.readString(),
    enableRealtimeAgenda = parcel.readByte() != 0.toByte(),
    duration = parcel.readLong(),
    isResume = parcel.readByte() != 0.toByte(),
    sessionId = parcel.readString(),
    resumeFromOffset = parcel.readLong(),
    resumeFromMp3ChunkIndex = parcel.readValue(Long::class.java.classLoader) as? Long,
  )

  override fun writeToParcel(parcel: Parcel, flags: Int) {
    parcel.writeLong(recordId)
    parcel.writeString(userId)
    parcel.writeString(language)
    parcel.writeString(audioType)
    parcel.writeString(translate)
    parcel.writeByte(if (enableRealtimeAgenda) 1 else 0)
    parcel.writeLong(duration)
    parcel.writeByte(if (isResume) 1 else 0)
    parcel.writeString(sessionId)
    parcel.writeLong(resumeFromOffset)
    parcel.writeValue(resumeFromMp3ChunkIndex)
  }

  override fun describeContents(): Int = 0

  companion object CREATOR : Parcelable.Creator<NetworkConfigRequest> {
    override fun createFromParcel(parcel: Parcel): NetworkConfigRequest {
      return NetworkConfigRequest(parcel)
    }

    override fun newArray(size: Int): Array<NetworkConfigRequest?> {
      return arrayOfNulls(size)
    }

    // 便捷创建方法
    fun createAsrWebSocketRequest(
      recordId: Long,
      userId: String,
      language: String = "cn",
      audioType: String = "ogg_opus",
      translate: String? = null,
      enableRealtimeAgenda: Boolean = true,
      duration: Long = 0L
    ): NetworkConfigRequest {
      return NetworkConfigRequest(
        recordId = recordId,
        userId = userId,
        language = language,
        audioType = audioType,
        translate = translate,
        enableRealtimeAgenda = enableRealtimeAgenda,
        duration = duration
      )
    }

    fun createAsrResumeRequest(
      recordId: Long,
      userId: String,
      sessionId: String,
      resumeFromOffset: Long,
      language: String = "cn",
      audioType: String = "ogg_opus",
      translate: String? = null,
      resumeFromMp3ChunkIndex: Long? = null
    ): NetworkConfigRequest {
      return NetworkConfigRequest(
        recordId = recordId,
        userId = userId,
        language = language,
        audioType = audioType,
        translate = translate,
        isResume = true,
        sessionId = sessionId,
        resumeFromOffset = resumeFromOffset,
        duration = resumeFromOffset,
        resumeFromMp3ChunkIndex = resumeFromMp3ChunkIndex
      )
    }

    /**
     * 创建智能续传请求（根据音频类型自动选择续传方式）
     * @param recordId 录音ID
     * @param userId 用户ID
     * @param sessionId 会话ID
     * @param resumeFromOffset 时间偏移量（毫秒）
     * @param resumeFromMp3ChunkIndex MP3 chunk 索引（仅MP3模式使用）
     * @param language 语言
     * @param audioType 音频类型
     * @param translate 翻译目标语言
     */
    fun createSmartResumeRequest(
      recordId: Long,
      userId: String,
      sessionId: String,
      resumeFromOffset: Long,
      resumeFromMp3ChunkIndex: Long? = null,
      language: String = "cn",
      audioType: String = "ogg_opus",
      translate: String? = null
    ): NetworkConfigRequest {
      // 根据音频类型决定使用哪种续传方式
      val mp3ChunkIndex = if (audioType.lowercase().contains("mp3")) {
        resumeFromMp3ChunkIndex
      } else {
        null
      }

      return NetworkConfigRequest(
        recordId = recordId,
        userId = userId,
        language = language,
        audioType = audioType,
        translate = translate,
        isResume = true,
        sessionId = sessionId,
        resumeFromOffset = resumeFromOffset,
        duration = resumeFromOffset,
        resumeFromMp3ChunkIndex = mp3ChunkIndex
      )
    }
  }
}

/**
 * 统一网络配置响应
 * 同时包含WebSocket配置和ASR识别续传配置
 */
@Serializable
data class NetworkConfig(
  // WebSocket 配置
  val websocketConfig: WebSocketConfig? = null,

  // ASR识别续传配置
  val asrResumeConfig: AsrResumeConfig? = null,

  // 通用配置
  val timeout: Long = 30000L,
  val retryCount: Int = 3,
  val headers: Map<String, String> = emptyMap()
) : Parcelable {

  constructor(parcel: Parcel) : this(
    websocketConfig = parcel.readParcelable(WebSocketConfig::class.java.classLoader),
    asrResumeConfig = parcel.readParcelable(AsrResumeConfig::class.java.classLoader),
    timeout = parcel.readLong(),
    retryCount = parcel.readInt(),
    headers = parcel.readSerializable() as? Map<String, String> ?: emptyMap()
  )

  override fun writeToParcel(parcel: Parcel, flags: Int) {
    parcel.writeParcelable(websocketConfig, flags)
    parcel.writeParcelable(asrResumeConfig, flags)
    parcel.writeLong(timeout)
    parcel.writeInt(retryCount)
    parcel.writeSerializable(HashMap(headers))
  }

  override fun describeContents(): Int = 0

  companion object CREATOR : Parcelable.Creator<NetworkConfig> {
    override fun createFromParcel(parcel: Parcel): NetworkConfig {
      return NetworkConfig(parcel)
    }

    override fun newArray(size: Int): Array<NetworkConfig?> {
      return arrayOfNulls(size)
    }
  }
}

/**
 * WebSocket 配置
 */
@Serializable
data class WebSocketConfig(
  val signedUrl: String,
  val apiKey: String = "",
  val protocols: List<String> = emptyList()
) : Parcelable {

  constructor(parcel: Parcel) : this(
    signedUrl = parcel.readString() ?: "",
    apiKey = parcel.readString() ?: "",
    protocols = parcel.createStringArrayList() ?: emptyList()
  )

  override fun writeToParcel(parcel: Parcel, flags: Int) {
    parcel.writeString(signedUrl)
    parcel.writeString(apiKey)
    parcel.writeStringList(protocols)
  }

  override fun describeContents(): Int = 0

  companion object CREATOR : Parcelable.Creator<WebSocketConfig> {
    override fun createFromParcel(parcel: Parcel): WebSocketConfig {
      return WebSocketConfig(parcel)
    }

    override fun newArray(size: Int): Array<WebSocketConfig?> {
      return arrayOfNulls(size)
    }
  }
}

/**
 * ASR识别续传配置（简化版）
 * 只保留实际使用的核心字段
 */
@Serializable
data class AsrResumeConfig(
  val resumeWebSocketUrl: String, // 续传用的WebSocket URL
  val sessionId: String, // 会话ID
  val resumeFromOffset: Long, // 从音频流的哪个位置开始续传（毫秒）
  val resumeFromMp3ChunkIndex: Int? = null // MP3模式下从第几个chunk开始续传
) : Parcelable {

  constructor(parcel: Parcel) : this(
    resumeWebSocketUrl = parcel.readString() ?: "",
    sessionId = parcel.readString() ?: "",
    resumeFromOffset = parcel.readLong(),
    resumeFromMp3ChunkIndex = parcel.readValue(Int::class.java.classLoader) as? Int
  )

  override fun writeToParcel(parcel: Parcel, flags: Int) {
    parcel.writeString(resumeWebSocketUrl)
    parcel.writeString(sessionId)
    parcel.writeLong(resumeFromOffset)
    parcel.writeValue(resumeFromMp3ChunkIndex)
  }

  override fun describeContents(): Int = 0

  companion object CREATOR : Parcelable.Creator<AsrResumeConfig> {
    override fun createFromParcel(parcel: Parcel): AsrResumeConfig {
      return AsrResumeConfig(parcel)
    }

    override fun newArray(size: Int): Array<AsrResumeConfig?> {
      return arrayOfNulls(size)
    }
  }
} 