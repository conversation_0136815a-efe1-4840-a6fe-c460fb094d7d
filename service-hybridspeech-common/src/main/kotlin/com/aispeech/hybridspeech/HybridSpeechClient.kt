package com.aispeech.hybridspeech

import android.content.ComponentName
import android.content.Context
import android.content.Intent
import android.content.ServiceConnection
import android.content.pm.ApplicationInfo
import android.content.pm.PackageManager
import android.os.IBinder
import android.os.RemoteException
import android.util.Log
import kotlinx.coroutines.*
import kotlinx.coroutines.channels.awaitClose
import kotlinx.coroutines.flow.*
import java.util.concurrent.atomic.AtomicBoolean
import kotlin.coroutines.resume
import kotlin.coroutines.resumeWithException

/**
 * 混合语音识别服务通用客户端
 * 
 * 提供开箱即用的服务连接、重试机制和异步操作支持
 * 
 * 主要功能：
 * - 自动服务绑定和重试
 * - 应用激活检测
 * - 协程化异步接口
 * - 连接状态监控
 * - 统一错误处理
 * 
 * 使用示例：
 * ```kotlin
 * val client = HybridSpeechClient(context)
 * 
 * // 连接服务
 * if (client.connectAndWait()) {
 *     // 注册回调
 *     client.registerTranscriptionCallback(callback)
 *     
 *     // 开始录音
 *     client.startRecordingAsync(config)
 * }
 * ```
 */
class HybridSpeechClient(
    private val context: Context,
    private val servicePackage: String = DEFAULT_SERVICE_PACKAGE,
    private val serviceClass: String = DEFAULT_SERVICE_CLASS
) {
    
    companion object {
        private const val TAG = "HybridSpeechClient"
        private const val DEFAULT_SERVICE_PACKAGE = "com.aispeech.tablet.service.hybridspeech"
        private const val DEFAULT_SERVICE_CLASS = "com.aispeech.tablet.service.hybridspeech.HybridSpeechService"
        private const val MAX_RETRY_COUNT = 3
        private const val BINDING_TIMEOUT = 5000L
        private const val DEFAULT_PROGRESS_INTERVAL = 1000
    }
    
    /**
     * 录音进度状态
     */
    sealed class RecordingProgressState {
        data object Started : RecordingProgressState()
        data class Progress(val durationMs: Long) : RecordingProgressState()
        data class Stopped(val totalDurationMs: Long) : RecordingProgressState()
        data class Error(val message: String) : RecordingProgressState()
    }
    
    // 服务相关
    private var hybridSpeechService: IHybridSpeechService? = null
    private var transcriptionCallback: ITranscriptionCallback? = null
    private var configProvider: IHybridSpeechConfigProvider? = null
    
    // 连接状态
    private val _connectionState = MutableStateFlow(false)
    val connectionState: StateFlow<Boolean> = _connectionState.asStateFlow()
    
    // 协程作用域
    private val clientScope = CoroutineScope(SupervisorJob() + Dispatchers.Main)
    
    // 重试相关变量
    private val bindingFlag = AtomicBoolean(false)
    private var bindingRetryCount = MAX_RETRY_COUNT
    private var bindingTimeoutJob: Job? = null
    
    /**
     * 检查服务连接状态
     */
    private fun requireServiceConnected(): IHybridSpeechService {
        return hybridSpeechService ?: throw IllegalStateException("Service not connected")
    }
    
    /**
     * 统一远程调用异常处理
     */
    private fun <T> executeRemoteCall(operation: String, action: () -> T): T {
        return try {
            action()
        } catch (e: RemoteException) {
            Log.e(TAG, "Remote call failed for $operation", e)
            throw e
        }
    }
    
    /**
     * 服务连接回调
     */
    private val serviceConnection = object : ServiceConnection {
        override fun onServiceConnected(name: ComponentName?, service: IBinder?) {
            Log.d(TAG, "Service connected: $name")
            
            // 取消超时任务
            bindingTimeoutJob?.cancel()
            bindingTimeoutJob = null
            
            bindingFlag.set(false)
            bindingRetryCount = MAX_RETRY_COUNT // 重置重试计数
            
            hybridSpeechService = IHybridSpeechService.Stub.asInterface(service)
            _connectionState.value = true
            
            // 重新注册回调
            reregisterCallbacks()
        }
        
        override fun onServiceDisconnected(name: ComponentName?) {
            Log.w(TAG, "Service disconnected: $name")
            hybridSpeechService = null
            _connectionState.value = false
            bindingFlag.set(false)
            
            // 取消任何正在进行的超时任务
            bindingTimeoutJob?.cancel()
            bindingTimeoutJob = null
        }
    }
    
    /**
     * 重新注册回调
     */
    private fun reregisterCallbacks() {
        // 重新注册转写回调
        transcriptionCallback?.let { callback ->
            runCatching {
                hybridSpeechService?.registerCallback(callback)
                Log.d(TAG, "Transcription callback re-registered")
            }.onFailure { e ->
                Log.e(TAG, "Failed to re-register transcription callback", e)
            }
        }
        
        // 重新注册配置提供者
        configProvider?.let { provider ->
            runCatching {
                hybridSpeechService?.registerConfigProvider(provider)
                Log.d(TAG, "Config provider re-registered")
            }.onFailure { e ->
                Log.e(TAG, "Failed to re-register config provider", e)
            }
        }
    }
    
    /**
     * 检查应用是否处于 stopped 状态
     */
    private fun isPackageStopped(packageName: String): Boolean {
        return try {
            val packageManager = context.packageManager
            val applicationInfo = packageManager.getApplicationInfo(packageName, PackageManager.GET_META_DATA)
            
            // 检查 ApplicationInfo.FLAG_STOPPED 标志
            val isStopped = (applicationInfo.flags and ApplicationInfo.FLAG_STOPPED) != 0
            Log.d(TAG, "Package $packageName stopped state: $isStopped")
            isStopped
        } catch (e: PackageManager.NameNotFoundException) {
            Log.w(TAG, "Package $packageName not found")
            false
        } catch (e: Exception) {
            Log.e(TAG, "Error checking package stopped state", e)
            false
        }
    }
    
    /**
     * 尝试激活应用包
     */
    private fun ensurePackageActive(packageName: String): Boolean {
        if (!isPackageStopped(packageName)) {
            Log.d(TAG, "Package $packageName is not stopped, no activation needed")
            return true
        }
        
        return try {
            Log.d(TAG, "Attempting to activate package: $packageName")
            val intent = Intent().apply {
                setPackage(packageName)
                addFlags(Intent.FLAG_INCLUDE_STOPPED_PACKAGES)
                addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            }
            
            context.sendBroadcast(intent)
            
            // 短暂等待后重新检查
            Thread.sleep(500)
            val stillStopped = isPackageStopped(packageName)
            Log.d(TAG, "Package $packageName activation result: ${!stillStopped}")
            !stillStopped
        } catch (e: Exception) {
            Log.e(TAG, "Failed to activate package $packageName", e)
            false
        }
    }
    
    /**
     * 尝试绑定服务（带重试机制）
     */
    private fun tryBindService(): Boolean {
        if (bindingFlag.getAndSet(true)) {
            Log.d(TAG, "Service binding already in progress")
            return false
        }
        
        if (bindingRetryCount <= 0) {
            Log.e(TAG, "Service binding retry count exhausted")
            bindingFlag.set(false)
            return false
        }
        
        bindingRetryCount--
        Log.d(TAG, "Attempting to bind service, retries left: $bindingRetryCount")
        
        return try {
            // 确保目标应用处于活跃状态
            if (!ensurePackageActive(servicePackage)) {
                Log.w(TAG, "Failed to activate target package: $servicePackage")
            }
            
            val intent = Intent().apply {
                component = ComponentName(servicePackage, serviceClass)
            }
            
            val bindResult = context.bindService(
                intent,
                serviceConnection,
                Context.BIND_AUTO_CREATE or Context.BIND_IMPORTANT
            )
            
            if (bindResult) {
                Log.d(TAG, "Service binding initiated successfully")
                
                // 设置超时任务
                bindingTimeoutJob = clientScope.launch {
                    delay(BINDING_TIMEOUT)
                    if (!_connectionState.value && bindingFlag.get()) {
                        Log.w(TAG, "Service binding timeout, attempting retry")
                        context.unbindService(serviceConnection)
                        bindingFlag.set(false)
                        
                        // 如果还有重试次数，继续尝试
                        if (bindingRetryCount > 0) {
                            delay(1000) // 等待1秒后重试
                            tryBindService()
                        } else {
                            Log.e(TAG, "Service binding failed after all retries")
                        }
                    }
                }
            } else {
                Log.e(TAG, "Failed to initiate service binding")
                bindingFlag.set(false)
            }
            
            bindResult
        } catch (e: Exception) {
            Log.e(TAG, "Exception during service binding", e)
            bindingFlag.set(false)
            false
        }
    }
    
    /**
     * 连接服务
     * @return 是否成功启动连接过程
     */
    fun connect(): Boolean {
        if (_connectionState.value) {
            Log.d(TAG, "Service already connected")
            return true
        }
        
        bindingRetryCount = MAX_RETRY_COUNT
        return tryBindService()
    }
    
    /**
     * 连接服务并等待连接完成
     * @param timeoutMs 超时时间，默认5秒
     * @return 是否连接成功
     */
    suspend fun connectAndWait(timeoutMs: Long = 5000): Boolean {
        if (_connectionState.value) {
            Log.d(TAG, "Service already connected")
            return true
        }
        
        if (!connect()) {
            return false
        }
        
        return try {
            withTimeout(timeoutMs) {
                connectionState.first { it }
            }
        } catch (e: TimeoutCancellationException) {
            Log.e(TAG, "Connection timeout after ${timeoutMs}ms")
            false
        }
    }

    /**
     * 断开服务连接
     */
    fun disconnect() {
        if (_connectionState.value) {
            try {
                context.unbindService(serviceConnection)
                Log.d(TAG, "Service unbound successfully")
            } catch (e: Exception) {
                Log.e(TAG, "Error unbinding service", e)
            }
        }

        hybridSpeechService = null
        transcriptionCallback = null
        configProvider = null
        _connectionState.value = false
        bindingFlag.set(false)

        // 取消超时任务
        bindingTimeoutJob?.cancel()
        bindingTimeoutJob = null

        // 取消所有协程
        clientScope.cancel()
    }

    /**
     * 异步开始录音
     */
    suspend fun startRecordingAsync(config: RecordingConfig) {
        val service = requireServiceConnected()

        return suspendCancellableCoroutine { continuation ->
            val callback = object : IStartRecordingCallback.Stub() {
                override fun onStartRecordingSuccess() {
                    Log.d(TAG, "Recording started successfully")
                    if (continuation.isActive) {
                        continuation.resume(Unit)
                    }
                }

                override fun onStartRecordingError(errorCode: Int, errorMessage: String?) {
                    Log.e(TAG, "Recording start failed: errorCode=$errorCode, message=$errorMessage")
                    if (continuation.isActive) {
                        val error = RuntimeException("Recording failed: errorCode=$errorCode, message=$errorMessage")
                        continuation.resumeWithException(error)
                    }
                }
            }

            executeRemoteCall("startRecordingWithConfigAsync") {
                service.startRecordingWithConfigAsync(config, callback)
                Log.d(TAG, "Start recording request sent")
            }
        }
    }

    /**
     * 异步停止录音并获取结果
     */
    suspend fun stopRecordingWithResultAsync(): RecordingResultInfo = suspendCancellableCoroutine { continuation ->
        val service = requireServiceConnected()

        val callback = object : IStopRecordingCallback.Stub() {
            override fun onStopRecordingSuccess(result: RecordingResultInfo?) {
                Log.d(TAG, "Recording stopped successfully")
                if (continuation.isActive) {
                    continuation.resume(result ?: RecordingResultInfo(
                        pcmFilePath = "",
                        mp3FilePath = "",
                        durationMs = 0L,
                        fileSizeBytes = 0L,
                        audioConfig = AudioRecordingConfig()
                    ))
                }
            }

            override fun onStopRecordingError(errorCode: Int, errorMessage: String?) {
                Log.e(TAG, "Recording stop failed: errorCode=$errorCode, message=$errorMessage")
                if (continuation.isActive) {
                    val error = RuntimeException("Stop recording failed: errorCode=$errorCode, message=$errorMessage")
                    continuation.resumeWithException(error)
                }
            }
        }

        executeRemoteCall("stopRecordingWithResultAsync") {
            service.stopRecordingWithResultAsync(callback)
            Log.d(TAG, "Stop recording request sent")
        }
    }

    /**
     * 异步暂停录音
     */
    suspend fun pauseRecordingAsync() {
        val service = requireServiceConnected()

        return suspendCancellableCoroutine { continuation ->
            val callback = object : IPauseRecordingCallback.Stub() {
                override fun onPauseRecordingSuccess() {
                    Log.d(TAG, "Recording paused successfully")
                    if (continuation.isActive) {
                        continuation.resume(Unit)
                    }
                }

                override fun onPauseRecordingError(errorCode: Int, errorMessage: String?) {
                    Log.e(TAG, "Recording pause failed: errorCode=$errorCode, message=$errorMessage")
                    if (continuation.isActive) {
                        val error = RuntimeException("Pause recording failed: errorCode=$errorCode, message=$errorMessage")
                        continuation.resumeWithException(error)
                    }
                }
            }

            executeRemoteCall("pauseRecordingAsync") {
                service.pauseRecordingAsync(callback)
                Log.d(TAG, "Pause recording request sent")
            }
        }
    }

    /**
     * 异步恢复录音
     */
    suspend fun resumeRecordingAsync() {
        val service = requireServiceConnected()

        return suspendCancellableCoroutine { continuation ->
            val callback = object : IResumeRecordingCallback.Stub() {
                override fun onResumeRecordingSuccess() {
                    Log.d(TAG, "Recording resumed successfully")
                    if (continuation.isActive) {
                        continuation.resume(Unit)
                    }
                }

                override fun onResumeRecordingError(errorCode: Int, errorMessage: String?) {
                    Log.e(TAG, "Recording resume failed: errorCode=$errorCode, message=$errorMessage")
                    if (continuation.isActive) {
                        val error = RuntimeException("Resume recording failed: errorCode=$errorCode, message=$errorMessage")
                        continuation.resumeWithException(error)
                    }
                }
            }

            executeRemoteCall("resumeRecordingAsync") {
                service.resumeRecordingAsync(callback)
                Log.d(TAG, "Resume recording request sent")
            }
        }
    }

    /**
     * 使用配置提供者开始录音
     * @param config 录音配置
     */
    suspend fun startRecordingWithProvider(config: RecordingConfig): Unit = suspendCancellableCoroutine { continuation ->
        val service = requireServiceConnected()

        val callback = object : IStartRecordingCallback.Stub() {
            override fun onStartRecordingSuccess() {
                Log.d(TAG, "Recording with provider started successfully")
                if (continuation.isActive) {
                    continuation.resume(Unit)
                }
            }

            override fun onStartRecordingError(errorCode: Int, errorMessage: String?) {
                Log.e(TAG, "Recording with provider start failed: errorCode=$errorCode, message=$errorMessage")
                if (continuation.isActive) {
                    val error = RuntimeException("Recording with provider failed: errorCode=$errorCode, message=$errorMessage")
                    continuation.resumeWithException(error)
                }
            }
        }

        executeRemoteCall("startRecordingWithProvider") {
            service.startRecordingWithProvider(config, callback)
            Log.d(TAG, "Start recording with provider request sent")
        }
    }

    /**
     * 注册转写结果回调
     */
    fun registerTranscriptionCallback(callback: ITranscriptionCallback) {
        transcriptionCallback = callback
        if (_connectionState.value) {
            runCatching {
                hybridSpeechService?.registerCallback(callback)
                Log.d(TAG, "Transcription callback registered successfully")
            }.onFailure { e ->
                Log.e(TAG, "Failed to register transcription callback", e)
            }
        }
    }

    /**
     * 取消注册转写结果回调
     */
    fun unregisterTranscriptionCallback() {
        if (_connectionState.value && transcriptionCallback != null) {
            runCatching {
                hybridSpeechService?.unregisterCallback(transcriptionCallback!!)
                Log.d(TAG, "Transcription callback unregistered successfully")
            }.onFailure { e ->
                Log.e(TAG, "Failed to unregister transcription callback", e)
            }
        }
        transcriptionCallback = null
    }

    /**
     * 注册配置提供者
     */
    fun registerConfigProvider(provider: IHybridSpeechConfigProvider) {
        configProvider = provider
        if (_connectionState.value) {
            runCatching {
                hybridSpeechService?.registerConfigProvider(provider)
                Log.d(TAG, "Config provider registered successfully")
            }.onFailure { e ->
                Log.e(TAG, "Failed to register config provider", e)
            }
        }
    }

    /**
     * 取消注册配置提供者
     */
    fun unregisterConfigProvider() {
        if (_connectionState.value) {
            runCatching {
                hybridSpeechService?.unregisterConfigProvider()
                Log.d(TAG, "Config provider unregistered successfully")
            }.onFailure { e ->
                Log.e(TAG, "Failed to unregister config provider", e)
            }
        }
        configProvider = null
    }

    /**
     * 创建录音进度监控流
     * @param intervalMs 进度回调间隔（毫秒）
     * @return 进度状态流
     */
    fun createProgressFlow(intervalMs: Int = DEFAULT_PROGRESS_INTERVAL): Flow<RecordingProgressState> =
        callbackFlow {
            val service = requireServiceConnected()

            val callback = object : IRecordProgressCallback.Stub() {
                override fun onRecordingStarted() {
                    trySend(RecordingProgressState.Started)
                }

                override fun onRecordingProgress(durationMs: Long) {
                    trySend(RecordingProgressState.Progress(durationMs))
                }

                override fun onRecordingStopped(totalDurationMs: Long) {
                    trySend(RecordingProgressState.Stopped(totalDurationMs))
                    close()
                }

                override fun onError(error: String?) {
                    close(RuntimeException(error ?: "Unknown progress error"))
                }
            }

            executeRemoteCall("registerProgressCallback") {
                service.registerProgressCallback(callback, intervalMs)
                Log.d(TAG, "Progress callback registered")
            }

            awaitClose {
                if (_connectionState.value) {
                    runCatching {
                        hybridSpeechService?.unregisterProgressCallback(callback)
                        Log.d(TAG, "Progress callback unregistered")
                    }.onFailure {
                        Log.w(TAG, "Failed to unregister progress callback", it)
                    }
                }
            }
        }

    /**
     * 获取当前录音时长
     */
    fun getCurrentDuration(): Long? {
        return if (_connectionState.value) {
            runCatching {
                hybridSpeechService?.getRecordingDuration()
            }.getOrElse { e ->
                Log.e(TAG, "Error getting current duration", e)
                null
            }
        } else {
            null
        }
    }

    /**
     * 获取当前状态
     */
    fun getCurrentStatus(): Int? {
        return if (_connectionState.value) {
            runCatching {
                hybridSpeechService?.getCurrentStatus()
            }.getOrElse { e ->
                Log.e(TAG, "Error getting current status", e)
                null
            }
        } else {
            null
        }
    }

    /**
     * 清理资源
     * 断开服务连接并清理所有资源
     */
    fun cleanup() {
        disconnect()
    }
}
