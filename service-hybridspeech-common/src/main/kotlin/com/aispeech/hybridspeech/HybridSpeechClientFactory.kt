package com.aispeech.hybridspeech

import android.content.Context

/**
 * 混合语音识别客户端工厂类
 * 
 * 提供便捷的客户端创建方法和预配置选项
 */
object HybridSpeechClientFactory {
    
    /**
     * 创建标准的混合语音识别客户端
     * 使用默认的服务包名和类名
     * 
     * @param context 应用上下文
     * @return 配置好的客户端实例
     */
    fun createStandardClient(context: Context): HybridSpeechClient {
        return HybridSpeechClient(context.applicationContext)
    }
    
    /**
     * 创建自定义服务的客户端
     * 
     * @param context 应用上下文
     * @param servicePackage 服务包名
     * @param serviceClass 服务类名（可选，默认为 {servicePackage}.HybridSpeechService）
     * @return 配置好的客户端实例
     */
    fun createCustomClient(
        context: Context,
        servicePackage: String,
        serviceClass: String? = null
    ): HybridSpeechClient {
        val actualServiceClass = serviceClass ?: "$servicePackage.HybridSpeechService"
        return HybridSpeechClient(
            context = context.applicationContext,
            servicePackage = servicePackage,
            serviceClass = actualServiceClass
        )
    }
    
    /**
     * 创建并自动连接的客户端
     * 
     * @param context 应用上下文
     * @param timeoutMs 连接超时时间（毫秒）
     * @return 已连接的客户端实例，如果连接失败则返回 null
     */
    suspend fun createAndConnect(
        context: Context,
        timeoutMs: Long = 5000
    ): HybridSpeechClient? {
        val client = createStandardClient(context)
        return if (client.connectAndWait(timeoutMs)) {
            client
        } else {
            client.cleanup()
            null
        }
    }
    
    /**
     * 创建带配置提供者的客户端
     * 
     * @param context 应用上下文
     * @param configProvider 配置提供者
     * @param autoConnect 是否自动连接
     * @param timeoutMs 连接超时时间（毫秒）
     * @return 配置好的客户端实例
     */
    suspend fun createWithConfigProvider(
        context: Context,
        configProvider: IHybridSpeechConfigProvider,
        autoConnect: Boolean = true,
        timeoutMs: Long = 5000
    ): HybridSpeechClient? {
        val client = createStandardClient(context)
        
        if (autoConnect) {
            if (client.connectAndWait(timeoutMs)) {
                client.registerConfigProvider(configProvider)
                return client
            } else {
                client.cleanup()
                return null
            }
        } else {
            client.registerConfigProvider(configProvider)
            return client
        }
    }
    
    /**
     * 创建带转写回调的客户端
     * 
     * @param context 应用上下文
     * @param transcriptionCallback 转写结果回调
     * @param autoConnect 是否自动连接
     * @param timeoutMs 连接超时时间（毫秒）
     * @return 配置好的客户端实例
     */
    suspend fun createWithTranscriptionCallback(
        context: Context,
        transcriptionCallback: ITranscriptionCallback,
        autoConnect: Boolean = true,
        timeoutMs: Long = 5000
    ): HybridSpeechClient? {
        val client = createStandardClient(context)
        
        if (autoConnect) {
            if (client.connectAndWait(timeoutMs)) {
                client.registerTranscriptionCallback(transcriptionCallback)
                return client
            } else {
                client.cleanup()
                return null
            }
        } else {
            client.registerTranscriptionCallback(transcriptionCallback)
            return client
        }
    }
    
    /**
     * 创建完整配置的客户端
     * 
     * @param context 应用上下文
     * @param configProvider 配置提供者（可选）
     * @param transcriptionCallback 转写结果回调（可选）
     * @param autoConnect 是否自动连接
     * @param timeoutMs 连接超时时间（毫秒）
     * @return 配置好的客户端实例
     */
    suspend fun createFullyConfigured(
        context: Context,
        configProvider: IHybridSpeechConfigProvider? = null,
        transcriptionCallback: ITranscriptionCallback? = null,
        autoConnect: Boolean = true,
        timeoutMs: Long = 5000
    ): HybridSpeechClient? {
        val client = createStandardClient(context)
        
        if (autoConnect) {
            if (client.connectAndWait(timeoutMs)) {
                configProvider?.let { client.registerConfigProvider(it) }
                transcriptionCallback?.let { client.registerTranscriptionCallback(it) }
                return client
            } else {
                client.cleanup()
                return null
            }
        } else {
            configProvider?.let { client.registerConfigProvider(it) }
            transcriptionCallback?.let { client.registerTranscriptionCallback(it) }
            return client
        }
    }
}
