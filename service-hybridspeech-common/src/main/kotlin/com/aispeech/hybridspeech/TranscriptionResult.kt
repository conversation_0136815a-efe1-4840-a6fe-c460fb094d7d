package com.aispeech.hybridspeech

import android.os.Parcel
import android.os.Parcelable
import androidx.compose.runtime.Immutable
import kotlinx.serialization.KSerializer
import kotlinx.serialization.Serializable
import kotlinx.serialization.SerializationException
import kotlinx.serialization.descriptors.SerialDescriptor
import kotlinx.serialization.descriptors.buildClassSerialDescriptor
import kotlinx.serialization.encodeToString
import kotlinx.serialization.encoding.Decoder
import kotlinx.serialization.encoding.Encoder
import kotlinx.serialization.json.Json
import kotlinx.serialization.json.JsonArray
import kotlinx.serialization.json.JsonDecoder
import kotlinx.serialization.json.JsonElement
import kotlinx.serialization.json.JsonEncoder
import kotlinx.serialization.json.JsonObject
import kotlinx.serialization.json.JsonPrimitive
import kotlinx.serialization.json.boolean
import kotlinx.serialization.json.buildJsonArray
import kotlinx.serialization.json.buildJsonObject
import kotlinx.serialization.json.intOrNull
import kotlinx.serialization.json.jsonArray
import kotlinx.serialization.json.jsonObject
import kotlinx.serialization.json.jsonPrimitive
import kotlinx.serialization.json.longOrNull
import kotlinx.serialization.json.put

/**
 * 统一转写结果模型（在线 + 离线）
 *
 * - 在线 JSON：始终包含 code & data
 * - 离线 JSON：缺 code，但仍可解析；序列化时会补齐 code，从而 **对外输出一致格式**
 *
 * @see TranscriptionResultSerializer
 */
@Immutable
@Serializable(with = TranscriptionResultSerializer::class)
sealed class TranscriptionResult : Parcelable {

  /** 协议码枚举，避免魔法数字/字符串 */
  @JvmInline
  @Serializable
  @Immutable
  value class ResultCode(val value: Int) {
    companion object {
      val START         = ResultCode(0)
      val PROCESSING    = ResultCode(6)
      val FINAL_TEXT    = ResultCode(9)
      val PROCESSING_ALT = ResultCode(8)
      val INITIALIZATION = ResultCode(11)
      val AGENDA        = ResultCode(12)

      fun of(v: Int) = when (v) {
        0  -> START
        6  -> PROCESSING
        8  -> PROCESSING_ALT
        9  -> FINAL_TEXT
        11 -> INITIALIZATION
        12 -> AGENDA
        else -> throw SerializationException("Unknown code: $v")
      }
    }
  }

  /** 内容类型枚举，标识原文还是译文 */
  @JvmInline
  @Serializable
  @Immutable
  value class ContentType(val value: String) {
    companion object {
      val ORIGINAL = ContentType("original")     // 原文
      val TRANSLATION = ContentType("translation") // 译文
    }
  }

  // ──────────────── 各具体结果 ────────────────

  @Immutable
  @Serializable
  data class StartResult(
    val sendAllSuccessCount: Int?,
    val sendAllSuccessLength: Int?,
    val finalTimestamp: Long?,
    val message: String = "success"
  ) : TranscriptionResult()

  /** 仅携带 var 占位符的中间结果（实时 / 离线均可能） */
  @Immutable
  @Serializable
  data class IntermediateResult(
    val begin: Long?,
    val role: Int,
    val `var`: String,
    val isOnline: Boolean = true
  ) : TranscriptionResult()

  /** 正在处理的文本结果，尚未最终确认 */
  @Immutable
  @Serializable
  data class ProcessingTextResult(
    val begin: Long,
    val end: Long,
    val role: Int,
    val text: String,
    val lineFeed: Int = 0,
    val isOnline: Boolean = true,
    val contentType: ContentType = ContentType.ORIGINAL
  ) : TranscriptionResult()

  /**
   * 初始化/批量结果
   * 用于处理 code=11，其 data 是一个数组的场景
   */
  @Immutable
  @Serializable
  data class InitializationResult(
    val results: List<ProcessingTextResult>
  ) : TranscriptionResult()

  /** 已确认的最终文本 */
  @Immutable
  @Serializable
  data class FinalTextResult(
    val begin: Long?,
    val end: Long?,
    val role: Int?,
    val text: String?,
    val contentType: ContentType = ContentType.ORIGINAL
  ) : TranscriptionResult() {

    val isMarker: Boolean
      get() = text == null || begin == null || end == null
  }

  /** 议程 / 章节级别的摘要结果 */
  @Immutable
  @Serializable
  data class AgendaResult(
    val begin: Long?,
    val end: Long?,
    val isLast: Boolean,
    val seq: Int,
    val status: Int,
    val text: String?,
    val timestamp: Long
  ) : TranscriptionResult()


  // ---------------- 手写 Parcelable -----------------

  final override fun writeToParcel(dest: Parcel, flags: Int) {
    dest.writeString(UnifiedJson.encodeToString(this))
  }

  final override fun describeContents(): Int = 0

  companion object {

    /** 必须加 @JvmField → 生成 public static final 字段，Lint 才满意 */
    @JvmField
    val CREATOR: Parcelable.Creator<TranscriptionResult> =
      object : Parcelable.Creator<TranscriptionResult> {

        override fun createFromParcel(parcel: Parcel): TranscriptionResult {
          val json = parcel.readString()
            ?: error("Empty parcel for TranscriptionResult")
          return UnifiedJson.decodeFromString(json)
        }

        override fun newArray(size: Int): Array<TranscriptionResult?> =
          arrayOfNulls(size)
      }
  }
}

/**
 * 自定义序列化器：
 * - 反序列化：兼容 **在线 + 离线** 两种输入格式
 * - 序列化：统一输出 **在线格式**（即顶层带 code / data）
 */
object TranscriptionResultSerializer : KSerializer<TranscriptionResult> {

  override val descriptor: SerialDescriptor =
    buildClassSerialDescriptor("TranscriptionResult")

  // region —— 序列化 —— -------------------------------------------------------------

  override fun serialize(encoder: Encoder, value: TranscriptionResult) {
    require(encoder is JsonEncoder)

    val element: JsonObject = when (value) {
      is TranscriptionResult.StartResult -> jsonObject(
        code = TranscriptionResult.ResultCode.START,
        data = buildJsonObject {
          put("sendAllSuccessCount", value.sendAllSuccessCount)
          put("sendAllSuccessLength", value.sendAllSuccessLength)
          put("finalTimestamp", value.finalTimestamp)
        },
        extra = buildJsonObject { put("message", value.message) }
      )

      is TranscriptionResult.IntermediateResult -> jsonObject(
        code = TranscriptionResult.ResultCode.PROCESSING,
        data = buildJsonObject {
          value.begin?.let { put("begin", it) }
          put("role", value.role)
          put("var", value.`var`)
        },
        extra = if (!value.isOnline) buildJsonObject { put("isOnline", false) } else null
      )

      is TranscriptionResult.ProcessingTextResult -> {
        // 统一使用 PROCESSING，不再根据 contentType 区分 code
        jsonObject(
          code = TranscriptionResult.ResultCode.PROCESSING,
          data = buildJsonObject {
            put("begin", value.begin)
            put("end", value.end)
            put("role", value.role)
            put("text", value.text)
            put("lineFeed", value.lineFeed)
            put("contentType", value.contentType.value)
          },
          extra = if (!value.isOnline) buildJsonObject { put("isOnline", false) } else null
        )
      }

      is TranscriptionResult.FinalTextResult -> {
        val dataObject = if (!value.isMarker) {
          buildJsonObject {
            put("begin", value.begin)
            put("end", value.end)
            put("role", value.role)
            put("text", value.text)
            put("contentType", value.contentType.value)
          }
        } else {
          buildJsonObject {}
        }
        jsonObject(
          code = TranscriptionResult.ResultCode.FINAL_TEXT,
          data = dataObject
        )
      }

      is TranscriptionResult.InitializationResult -> buildJsonObject {
        put("code", TranscriptionResult.ResultCode.INITIALIZATION.value)
        put("data", buildJsonArray {
          value.results.forEach { result ->
            add(serializeProcessingItemToJson(result))
          }
        })
      }

      is TranscriptionResult.AgendaResult -> jsonObject(
        code = TranscriptionResult.ResultCode.AGENDA,
        data = buildJsonObject {
          value.begin?.let { put("begin", it) }
          value.end?.let { put("end", it) }
          put("isLast", value.isLast)
          put("seq", value.seq)
          put("status", value.status)
          put("text", value.text)
          put("timestamp", value.timestamp)
        }
      )
    }

    encoder.encodeJsonElement(element)
  }

  // region —— 反序列化 —— -----------------------------------------------------------

  override fun deserialize(decoder: Decoder): TranscriptionResult {
    require(decoder is JsonDecoder)
    val root = decoder.decodeJsonElement().jsonObject

    // ── 在线格式：顶层有 code ──
    val codeElement = root["code"]?.jsonPrimitive
    val codeInt = codeElement?.intOrNull ?: codeElement?.content?.toIntOrNull()

    codeInt?.let { codeValue ->
      val dataElement = root["data"]
        ?: throw SerializationException("Missing 'data' when 'code' present")
      val code = TranscriptionResult.ResultCode.of(codeValue)

      return when (code) {
        TranscriptionResult.ResultCode.START -> {
          val data = dataElement.jsonObject
          TranscriptionResult.StartResult(
            sendAllSuccessCount = data["sendAllSuccessCount"]?.intOrZero(),
            sendAllSuccessLength = data["sendAllSuccessLength"]?.intOrZero(),
            finalTimestamp = data["finalTimestamp"]?.longOrZero(),
            message = root["message"]?.jsonPrimitive?.content ?: "success"
          )
        }

        TranscriptionResult.ResultCode.PROCESSING,
        TranscriptionResult.ResultCode.PROCESSING_ALT -> {
          val data = dataElement.jsonObject
          // 先检查是否包含 trans 属性，如果有就按翻译处理
          if ("trans" in data) {
            val trans = data["trans"]?.jsonObject ?: data
            parseProcessing(trans, TranscriptionResult.ContentType.TRANSLATION, role = 0, lineFeed = 0)
          } else if ("end" in data && "text" in data) {
            parseProcessing(data,
              contentType = data["contentType"]?.jsonPrimitive?.content?.let { TranscriptionResult.ContentType(it) }
                ?: TranscriptionResult.ContentType.ORIGINAL,
              role = data["role"]?.intOrZero(),
              lineFeed = data["lineFeed"]?.intOrZero(),
              isOnline = root["isOnline"]?.jsonPrimitive?.boolean ?: true
            )
          } else {
            TranscriptionResult.IntermediateResult(
              begin = data["begin"]?.jsonPrimitive?.longOrNull,
              role = data["role"]?.intOrZero() ?: 0,
              `var` = data["var"]?.jsonPrimitive?.content ?: "",
              isOnline = root["isOnline"]?.jsonPrimitive?.boolean ?: true
            )
          }
        }

        TranscriptionResult.ResultCode.FINAL_TEXT -> {
          val data = dataElement.jsonObject
          TranscriptionResult.FinalTextResult(
            begin = data["begin"]?.jsonPrimitive?.longOrNull,
            end = data["end"]?.jsonPrimitive?.longOrNull,
            role = data["role"]?.jsonPrimitive?.intOrNull,
            text = data["text"]?.jsonPrimitive?.content,
            contentType = data["contentType"]?.jsonPrimitive?.content?.let {
              TranscriptionResult.ContentType(it)
            } ?: TranscriptionResult.ContentType.ORIGINAL
          )
        }

        TranscriptionResult.ResultCode.AGENDA -> {
          val data = dataElement.jsonObject
          TranscriptionResult.AgendaResult(
            begin = data["begin"]?.jsonPrimitive?.longOrNull,
            end = data["end"]?.jsonPrimitive?.longOrNull,
            isLast = data["isLast"]!!.jsonPrimitive.boolean,
            seq = data["seq"]!!.intOrZero(),
            status = data["status"]!!.intOrZero(),
            text = data["text"]?.jsonPrimitive?.content,
            timestamp = data["timestamp"]!!.longOrZero()
          )
        }

        TranscriptionResult.ResultCode.INITIALIZATION -> {
          val dataArray = dataElement.jsonArray
          val results = dataArray.map { item ->
            val itemObject = item.jsonObject
            if ("trans" in itemObject) {
              val transObject = itemObject["trans"]!!.jsonObject
              parseProcessing(
                data = transObject,
                contentType = TranscriptionResult.ContentType.TRANSLATION,
                role = itemObject["role"]?.intOrZero() ?: 0,
                isOnline = true
              )
            } else { // 否则为原文结果
              parseProcessing(
                data = itemObject,
                contentType = TranscriptionResult.ContentType.ORIGINAL,
                role = itemObject["role"]?.intOrZero(),
                isOnline = true
              )
            }
          }
          TranscriptionResult.InitializationResult(results)
        }

        else -> throw SerializationException("Unknown code: $code")
      }
    }

    // ── 离线格式：无 code ──
    return parseOffline(root)
  }

  /**
   * 离线 JSON → 统一结果
   */
  private fun parseOffline(root: JsonObject): TranscriptionResult {
    val rec = root["rec"]?.jsonPrimitive?.content.orEmpty()
    val recWords = root["rec_words"]?.jsonArray ?: JsonArray(emptyList())
    val varText = root["var"]?.jsonPrimitive?.content.orEmpty()
    val varBegin = root["var_begin"]?.longOrZero() ?: 0L

    // 没有真实文本 -> Intermediate
    if (rec.isBlank() && recWords.isEmpty()) {
      return TranscriptionResult.IntermediateResult(
        begin = varBegin,
        role = 0,
        `var` = varText,
        isOnline = false
      )
    }

    // 有 rec / rec_words -> Processing
    val begin = recWords.firstOrNull()?.jsonObject?.get("start")?.longOrZero() ?: varBegin
    val end = recWords.lastOrNull()?.jsonObject?.get("end")?.longOrZero() ?: varBegin

    return TranscriptionResult.ProcessingTextResult(
      begin = begin,
      end = end,
      role = 0,
      text = rec,
      isOnline = false
    )
  }

  private fun serializeProcessingItemToJson(result: TranscriptionResult.ProcessingTextResult): JsonObject {
    return if (result.contentType == TranscriptionResult.ContentType.TRANSLATION) {
      buildJsonObject {
        put("trans", buildJsonObject {
          put("begin", result.begin)
          put("end", result.end)
          put("text", result.text)
          put("var", "")
        })
      }
    } else {
      buildJsonObject {
        put("begin", result.begin)
        put("end", result.end)
        put("role", result.role)
        put("text", result.text)
      }
    }
  }


  /**
   * 转换最终态
   * 原文/翻译共用
   */
  private fun parseProcessing(
    data: JsonObject,
    contentType: TranscriptionResult.ContentType = TranscriptionResult.ContentType.ORIGINAL,
    role: Int? = null,
    lineFeed: Int? = null,
    isOnline: Boolean = true
  ): TranscriptionResult.ProcessingTextResult {
    return TranscriptionResult.ProcessingTextResult(
      begin = data["begin"]!!.jsonPrimitive.longOrZero(),
      end = data["end"]!!.longOrZero(),
      role = role ?: data["role"]?.intOrZero() ?: 0,
      text = data["text"]!!.jsonPrimitive.content,
      lineFeed = lineFeed ?: data["lineFeed"]?.intOrZero() ?: 0,
      isOnline = isOnline,
      contentType = contentType
    )
  }


  // region —— 私有工具 —— -------------------------------------------------------------

  /** 构建统一输出 JSON：code + data (+ extra) */
  private fun jsonObject(
    code: TranscriptionResult.ResultCode,
    data: JsonObject,
    extra: JsonObject? = null
  ) = buildJsonObject {
    put("code", code.value)
    put("data", data)
    extra?.forEach { (k, v) -> put(k, v) }
  }

  private fun JsonElement?.intOrZero(): Int =
    (this as? JsonPrimitive)?.intOrNull ?: 0

  private fun JsonElement?.longOrZero(): Long =
    (this as? JsonPrimitive)?.longOrNull ?: 0L
}

// ────────────────── 全局统一 Json 配置（可复用） ──────────────────
val UnifiedJson = Json {
  ignoreUnknownKeys = true
  encodeDefaults = false   // 减少包体
  explicitNulls = false
}


/**
 * 服务状态枚举
 */
object ServiceStatus {
  const val IDLE = 0
  const val RECORDING = 1
  const val PROCESSING = 2
  const val ERROR = 3
  const val INITIALIZING = 4
  const val PAUSED = 5
}