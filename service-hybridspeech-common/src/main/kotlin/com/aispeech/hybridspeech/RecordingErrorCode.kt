package com.aispeech.hybridspeech

/**
 * 停止录音错误代码定义
 */
object StopRecordingErrorCode {
  /** 成功 */
  const val SUCCESS = 0

  /** 当前状态不允许停止录音 */
  const val INVALID_STATE = 1001

  /** 音频采集停止失败 */
  const val AUDIO_CAPTURE_STOP_FAILED = 1002

  /** 存储管理器停止失败 */
  const val STORAGE_MANAGER_STOP_FAILED = 1003

  /** ASR 处理停止失败 */
  const val ASR_STOP_FAILED = 1004

  /** 转写分发器停止失败 */
  const val TRANSCRIPTION_DISPATCHER_STOP_FAILED = 1005

  /** 文件处理失败 */
  const val FILE_PROCESSING_FAILED = 1006

  /** 超时错误 */
  const val TIMEOUT = 1007

  /** 未知错误 */
  const val UNKNOWN_ERROR = 1999

  /**
   * 获取错误代码对应的描述
   */
  fun getErrorMessage(errorCode: Int): String {
    return when (errorCode) {
      SUCCESS -> "成功"
      INVALID_STATE -> "当前状态不允许停止录音"
      AUDIO_CAPTURE_STOP_FAILED -> "音频采集停止失败"
      STORAGE_MANAGER_STOP_FAILED -> "存储管理器停止失败"
      ASR_STOP_FAILED -> "ASR 处理停止失败"
      TRANSCRIPTION_DISPATCHER_STOP_FAILED -> "转写分发器停止失败"
      FILE_PROCESSING_FAILED -> "文件处理失败"
      TIMEOUT -> "操作超时"
      UNKNOWN_ERROR -> "未知错误"
      else -> "未定义的错误代码: $errorCode"
    }
  }
}

/**
 * 启动录音错误代码定义
 */
object StartRecordingErrorCode {
  /** 成功 */
  const val SUCCESS = 0

  /** 当前状态不允许启动录音 */
  const val INVALID_STATE = 2001

  /** 音频采集启动失败 */
  const val AUDIO_CAPTURE_START_FAILED = 2002

  /** 存储管理器启动失败 */
  const val STORAGE_MANAGER_START_FAILED = 2003

  /** 在线ASR启动失败 */
  const val ONLINE_ASR_START_FAILED = 2004

  /** 离线ASR启动失败 */
  const val OFFLINE_ASR_START_FAILED = 2005

  /** 转写分发器启动失败 */
  const val TRANSCRIPTION_DISPATCHER_START_FAILED = 2006

  /** 离线引擎初始化失败 */
  const val OFFLINE_ENGINE_INIT_FAILED = 2007

  /** 配置参数无效 */
  const val INVALID_CONFIG = 2008

  /** 权限不足 */
  const val PERMISSION_DENIED = 2009

  /** 网络连接失败 */
  const val NETWORK_ERROR = 2010

  /** 存储空间不足 */
  const val STORAGE_FULL = 2011

  /** 配置提供者错误 */
  const val CONFIG_PROVIDER_ERROR = 2013

  /** 超时错误 */
  const val TIMEOUT = 2012

  /** 未知错误 */
  const val UNKNOWN_ERROR = 2999

  /**
   * 获取错误代码对应的描述
   */
  fun getErrorMessage(errorCode: Int): String {
    return when (errorCode) {
      SUCCESS -> "成功"
      INVALID_STATE -> "当前状态不允许启动录音"
      AUDIO_CAPTURE_START_FAILED -> "音频采集启动失败"
      STORAGE_MANAGER_START_FAILED -> "存储管理器启动失败"
      ONLINE_ASR_START_FAILED -> "在线ASR启动失败"
      OFFLINE_ASR_START_FAILED -> "离线ASR启动失败"
      TRANSCRIPTION_DISPATCHER_START_FAILED -> "转写分发器启动失败"
      OFFLINE_ENGINE_INIT_FAILED -> "离线引擎初始化失败"
      INVALID_CONFIG -> "配置参数无效"
      PERMISSION_DENIED -> "权限不足"
      NETWORK_ERROR -> "网络连接失败"
      STORAGE_FULL -> "存储空间不足"
      TIMEOUT -> "操作超时"
      UNKNOWN_ERROR -> "未知错误"
      else -> "未定义的错误代码: $errorCode"
    }
  }
}


/*
* 暂停录音错误代码定义
*/
object PauseRecordingErrorCode {
  /** 成功 */
  const val SUCCESS = 0

  /** 当前状态不允许暂停录音 */
  const val INVALID_STATE = 3001

  /** 音频采集暂停失败 */
  const val AUDIO_CAPTURE_PAUSE_FAILED = 3002

  /** 存储管理器暂停失败 */
  const val STORAGE_MANAGER_PAUSE_FAILED = 3003

  /** ASR 处理暂停失败 */
  const val ASR_PAUSE_FAILED = 3004

  /** 转写分发器暂停失败 */
  const val TRANSCRIPTION_DISPATCHER_PAUSE_FAILED = 3005

  /** 超时错误 */
  const val TIMEOUT = 3006

  /** 未知错误 */
  const val UNKNOWN_ERROR = 3999

  /**
   * 获取错误代码对应的描述
   */
  fun getErrorMessage(errorCode: Int): String {
    return when (errorCode) {
      SUCCESS -> "成功"
      INVALID_STATE -> "当前状态不允许暂停录音"
      AUDIO_CAPTURE_PAUSE_FAILED -> "音频采集暂停失败"
      STORAGE_MANAGER_PAUSE_FAILED -> "存储管理器暂停失败"
      ASR_PAUSE_FAILED -> "ASR 处理暂停失败"
      TRANSCRIPTION_DISPATCHER_PAUSE_FAILED -> "转写分发器暂停失败"
      TIMEOUT -> "操作超时"
      UNKNOWN_ERROR -> "未知错误"
      else -> "未定义的错误代码: $errorCode"
    }
  }
}

/**
 * 继续录音错误代码定义
 */
object ResumeRecordingErrorCode {
  /** 成功 */
  const val SUCCESS = 0

  /** 当前状态不允许继续录音 */
  const val INVALID_STATE = 4001

  /** 音频采集继续失败 */
  const val AUDIO_CAPTURE_RESUME_FAILED = 4002

  /** 存储管理器继续失败 */
  const val STORAGE_MANAGER_RESUME_FAILED = 4003

  /** ASR 处理继续失败 */
  const val ASR_RESUME_FAILED = 4004

  /** 转写分发器继续失败 */
  const val TRANSCRIPTION_DISPATCHER_RESUME_FAILED = 4005

  /** 超时错误 */
  const val TIMEOUT = 4006

  /** 未知错误 */
  const val UNKNOWN_ERROR = 4999

  /**
   * 获取错误代码对应的描述
   */
  fun getErrorMessage(errorCode: Int): String {
    return when (errorCode) {
      SUCCESS -> "成功"
      INVALID_STATE -> "当前状态不允许继续录音"
      AUDIO_CAPTURE_RESUME_FAILED -> "音频采集继续失败"
      STORAGE_MANAGER_RESUME_FAILED -> "存储管理器继续失败"
      ASR_RESUME_FAILED -> "ASR 处理继续失败"
      TRANSCRIPTION_DISPATCHER_RESUME_FAILED -> "转写分发器继续失败"
      TIMEOUT -> "操作超时"
      UNKNOWN_ERROR -> "未知错误"
      else -> "未定义的错误代码: $errorCode"
    }
  }
}
