package com.aispeech.hybridspeech;

// 录音进度回调接口
interface IRecordProgressCallback {
    /**
     * 录音时长更新回调
     * @param durationMs 录音时长（毫秒）
     */
    void onRecordingProgress(long durationMs);

    /**
     * 录音开始回调
     */
    void onRecordingStarted();

    /**
     * 录音停止回调
     * @param totalDurationMs 总录音时长（毫秒）
     */
    void onRecordingStopped(long totalDurationMs);

    /**
     * 错误回调
     * @param error 错误信息
     */
    void onError(String error);
}