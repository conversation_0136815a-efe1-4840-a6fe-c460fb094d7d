package com.aispeech.hybridspeech;

import com.aispeech.hybridspeech.ITranscriptionCallback;
import com.aispeech.hybridspeech.IRecordProgressCallback;
import com.aispeech.hybridspeech.IStopRecordingCallback;
import com.aispeech.hybridspeech.IStartRecordingCallback;
import com.aispeech.hybridspeech.IPauseRecordingCallback;
import com.aispeech.hybridspeech.IResumeRecordingCallback;
import com.aispeech.hybridspeech.IHybridSpeechConfigProvider;
import com.aispeech.hybridspeech.RecordingConfig;
import com.aispeech.hybridspeech.RecordingResultInfo;


// 混合语音识别服务主接口
interface IHybridSpeechService {
    // 异步开始录音和转写
    void startRecordingWithConfigAsync(in RecordingConfig config, IStartRecordingCallback callback);

    // 异步暂停录音
    void pauseRecordingAsync(IPauseRecordingCallback callback);

    // 异步继续录音
    void resumeRecordingAsync(IResumeRecordingCallback callback);

    // 异步停止录音并通过回调返回结果
    void stopRecordingWithResultAsync(IStopRecordingCallback callback);

    // 注册转写结果回调
    void registerCallback(ITranscriptionCallback callback);

    // 取消注册回调
    void unregisterCallback(ITranscriptionCallback callback);

    // 获取当前状态
    int getCurrentStatus();

    /**
     * 获取当前录音时长
     * @return 录音时长（毫秒）
     */
    long getRecordingDuration();

    /**
     * 注册录音进度回调
     * @param callback 进度回调接口
     * @param intervalMs 回调间隔（毫秒），建议100-1000ms之间
     */
    void registerProgressCallback(IRecordProgressCallback callback, int intervalMs);

    /**
     * 取消注册录音进度回调
     * @param callback 要取消的回调接口
     */
    void unregisterProgressCallback(IRecordProgressCallback callback);

    /**
     * 取消所有录音进度回调
     */
    void unregisterAllProgressCallbacks();


    // ============ 配置提供者相关方法 ============

    /**
     * 注册配置提供者
     * @param provider 配置提供者接口，客户端实现
     */
    void registerConfigProvider(IHybridSpeechConfigProvider provider);

    /**
     * 取消注册配置提供者
     */
    void unregisterConfigProvider();

    // ============ 基于配置提供者的新方法 ============

    /**
     * 使用配置提供者启动在线录音
     * 服务端会通过配置提供者获取已签名的WebSocket URL和续传配置
     * @param request 在线录音请求参数
     * @param callback 启动录音回调
     */
    void startRecordingWithProvider(in RecordingConfig config, IStartRecordingCallback callback);
}
