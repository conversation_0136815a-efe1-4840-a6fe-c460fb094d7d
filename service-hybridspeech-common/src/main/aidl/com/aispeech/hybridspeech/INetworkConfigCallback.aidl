package com.aispeech.hybridspeech;

import com.aispeech.hybridspeech.NetworkConfig;

/**
 * 统一网络配置回调接口
 * 用于客户端向服务端返回网络配置（包含WebSocket和续传信息）
 */
interface INetworkConfigCallback {
    /**
     * 配置准备就绪
     * @param config 网络配置（包含已签名的WebSocket URL和续传配置）
     */
    void onConfigReady(in NetworkConfig config);
    
    /**
     * 配置获取失败
     * @param errorCode 错误代码
     * @param errorMessage 错误信息
     */
    void onConfigError(int errorCode, String errorMessage);
} 