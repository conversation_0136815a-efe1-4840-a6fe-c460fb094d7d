package com.aispeech.hybridspeech;

import com.aispeech.hybridspeech.IRecordingSession;
import com.aispeech.hybridspeech.RecordingResultInfo;
import com.aispeech.hybridspeech.TranscriptionResult;

/**
 * 统一的录音会话事件回调接口。
 * 客户端实现此接口以接收来自服务端的异步通知。
 * 使用 oneway 关键字确保调用是非阻塞的，不会挂起服务端线程。
 */
oneway interface IRecordingSessionCallback {
    /**
     * 当会话成功创建并开始时调用。
     * 这是整个流程的起点，客户端在此回调中获取到 IRecordingSession 的代理对象。
     * @param session IRecordingSession 的代理对象，客户端之后通过它来控制会话。
     */
    void onSessionCreated(IRecordingSession session);

    /**
     * 当会话创建失败时调用。
     * @param errorMessage 失败原因的描述。
     */
    void onSessionCreateFailed(String errorMessage);

    /**
     * 当录音成功暂停时调用。
     */
    void onPaused();

    /**
     * 当录音成功恢复时调用。
     */
    void onResumed();

    /**
     * 当会话停止时调用。这标志着会话的生命周期结束。
     * @param finalResult 最终的录音和转写结果。如果出错，可能为 null。
     */
    void onStopped(in RecordingResultInfo finalResult);

    /**
     * 实时转写结果回调。
     * 在录音过程中，服务端可能会多次调用此方法返回中间识别结果。
     * @param partialResult 部分或中间的转写结果。
     */
    void onTranscriptionUpdate(in TranscriptionResult partialResult);

    /**
     * 录音进度回调。
     * @param durationMs 当前录音时长（毫秒）。
     */
    void onProgressUpdate(long durationMs);

    /**
     * 当会话过程中发生错误时调用。
     * @param errorCode 错误码。
     * @param errorMessage 错误信息。
     */
    void onError(int errorCode, String errorMessage);
}
