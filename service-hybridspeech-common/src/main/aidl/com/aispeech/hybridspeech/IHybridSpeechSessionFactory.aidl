package com.aispeech.hybridspeech;

import com.aispeech.hybridspeech.RecordingConfig;
import com.aispeech.hybridspeech.IRecordingSession;
import com.aispeech.hybridspeech.IRecordingSessionCallback;
import com.aispeech.hybridspeech.IHybridSpeechConfigProvider;

/**
 * 混合语音会话工厂接口。
 * 职责：创建录音会话 (IRecordingSession)。
 */
interface IHybridSpeechSessionFactory {
    /**
     * 创建一个新的录音会话并立即开始录音。
     * 服务端会通过回调将 IRecordingSession 对象返回给客户端。
     * @param config 录音配置
     * @param callback 统一的会话事件回调
     */
    void createRecordingSession(in RecordingConfig config, IRecordingSessionCallback callback);

    /**
     * (可选) 注册一个全局的配置提供者，所有通过此工厂创建的会话都可能使用它。
     * @param provider 配置提供者接口
     */
    void registerConfigProvider(IHybridSpeechConfigProvider provider);

    /**
     * (可选) 取消注册全局配置提供者。
     */
    void unregisterConfigProvider();
}
