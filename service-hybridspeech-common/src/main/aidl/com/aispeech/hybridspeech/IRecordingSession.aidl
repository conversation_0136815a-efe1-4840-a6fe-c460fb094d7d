package com.aispeech.hybridspeech;

/**
 * 代表一次录音会话的接口。
 * 客户端通过这个对象的代理来控制一次具体的录音过程。
 * 这个对象由 IHybridSpeechSessionFactory 创建并返回给客户端。
 */
interface IRecordingSession {
    /**
     * 暂停当前会话的录音。
     * 操作结果会通过 IRecordingSessionCallback.onPaused() 异步通知。
     */
    void pause();

    /**
     * 继续当前会话的录音。
     * 操作结果会通过 IRecordingSessionCallback.onResumed() 异步通知。
     */
    void resume();

    /**
     * 停止当前会话的录音和转写。
     * 这是一个终结操作，会话停止后不能再继续。
     * 最终结果会通过 IRecordingSessionCallback.onStopped() 返回。
     */
    void stop();

    /**
     * 获取当前会话的状态。
     * （例如：IDLE, RECORDING, PAUSED, STOPPED）。
     * @return 一个代表状态的整型值。
     */
    int getStatus();

    /**
     * 获取当前会话已经录制的时长（毫秒）。
     * @return 录音时长（毫秒）。
     */
    long getRecordingDuration();

    /**
     * 释放会话。客户端在确认不再需要此会话时（例如，Activity/Fragment销毁时）调用，
     * 以便服务端可以立即清理与此会话相关的所有资源，防止内存泄漏。
     */
    void release();
}
