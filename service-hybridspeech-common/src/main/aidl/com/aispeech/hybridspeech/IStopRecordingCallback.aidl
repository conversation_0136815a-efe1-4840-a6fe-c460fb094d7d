package com.aispeech.hybridspeech;

import com.aispeech.hybridspeech.RecordingResultInfo;

// 异步停止录音回调接口
interface IStopRecordingCallback {
    /**
     * 停止录音成功回调
     * @param result 录音结果信息
     */
    void onStopRecordingSuccess(in RecordingResultInfo result);

    /**
     * 停止录音失败回调
     * @param errorCode 错误代码
     * @param errorMessage 错误信息
     */
    void onStopRecordingError(int errorCode, String errorMessage);
}
