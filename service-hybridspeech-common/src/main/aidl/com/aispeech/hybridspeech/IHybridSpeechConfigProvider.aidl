package com.aispeech.hybridspeech;

import com.aispeech.hybridspeech.NetworkConfigRequest;
import com.aispeech.hybridspeech.INetworkConfigCallback;

/**
 * 混合语音服务配置提供者接口
 * 客户端实现此接口，为服务端提供各种网络请求的配置（包括已签名的URL）
 */
interface IHybridSpeechConfigProvider {
    /**
     * 服务端请求网络配置（包含WebSocket和续传信息）
     * 统一接口，同时返回WebSocket配置和可能的续传配置
     * @param request 网络配置请求参数
     * @param callback 配置回调接口
     */
    void requestNetworkConfig(in NetworkConfigRequest request, INetworkConfigCallback callback);
} 