package com.aispeech.hybridspeech

import android.content.Context
import kotlinx.coroutines.test.runTest
import org.junit.Test
import org.junit.Before
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import org.mockito.kotlin.whenever
import kotlin.test.assertNotNull
import kotlin.test.assertFalse

/**
 * HybridSpeechClient 单元测试
 */
class HybridSpeechClientTest {

    @Mock
    private lateinit var mockContext: Context

    private lateinit var client: HybridSpeechClient

    @Before
    fun setUp() {
        MockitoAnnotations.openMocks(this)
        
        // 模拟 Context
        whenever(mockContext.packageName).thenReturn("com.test.app")
        
        client = HybridSpeechClient(mockContext)
    }

    @Test
    fun `test client creation`() {
        assertNotNull(client)
        assertFalse(client.connectionState.value)
    }

    @Test
    fun `test connection state flow`() = runTest {
        // 初始状态应该是未连接
        assertFalse(client.connectionState.value)
    }

    @Test
    fun `test client cleanup`() {
        // 测试清理方法不会抛出异常
        client.cleanup()
        assertFalse(client.connectionState.value)
    }
}
